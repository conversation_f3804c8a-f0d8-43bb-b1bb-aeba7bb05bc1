import { dtManualActionEvent, FE_LOG_PREFIX, convertStringValue } from "app/services/firebase/analytics"
import React, { useImperativeHandle, useRef, useEffect } from "react"
import { Dimensions, StyleSheet } from "react-native"
import {
  Camera,
  CameraRuntimeError,
  CodeType,
  PhotoFile,
  TakePhotoOptions,
  useCameraDevice,
  useCameraFormat,
  useCameraPermission,
  useCodeScanner,
} from "react-native-vision-camera"

const { width: screenWidth, height: screenHeight } = Dimensions.get('screen')

// Type for MyCamera ref
export interface MyCameraRef {
  takePicture: (options?: TakePhotoOptions) => Promise<PhotoFile>
  requestPermission: () => Promise<boolean>
  hasPermission: boolean
  isCameraReady: boolean
}

interface MyCameraProps {
  photo?: boolean
  video?: boolean
  photoRatio?: number
  flashMode?: boolean
  isActive?: boolean
  isCodeScanned?: boolean
  onCodeScanned?: (codes: any, frame: any) => void
  onCodeScannedTypes?: CodeType[]
}

const MyCamera = (props: MyCameraProps, ref: any) => {
  const {
    photo: isPhoto = true,
    video: isVideo = false,
    photoRatio = screenHeight / screenWidth,
    flashMode = false,
    isActive = true,
    isCodeScanned = false,
    onCodeScanned = () => {},
    onCodeScannedTypes = ['pdf-417'],
  } = props
  const cameraRef = useRef<Camera>(null)
  const [isCameraReady, setCameraReady] = React.useState(false)
  const { hasPermission, requestPermission } = useCameraPermission()
  const device = useCameraDevice("back")
  const format = useCameraFormat(device, [{ photoAspectRatio: photoRatio }])
  const codeScanner = useCodeScanner({
    codeTypes: onCodeScannedTypes,
    onCodeScanned,
  })

  const onInitialized = () => {
    setCameraReady(true)
  }

  const onError = (error: CameraRuntimeError) => {
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}Camera_error`)
    dtAction.reportStringValue('code', `${error.code}`)
    dtAction.reportStringValue('message', `${convertStringValue(error?.message)}`)
    dtAction.leaveAction()
    console.error("MyCamera camera error", {
      code: error.code,
      message: error?.message,
    })
  }

  const takePicture = (options: TakePhotoOptions = {}) => {
    return cameraRef?.current?.takePhoto?.({
      flash: flashMode ? "on" : "off",
      ...options,
    })
  }

  // Expose functions to parent
  useImperativeHandle(ref, () => ({
    takePicture,
    requestPermission,
    hasPermission,
    isCameraReady,
  }))

  return !!device ? (
    <Camera
      ref={cameraRef}
      format={format}
      isActive={isActive}
      device={device}
      photo={isPhoto}
      video={isVideo}
      outputOrientation="preview"
      style={StyleSheet.absoluteFill}
      torch={flashMode ? "on" : "off"}
      onError={onError}
      onInitialized={onInitialized}
      codeScanner={!!isCodeScanned ? codeScanner : null}
    />
  ) : (
    <></>
  )
}

export default React.forwardRef(MyCamera)
