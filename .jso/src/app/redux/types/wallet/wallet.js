  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.WalletQuantity = exports.WalletMyTravel = exports.WalletMyPlayPass = exports.WalletDetailMyPerks = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _forEach = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _sortBy = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[12]);
  var _enum = _$$_REQUIRE(_dependencyMap[13]);
  var _utils = _$$_REQUIRE(_dependencyMap[14]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var WalletMyTravelClass = /*#__PURE__*/(0, _createClass2.default)(function WalletMyTravelClass() {
    (0, _classCallCheck2.default)(this, WalletMyTravelClass);
  });
  var WalletMyTravel = exports.WalletMyTravel = /*#__PURE__*/function (_WalletMyTravelClass) {
    function WalletMyTravel() {
      (0, _classCallCheck2.default)(this, WalletMyTravel);
      return _callSuper(this, WalletMyTravel, arguments);
    }
    (0, _inherits2.default)(WalletMyTravel, _WalletMyTravelClass);
    return (0, _createClass2.default)(WalletMyTravel, [{
      key: "success",
      value: function success(action) {
        return this.formatData(action);
      }
    }, {
      key: "sortFlightByDate",
      value: function sortFlightByDate(savedFlights) {
        var _dataFlight;
        var dataFlight = savedFlights == null ? undefined : savedFlights.filter(function (e) {
          return e == null ? undefined : e.needShowInSavedFlight;
        });
        dataFlight = (_dataFlight = dataFlight) == null ? undefined : _dataFlight.map(function (e) {
          return Object.assign({}, e, {
            timeStamp: (0, _dateTime.convertDateTimeToTimeStampSingapore)(`${e == null ? undefined : e.scheduledDate} ${e == null ? undefined : e.scheduledTime}`)
          });
        });
        return (0, _sortBy.default)(dataFlight, ["timeStamp"]);
      }
    }, {
      key: "handleTitleForSection",
      value: function handleTitleForSection(flightData) {
        var today = (0, _moment.default)();
        var tomorrow = (0, _moment.default)().add(1, "day");
        var flightDate = (0, _moment.default)(flightData == null ? undefined : flightData.scheduledDate);
        if ((0, _moment.default)(tomorrow).isSame(flightDate.format(_dateTime.DateFormats.YearMonthDay), "day")) {
          return (0, _i18n.translate)("upcomingEvent.tomorrow");
        } else if ((0, _moment.default)(today).isSame(flightDate.format(_dateTime.DateFormats.YearMonthDay), "day")) {
          return (0, _i18n.translate)("upcomingEvent.today");
        } else {
          return `${flightDate.locale("en").format(_dateTime.DateFormats.DayDateMonthYear)}`;
        }
      }
    }, {
      key: "formatData",
      value: function formatData(action) {
        var arr = [];
        var dataFlight = this.sortFlightByDate(action == null ? undefined : action.savedFlights);
        var upcomingFlag = false;
        for (var i = 0; i < dataFlight.length; i++) {
          var currentFlight = dataFlight[i];
          var previousFlight = dataFlight[i - 1];
          var title = undefined;
          if (upcomingFlag) {
            title = null;
          } else {
            if (i === 0) {
              title = this.handleTitleForSection(currentFlight);
            } else {
              if ((currentFlight == null ? undefined : currentFlight.scheduledDate) !== (previousFlight == null ? undefined : previousFlight.scheduledDate)) {
                title = "UPCOMING";
              } else {
                title = null;
              }
            }

            // Check if the current title is "UPCOMING" and set the flag accordingly
            upcomingFlag = title === "UPCOMING";
          }
          arr.push({
            title: title,
            date: currentFlight == null ? undefined : currentFlight.scheduledDate,
            time: currentFlight == null ? undefined : currentFlight.scheduledTime,
            dataFlight: currentFlight
          });
        }
        return arr;
      }
    }]);
  }(WalletMyTravelClass);
  var WalletMyPlayPass = exports.WalletMyPlayPass = /*#__PURE__*/function (_WalletMyTravelClass2) {
    function WalletMyPlayPass() {
      (0, _classCallCheck2.default)(this, WalletMyPlayPass);
      return _callSuper(this, WalletMyPlayPass, arguments);
    }
    (0, _inherits2.default)(WalletMyPlayPass, _WalletMyTravelClass2);
    return (0, _createClass2.default)(WalletMyPlayPass, [{
      key: "mergePass",
      value: function mergePass(passes, newPass) {
        var existedPasses = passes == null || passes.find == null ? undefined : passes.find(function (item) {
          var _item$bookingKey, _item$bookingKey2, _newPass$bookingKey, _newPass$bookingKey2;
          return (item == null || (_item$bookingKey = item.bookingKey) == null ? undefined : _item$bookingKey.slice(0, item == null || (_item$bookingKey2 = item.bookingKey) == null || _item$bookingKey2.lastIndexOf == null ? undefined : _item$bookingKey2.lastIndexOf("_"))) === (newPass == null || (_newPass$bookingKey = newPass.bookingKey) == null ? undefined : _newPass$bookingKey.slice(0, newPass == null || (_newPass$bookingKey2 = newPass.bookingKey) == null || _newPass$bookingKey2.lastIndexOf == null ? undefined : _newPass$bookingKey2.lastIndexOf("_")));
        });
        if (existedPasses) {
          return (0, _toConsumableArray2.default)(passes || []).map(function (item) {
            if ((item == null ? undefined : item.bookingKey) === existedPasses.bookingKey) {
              return Object.assign({}, item || {}, {
                quantity: (0, _utils.toNumber)(item == null ? undefined : item.quantity) + (0, _utils.toNumber)(newPass == null ? undefined : newPass.quantity)
              });
            }
            return item;
          });
        }
        return passes.concat(newPass);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _this = this;
        var dataFormatted = {
          past: [],
          future: [],
          future_category: []
        };
        (0, _forEach.default)(action == null ? undefined : action.payload, function (e) {
          var _e$tickets;
          if (e != null && e.type && (e == null ? undefined : e.type) !== "carPass") {
            var isActive = e == null ? undefined : e.isActive;
            var isPast = !(e != null && e.isActive);
            if (isActive) {
              dataFormatted.future = _this.mergePass(dataFormatted.future, Object.assign({}, e, {
                isExpired: false
              }));
              var listCategoryItem = e == null ? undefined : e.tags.split(",");
              if (!(0, _isEmpty.default)(listCategoryItem)) {
                var arr = [];
                (0, _forEach.default)(listCategoryItem, function (item) {
                  var upper = item.charAt(0).toUpperCase() + item.slice(1);
                  arr.push(upper);
                });
                dataFormatted.future_category = (0, _toConsumableArray2.default)(new Set([].concat((0, _toConsumableArray2.default)(dataFormatted.future_category), arr)));
              }
            }
            if (isPast) {
              dataFormatted.past = _this.mergePass(dataFormatted.past, Object.assign({}, e, {
                isExpired: true
              }));
            }
          } else if ((e == null || (_e$tickets = e.tickets) == null || (_e$tickets = _e$tickets[0]) == null ? undefined : _e$tickets.type) === "carPass") {
            var _isPast = !(e != null && e.isActive);
            if (_isPast) {
              var _e$tickets2;
              dataFormatted.past.push(Object.assign({}, e, {
                isExpired: true,
                endDate: e == null || (_e$tickets2 = e.tickets) == null || (_e$tickets2 = _e$tickets2[0]) == null ? undefined : _e$tickets2.expiry,
                startDate: e == null ? undefined : e.created_ts,
                type: "carPass"
              }));
            } else {
              var _e$tickets3;
              dataFormatted.future.push(Object.assign({}, e, {
                isExpired: false,
                endDate: e == null || (_e$tickets3 = e.tickets) == null || (_e$tickets3 = _e$tickets3[0]) == null ? undefined : _e$tickets3.expiry,
                startDate: e == null ? undefined : e.created_ts,
                type: "carPass"
              }));
            }
          }
        });
        return dataFormatted;
      }
    }]);
  }(WalletMyTravelClass);
  var WalletDetailMyPerks = exports.WalletDetailMyPerks = /*#__PURE__*/function (_WalletMyTravelClass3) {
    function WalletDetailMyPerks() {
      (0, _classCallCheck2.default)(this, WalletDetailMyPerks);
      return _callSuper(this, WalletDetailMyPerks, arguments);
    }
    (0, _inherits2.default)(WalletDetailMyPerks, _WalletMyTravelClass3);
    return (0, _createClass2.default)(WalletDetailMyPerks, [{
      key: "success",
      value: function success(payload) {
        if ((0, _isEmpty.default)(payload)) return [];
        return payload == null ? undefined : payload.filter(function (e) {
          return (e == null ? undefined : e.visible_flag) === "true";
        });
      }
    }]);
  }(WalletMyTravelClass);
  var WalletQuantity = exports.WalletQuantity = /*#__PURE__*/function (_WalletMyTravelClass4) {
    function WalletQuantity() {
      (0, _classCallCheck2.default)(this, WalletQuantity);
      return _callSuper(this, WalletQuantity, arguments);
    }
    (0, _inherits2.default)(WalletQuantity, _WalletMyTravelClass4);
    return (0, _createClass2.default)(WalletQuantity, [{
      key: "handleTitle",
      value: function handleTitle(element, count) {
        return Object.assign({}, element, {
          qTitle: count ? `${element == null ? undefined : element.title} (${count})` : `${element == null ? undefined : element.title} (0)`
        });
      }
    }, {
      key: "handleCountPass",
      value: function handleCountPass(element, payload) {
        var _payload$getPlaypassB, _payload$getPlaypassB2, _payload$getCarpassBo, _payload$getCarpassBo2;
        var count = (0, _utils.handleCondition)(payload == null || (_payload$getPlaypassB = payload.getPlaypassBookings) == null || (_payload$getPlaypassB = _payload$getPlaypassB[0]) == null ? undefined : _payload$getPlaypassB.playpass_count, Number(payload == null || (_payload$getPlaypassB2 = payload.getPlaypassBookings) == null || (_payload$getPlaypassB2 = _payload$getPlaypassB2[0]) == null ? undefined : _payload$getPlaypassB2.playpass_count), 0) + (0, _utils.handleCondition)(payload == null || (_payload$getCarpassBo = payload.getCarpassBookings) == null || (_payload$getCarpassBo = _payload$getCarpassBo[0]) == null ? undefined : _payload$getCarpassBo.carpass_count, Number(payload == null || (_payload$getCarpassBo2 = payload.getCarpassBookings) == null || (_payload$getCarpassBo2 = _payload$getCarpassBo2[0]) == null ? undefined : _payload$getCarpassBo2.carpass_count), 0);
        return this.handleTitle(element, count);
      }
    }, {
      key: "handleCountMyTravel",
      value: function handleCountMyTravel(element, payload) {
        var _payload$getSavedFlig;
        var count = payload == null || (_payload$getSavedFlig = payload.getSavedFlights) == null ? undefined : _payload$getSavedFlig.length;
        return this.handleTitle(element, count);
      }
    }, {
      key: "handleCountPerk",
      value: function handleCountPerk(element, payload, additional) {
        var _payload$additional$p, _payload$additional$p2;
        var count = (0, _utils.handleCondition)((_payload$additional$p = payload[additional.perkFunctionName]) == null ? undefined : _payload$additional$p.active_perk_count, (_payload$additional$p2 = payload[additional.perkFunctionName]) == null ? undefined : _payload$additional$p2.active_perk_count, 0);
        return this.handleTitle(element, count);
      }
    }, {
      key: "handleCountCredit",
      value: function handleCountCredit(element, payload) {
        var _payload$getCredits;
        var count = payload == null || (_payload$getCredits = payload.getCredits) == null ? undefined : _payload$getCredits.length;
        return this.handleTitle(element, count);
      }
    }, {
      key: "handleCountOrder",
      value: function handleCountOrder(element, payload) {
        var _payload$getOrders, _payload$getOrders2;
        var count = (0, _utils.handleCondition)(payload == null || (_payload$getOrders = payload.getOrders) == null || (_payload$getOrders = _payload$getOrders[0]) == null ? undefined : _payload$getOrders.total_results, payload == null || (_payload$getOrders2 = payload.getOrders) == null || (_payload$getOrders2 = _payload$getOrders2[0]) == null ? undefined : _payload$getOrders2.total_results, 0);
        return this.handleTitle(element, count);
      }
    }, {
      key: "mapItemByNavigationValue",
      value: function mapItemByNavigationValue(element, payload, additional) {
        var _element$navigation = element == null ? undefined : element.navigation,
          value = _element$navigation.value;
        switch (value) {
          case _enum.NavigationWalletTypes.walletPasses:
            return this.handleCountPass(element, payload);
          case _enum.NavigationWalletTypes.walletTravel:
            return this.handleCountMyTravel(element, payload);
          case _enum.NavigationWalletTypes.walletPerks:
            return this.handleCountPerk(element, payload, additional);
          case _enum.NavigationWalletTypes.walletCredits:
            return this.handleCountCredit(element, payload);
          case _enum.NavigationWalletTypes.walletOrders:
            return this.handleCountOrder(element, payload);
          default:
            return element;
        }
      }
    }, {
      key: "success",
      value: function success(payload, shortLinkList) {
        var _this2 = this;
        var additional = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
        if ((0, _isEmpty.default)(payload)) return [];
        return shortLinkList == null ? undefined : shortLinkList.map(function (element) {
          return _this2.mapItemByNavigationValue(element, payload, additional);
        });
      }
    }, {
      key: "failure",
      value: function failure(shortLinkList) {
        if ((0, _isEmpty.default)(shortLinkList)) return [];
        return shortLinkList == null ? undefined : shortLinkList.map(function (e) {
          return Object.assign({}, e, {
            qTitle: `${e == null ? undefined : e.title} (0)`
          });
        });
      }
    }]);
  }(WalletMyTravelClass);
