  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PageLevelConfig = undefined;
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var PageLevelConfig = exports.PageLevelConfig = /*#__PURE__*/function () {
    function PageLevelConfig() {
      (0, _classCallCheck2.default)(this, PageLevelConfig);
      this.blackListSection = ["chipFilter", "dineReservation"];
    }
    return (0, _createClass2.default)(PageLevelConfig, [{
      key: "getPageConfig",
      value: function getPageConfig(data) {
        var nPageConfig = [];
        var lengthData = data == null ? undefined : data.length;
        if (lengthData) {
          for (var i = 0; i < lengthData; i++) {
            var _data$i;
            if (!this.blackListSection.includes((_data$i = data[i]) == null ? undefined : _data$i.name)) {
              var item = data[i];
              item.props.sectionName = item.props.title;
              item.props._uid = item == null ? undefined : item._uid;
              nPageConfig.push(item);
            }
          }
        }
        return {
          name: "view",
          _uid: "1",
          children: nPageConfig
        };
      }
    }, {
      key: "success",
      value: function success(action, pageLayoutComponentStructure) {
        var data = action.payload.data;
        if (data && data.getPageConfig !== null && data.getPageConfig.length > 0) {
          var pageConfigurationArray = data == null ? undefined : data.getPageConfig;
          return new PageLevelConfig().getPageConfig(pageConfigurationArray);
        } else {
          var _pageConfigurationArray = pageLayoutComponentStructure == null ? undefined : pageLayoutComponentStructure.children;
          return new PageLevelConfig().getPageConfig(_pageConfigurationArray);
        }
      }
    }, {
      key: "failure",
      value: function failure(pageLayoutComponentStructure) {
        var pageConfigurationArray = pageLayoutComponentStructure == null ? undefined : pageLayoutComponentStructure.children;
        return new PageLevelConfig().getPageConfig(pageConfigurationArray);
      }
    }]);
  }();
