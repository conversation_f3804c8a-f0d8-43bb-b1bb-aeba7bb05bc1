  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FilterValueShop = exports.FilterValueDine = undefined;
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var FilterValueDine = exports.FilterValueDine = /*#__PURE__*/function () {
    function FilterValueDine() {
      (0, _classCallCheck2.default)(this, FilterValueDine);
    }
    return (0, _createClass2.default)(FilterValueDine, null, [{
      key: "setFilterValueDine",
      value: function setFilterValueDine(filterState, filterTitles) {
        this.filterValueItems = filterState;
        this.filterValueTitles = filterTitles;
      }
    }, {
      key: "getFilterValueDine",
      value: function getFilterValueDine() {
        return {
          filterValueItems: this.filterValueItems,
          filterValueTitles: this.filterValueTitles
        };
      }
    }]);
  }();
  var FilterValueShop = exports.FilterValueShop = /*#__PURE__*/function () {
    function FilterValueShop() {
      (0, _classCallCheck2.default)(this, FilterValueShop);
    }
    return (0, _createClass2.default)(FilterValueShop, null, [{
      key: "setFilterValueShop",
      value: function setFilterValueShop(filterState, filterTitles) {
        this.filterValueItems = filterState;
        this.filterValueTitles = filterTitles;
      }
    }, {
      key: "getFilterValueShop",
      value: function getFilterValueShop() {
        return {
          filterValueItems: this.filterValueItems,
          filterValueTitles: this.filterValueTitles
        };
      }
    }]);
  }();
