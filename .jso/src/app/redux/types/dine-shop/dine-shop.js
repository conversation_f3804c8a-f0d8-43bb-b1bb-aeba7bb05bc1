  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Tenants = exports.TenantOfferDetails = exports.SpotlightBrandOffer = exports.ShopFilterResultData = exports.RestaurantShopDetails = exports.RestaurantShopData = exports.RestaurantChangiRewardsData = exports.RestaurantChangiEatsData = exports.RestaurantBlogsAndReviews = exports.PopularRestaurants = exports.PartnerOffer = exports.NotToBeMissed = exports.NewlyOpened = exports.MasonryLanding = exports.MainPromo = exports.LocationList = exports.FilterPills = exports.FilterParameter = exports.ExploreMore = exports.DiningGuideDetail = exports.DiningGuide = exports.DineShopOffersData = exports.DineShopDetailsLocationList = exports.DineShopDetails = exports.DineReservationList = exports.DineFilterResultData = exports.CuisinesCategoriesData = exports.ChangiRewards = exports.ChangiEats = exports.BlogsAndReviews = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _dineShopFilter = _$$_REQUIRE(_dependencyMap[7]);
  var _chipFilter = _$$_REQUIRE(_dependencyMap[8]);
  var _contentGuide = _$$_REQUIRE(_dependencyMap[9]);
  var _masonryListing = _$$_REQUIRE(_dependencyMap[10]);
  var _mainPromo = _$$_REQUIRE(_dependencyMap[11]);
  var _dineShopBaseClass = _$$_REQUIRE(_dependencyMap[12]);
  var _tenantOffer = _$$_REQUIRE(_dependencyMap[13]);
  var _infoBanner = _$$_REQUIRE(_dependencyMap[14]);
  var _tenant = _$$_REQUIRE(_dependencyMap[15]);
  var _i18n = _$$_REQUIRE(_dependencyMap[16]);
  var _constants = _$$_REQUIRE(_dependencyMap[17]);
  var _productOffer = _$$_REQUIRE(_dependencyMap[18]);
  var _tenantListingHorizontal = _$$_REQUIRE(_dependencyMap[19]);
  var _cuisineCategory = _$$_REQUIRE(_dependencyMap[20]);
  var _lodash = _$$_REQUIRE(_dependencyMap[21]);
  var _utils = _$$_REQUIRE(_dependencyMap[22]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var redeemable = "yes";
  var loadingType = "loading";
  var defaultState = "default";
  var convertTenantListingHorizontal = function convertTenantListingHorizontal(item, type) {
    if (item) {
      var _item$aemTenantDetail, _item$aemTenantDetail2, _item$aemTenantDetail3, _item$aemTenantDetail4, _item$aemTenantDetail5, _item$aemTenantDetail6, _item$aemTenantDetail7, _item$aemTenantDetail8;
      return {
        aemAvailability: item == null ? undefined : item.aem_availability,
        area: item == null || (_item$aemTenantDetail = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail.area,
        areaDisplay: item == null ? undefined : item.area_display,
        categories: item == null || (_item$aemTenantDetail2 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail2.categories,
        dietary: item == null || (_item$aemTenantDetail3 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail3.dietary,
        imageUrlArray: item == null || (_item$aemTenantDetail4 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail4.additionalTileImages,
        location: Tenants.handleExploreMoreLocation(item.location_display, item == null || (_item$aemTenantDetail5 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail5.categories, item == null || (_item$aemTenantDetail6 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail6.price),
        locationDisplay: item == null ? undefined : item.location_display,
        logoUrl: (item == null || (_item$aemTenantDetail7 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail7.logoImage) || (item == null ? undefined : item.logoImage),
        rewardTitle: item == null || (_item$aemTenantDetail8 = item.aemTenantDetails) == null || (_item$aemTenantDetail8 = _item$aemTenantDetail8.rewards) == null ? undefined : _item$aemTenantDetail8.title,
        tenantName: item.title,
        tenentId: item.id,
        type: type
      };
    }
    return {
      location: undefined,
      imageUrlArray: undefined,
      logoUrl: undefined,
      tenantName: undefined,
      rewardTitle: undefined,
      aemAvailability: undefined,
      tenentId: undefined,
      type: type
    };
  };
  var Tenants = exports.Tenants = /*#__PURE__*/function () {
    function Tenants(data) {
      var _data$aemTenantDetail, _data$aemTenantDetail2, _data$aemTenantDetail3, _data$aemTenantDetail4, _data$aemTenantDetail5;
      (0, _classCallCheck2.default)(this, Tenants);
      this.id = data == null ? undefined : data.id;
      this.type = _tenant.TenantType.default;
      this.logoUrl = (_data$aemTenantDetail = data.aemTenantDetails) == null ? undefined : _data$aemTenantDetail.logoImage;
      this.location = data.location ? this.handleLocations(data.location) : null;
      this.imageUrl = (_data$aemTenantDetail2 = data.aemTenantDetails) == null ? undefined : _data$aemTenantDetail2.backgroundImage;
      this.tenantName = data.title;
      this.imageUrlArray = (_data$aemTenantDetail3 = data.aemTenantDetails) == null ? undefined : _data$aemTenantDetail3.additionalTileImages;
      this.categories = (_data$aemTenantDetail4 = data.aemTenantDetails) == null ? undefined : _data$aemTenantDetail4.categories;
      this.price = (_data$aemTenantDetail5 = data.aemTenantDetails) == null ? undefined : _data$aemTenantDetail5.price;
      this.outletsLocationString = Tenants.handleExploreMoreLocation(data.location_display, this.categories, this.price) || "";
      this.rewardTitle = (0, _utils.handleCondition)(data.aemTenantDetails, (0, _utils.handleCondition)(data.aemTenantDetails.rewards, data.aemTenantDetails.rewards.title, ""), "");
      this.title = data.pageTitle;
      this.tags = data.tags;
      this.extraLocations = data.locations;
      this.foodPrice = data.price;
    }
    return (0, _createClass2.default)(Tenants, [{
      key: "handleLocations",
      value: function handleLocations(location) {
        return location && location.length > 1 ? (0, _i18n.translate)("newlyOpened.multipleLocations") : location;
      }
    }], [{
      key: "handleExploreMoreLocation",
      value: function handleExploreMoreLocation(location, categories, price) {
        var parts = [];
        parts.push(location);
        parts.push(categories == null ? undefined : categories.join(", "));
        parts.push(price);
        return parts.filter(function (value) {
          return !(0, _lodash.isEmpty)(value);
        }).join(` ${(0, _constants.getDotUnicode)()} `);
      }
    }]);
  }();
  var LocationsList = /*#__PURE__*/(0, _createClass2.default)(function LocationsList(data) {
    (0, _classCallCheck2.default)(this, LocationsList);
    this.mapName = data.mapName;
    this.status = data.status;
    this.statusColor = data.statusColor;
    this.getDirectionsText = data.getDirectionsText;
    this.getDirectionsCoordinates = data.getDirectionsCoordinates;
    this.websiteViewText = data.websiteViewText;
    this.websiteViewLink = data.websiteViewLink;
    this.viewMenuText = data.viewMenuText;
    this.viewMenuLink = data.viewMenuLink;
    this.phoneDetails = data.phoneDetails;
    this.timingsInfo = data.timings;
    this.areaInfo = data.areaInfo;
    this.isExpanded = data.isExpanded;
  });
  var BlogsAndReviews = exports.BlogsAndReviews = /*#__PURE__*/(0, _createClass2.default)(function BlogsAndReviews(blogsAndReviewType, blogsAndReviewsData) {
    (0, _classCallCheck2.default)(this, BlogsAndReviews);
    this.blogTitle = blogsAndReviewsData == null ? undefined : blogsAndReviewsData.blogTitle;
    this.linkText = blogsAndReviewsData == null ? undefined : blogsAndReviewsData.blogLink;
    this.logo = blogsAndReviewsData == null ? undefined : blogsAndReviewsData.blogLogoImage;
    this.description = blogsAndReviewsData == null ? undefined : blogsAndReviewsData.description;
    this.type = blogsAndReviewType;
  });
  var ChangiEats = exports.ChangiEats = /*#__PURE__*/(0, _createClass2.default)(function ChangiEats(changiEatsType, changiEatsData) {
    (0, _classCallCheck2.default)(this, ChangiEats);
    this.image = changiEatsData == null ? undefined : changiEatsData.image;
    this.copy = changiEatsData == null ? undefined : changiEatsData.copy;
    this.btnLabel = changiEatsData == null ? undefined : changiEatsData.buttonLabel;
    this.btnLink = changiEatsData == null ? undefined : changiEatsData.link;
    this.type = changiEatsType;
  });
  var NewlyOpened = exports.NewlyOpened = /*#__PURE__*/function (_DineShopBaseClass) {
    function NewlyOpened(tenants, hasError, errorPayload) {
      var _this;
      (0, _classCallCheck2.default)(this, NewlyOpened);
      _this = _callSuper(this, NewlyOpened);
      _this.data = tenants;
      _this.hasError = hasError;
      _this.errorPayload = errorPayload;
      return _this;
    }
    (0, _inherits2.default)(NewlyOpened, _DineShopBaseClass);
    return (0, _createClass2.default)(NewlyOpened, [{
      key: "failure",
      value: function failure() {
        return new NewlyOpened([], true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new NewlyOpened((0, _toConsumableArray2.default)(Array.from(Array(3)).map(function () {
          return NewlyOpened.convertDto({}, _tenant.TenantType.loading);
        })), false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload = action.payload,
          data = _action$payload.data,
          errors = _action$payload.errors;
        if ((data == null ? undefined : data.getNewlyOpened) === null && errors.length > 0) {
          var extractErrors = NewlyOpened.checkErrors("getNewlyOpened", errors);
          return new NewlyOpened([], extractErrors[0], extractErrors[1]);
        } else {
          var _data$getNewlyOpened;
          var dataHolder = [];
          dataHolder = (data == null || (_data$getNewlyOpened = data.getNewlyOpened) == null ? undefined : _data$getNewlyOpened.length) > 0 ? data.getNewlyOpened.map(function (item) {
            return NewlyOpened.convertDto(item, _tenant.TenantType.default);
          }) : [];
          return new NewlyOpened(dataHolder, false, []);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  NewlyOpened.convertDto = function (item, type) {
    if (item) {
      var _item$aemTenantDetail9, _item$aemTenantDetail0;
      return {
        imageUrl: item == null || (_item$aemTenantDetail9 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail9.backgroundImage,
        logoUrl: item == null || (_item$aemTenantDetail0 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail0.logoImage,
        location: Tenants.handleExploreMoreLocation(item.location_display),
        tenantName: item.title,
        type: type,
        id: item.id
      };
    }
    return {
      imageUrl: undefined,
      logoUrl: undefined,
      location: undefined,
      tenantName: undefined,
      type: type
    };
  };
  var ExploreMore = exports.ExploreMore = /*#__PURE__*/function (_DineShopBaseClass2) {
    function ExploreMore(tenants, isMoreRecords, hasError, errorPayload) {
      var _this2;
      (0, _classCallCheck2.default)(this, ExploreMore);
      _this2 = _callSuper(this, ExploreMore);
      _this2.data = tenants;
      _this2.isMoreRecords = isMoreRecords;
      _this2.hasError = hasError;
      _this2.errorPayload = errorPayload;
      return _this2;
    }
    (0, _inherits2.default)(ExploreMore, _DineShopBaseClass2);
    return (0, _createClass2.default)(ExploreMore, [{
      key: "failure",
      value: function failure() {
        return new ExploreMore([], false, true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new ExploreMore((0, _toConsumableArray2.default)(Array.from(Array(4)).map(function () {
          return convertTenantListingHorizontal({}, _tenantListingHorizontal.TenantListingHorizontalType.loading);
        })), false, false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload2 = action.payload,
          data = _action$payload2.data,
          errors = _action$payload2.errors;
        if ((data == null ? undefined : data.getExploreMoreLanding) === null && errors.length > 0) {
          var extractErrors = ExploreMore.checkErrors("getExploreMoreLanding", errors);
          return new ExploreMore([], false, extractErrors[0], extractErrors[1]);
        } else {
          var _data$getExploreMoreL, _data$getExploreMoreL2;
          var dataHolder = [];
          dataHolder = (data == null || (_data$getExploreMoreL = data.getExploreMoreLanding) == null || (_data$getExploreMoreL = _data$getExploreMoreL.nodes) == null ? undefined : _data$getExploreMoreL.length) > 0 ? Array.isArray(data.getExploreMoreLanding.nodes) && data.getExploreMoreLanding.nodes.map(function (item) {
            return convertTenantListingHorizontal(item, _tenantListingHorizontal.TenantListingHorizontalType.default);
          }) : [];
          return new ExploreMore(dataHolder, data == null || (_data$getExploreMoreL2 = data.getExploreMoreLanding) == null ? undefined : _data$getExploreMoreL2.moreRecords, false, []);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  var DiningGuideDetail = exports.DiningGuideDetail = /*#__PURE__*/(0, _createClass2.default)(function DiningGuideDetail(data) {
    (0, _classCallCheck2.default)(this, DiningGuideDetail);
    this.type = _contentGuide.ContentGuideType.default;
    this.imageUrl = data.image;
    this.source = data.source;
    this.description = data.description;
    this.title = data.title;
    this.link = data.link;
  });
  var DiningGuide = exports.DiningGuide = /*#__PURE__*/function () {
    function DiningGuide(guides, type, hasError, errorPayload) {
      (0, _classCallCheck2.default)(this, DiningGuide);
      this.data = guides;
      this.type = type;
      this.hasError = hasError;
      this.errorPayload = errorPayload;
    }
    return (0, _createClass2.default)(DiningGuide, null, [{
      key: "getLoadingData",
      value: function getLoadingData() {
        return new DiningGuide([new DiningGuideDetail({}), new DiningGuideDetail({}), new DiningGuideDetail({})], _contentGuide.ContentGuideType.loading, false, []);
      }
    }]);
  }();
  var FilterPills = exports.FilterPills = /*#__PURE__*/function (_DineShopBaseClass3) {
    function FilterPills(chipsData, type, errorFlag, errorPayload) {
      var _this3;
      (0, _classCallCheck2.default)(this, FilterPills);
      _this3 = _callSuper(this, FilterPills);
      _this3.data = chipsData;
      _this3.type = type;
      _this3.errorFlag = errorFlag;
      _this3.errorPayload = errorPayload;
      return _this3;
    }
    (0, _inherits2.default)(FilterPills, _DineShopBaseClass3);
    return (0, _createClass2.default)(FilterPills, [{
      key: "failure",
      value: function failure() {
        return new FilterPills([], _chipFilter.ChipFilterType.default, true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new FilterPills([{}, {}, {}], _chipFilter.ChipFilterType.loading, false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload3 = action.payload,
          data = _action$payload3.data,
          errors = _action$payload3.errors;
        if (data === null && errors.length > 0) {
          var extractErrors = FilterPills.checkErrors("listFilterPillDineShop", errors);
          return new FilterPills([{}, {}, {}], _chipFilter.ChipFilterType.loading, extractErrors[0], extractErrors[1]);
        } else {
          return new FilterPills(data, _chipFilter.ChipFilterType.default, false, []);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  var FilterParameter = exports.FilterParameter = /*#__PURE__*/function (_DineShopBaseClass4) {
    function FilterParameter(filterListData, type, errorFlag, errorPayload) {
      var _this4;
      (0, _classCallCheck2.default)(this, FilterParameter);
      _this4 = _callSuper(this, FilterParameter);
      _this4.data = filterListData;
      _this4.type = type;
      _this4.errorFlag = errorFlag;
      _this4.errorPayload = errorPayload;
      return _this4;
    }
    (0, _inherits2.default)(FilterParameter, _DineShopBaseClass4);
    return (0, _createClass2.default)(FilterParameter, [{
      key: "failure",
      value: function failure() {
        return new FilterParameter([], _dineShopFilter.DineShopFilterType.default, true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new FilterParameter([], _dineShopFilter.DineShopFilterType.loading, false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _data$childTags;
        var data = action.payload.data;
        return data == null || (_data$childTags = data.childTags) == null ? undefined : _data$childTags.filter(function (e) {
          return !(0, _lodash.isEmpty)(e == null ? undefined : e.childTags) && (e == null ? undefined : e.tagName) !== "default";
        });
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  var MasonryLanding = exports.MasonryLanding = /*#__PURE__*/function (_DineShopBaseClass5) {
    function MasonryLanding(data, hasMoreRecords, errorFlag, errorPayload) {
      var _this5;
      (0, _classCallCheck2.default)(this, MasonryLanding);
      _this5 = _callSuper(this, MasonryLanding);
      _this5.hasMoreRecords = false;
      _this5.data = data;
      _this5.hasMoreRecords = hasMoreRecords;
      _this5.errorFlag = errorFlag;
      _this5.errorPayload = errorPayload;
      return _this5;
    }
    (0, _inherits2.default)(MasonryLanding, _DineShopBaseClass5);
    return (0, _createClass2.default)(MasonryLanding, [{
      key: "failure",
      value: function failure() {
        return new MasonryLanding([], false, true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new MasonryLanding((0, _toConsumableArray2.default)(Array.from(Array(6)).map(function () {
          return MasonryLanding.convertDto({}, _masonryListing.MasonryListingState.loading);
        })), false, false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload4 = action.payload,
          data = _action$payload4.data,
          errors = _action$payload4.errors;
        if ((data == null ? undefined : data.getMasonryLanding) === null && errors.length > 0) {
          var extractErrors = MasonryLanding.checkErrors("getMasonryLanding", errors);
          return new MasonryLanding([], false, extractErrors[0], extractErrors[1]);
        } else {
          var _data$getMasonryLandi, _data$getMasonryLandi2;
          var dataHolder = [];
          dataHolder = (data == null || (_data$getMasonryLandi = data.getMasonryLanding) == null || (_data$getMasonryLandi = _data$getMasonryLandi.nodes) == null ? undefined : _data$getMasonryLandi.length) > 0 ? Array.isArray(data.getMasonryLanding.nodes) && data.getMasonryLanding.nodes.map(function (item) {
            return MasonryLanding.convertDto(item, _masonryListing.MasonryListingState.default);
          }) : [];
          return new MasonryLanding(dataHolder, data == null || (_data$getMasonryLandi2 = data.getMasonryLanding) == null ? undefined : _data$getMasonryLandi2.moreRecords, false, []);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  MasonryLanding.convertDto = function (item, type) {
    if (item) {
      var _item$aemTenantDetail1, _item$aemTenantDetail10, _item$aemTenantDetail11;
      return {
        areaDisplay: item == null ? undefined : item.area_display,
        id: 0,
        location: item == null ? undefined : item.location_display,
        logoImage: (item == null ? undefined : item.logoImage) || (item == null || (_item$aemTenantDetail1 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail1.logoImage),
        longImage: item == null || (_item$aemTenantDetail10 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail10.longImage,
        shortImage: item == null || (_item$aemTenantDetail11 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail11.shortImage,
        state: type,
        tenantId: item == null ? undefined : item.id,
        titleName: item == null ? undefined : item.title
      };
    }
    return {
      id: undefined,
      longImage: undefined,
      shortImage: undefined,
      location: undefined,
      titleName: undefined,
      state: type,
      tenantId: undefined,
      logoImage: undefined
    };
  };
  var ShopFilterResultData = exports.ShopFilterResultData = /*#__PURE__*/function (_DineShopBaseClass6) {
    function ShopFilterResultData(tenants, pageInfo, totalCount, errorFlag, errorPayload, hasNextPage) {
      var _this6;
      (0, _classCallCheck2.default)(this, ShopFilterResultData);
      _this6 = _callSuper(this, ShopFilterResultData);
      _this6.data = tenants;
      _this6.pageInfo = pageInfo;
      _this6.totalCount = totalCount;
      _this6.errorFlag = errorFlag;
      _this6.errorPayload = errorPayload;
      _this6.hasNextPage = hasNextPage;
      return _this6;
    }
    (0, _inherits2.default)(ShopFilterResultData, _DineShopBaseClass6);
    return (0, _createClass2.default)(ShopFilterResultData, [{
      key: "failure",
      value: function failure() {
        return new ShopFilterResultData((0, _toConsumableArray2.default)(Array.from(Array(4)).map(function () {
          return convertTenantListingHorizontal({}, _tenantListingHorizontal.TenantListingHorizontalType.loading);
        })), {
          endCursor: "",
          startCursor: "",
          hasPreviousPage: false,
          hasNextPage: false
        }, 0, true, [], false);
      }
    }, {
      key: "request",
      value: function request() {
        return new ShopFilterResultData((0, _toConsumableArray2.default)(Array.from(Array(4)).map(function () {
          return convertTenantListingHorizontal({}, _tenantListingHorizontal.TenantListingHorizontalType.loading);
        })), {
          endCursor: "",
          startCursor: "",
          hasPreviousPage: false,
          hasNextPage: false
        }, 0, false, [], false);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload5 = action.payload,
          data = _action$payload5.data,
          errors = _action$payload5.errors;
        if ((data == null ? undefined : data.getResultScreenByFilterShop_v2) === null && errors.length > 0) {
          var extractErrors = ShopFilterResultData.checkErrors("getResultScreenByFilterShop_v2", errors);
          return new ShopFilterResultData((0, _toConsumableArray2.default)(Array.from(Array(4)).map(function () {
            return convertTenantListingHorizontal({}, _tenantListingHorizontal.TenantListingHorizontalType.loading);
          })), extractErrors[0], extractErrors[1], false);
        } else {
          var _data$getResultScreen, _data$getResultScreen2, _data$getResultScreen3;
          var dataResult = [];
          dataResult = (data == null || (_data$getResultScreen = data.getResultScreenByFilterShop_v2) == null || (_data$getResultScreen = _data$getResultScreen.nodes) == null ? undefined : _data$getResultScreen.length) > 0 ? Array.isArray(data.getResultScreenByFilterShop_v2.nodes) && data.getResultScreenByFilterShop_v2.nodes.map(function (item) {
            return convertTenantListingHorizontal(item, _tenantListingHorizontal.TenantListingHorizontalType.default);
          }) : [];
          var resultPageInfo = data != null && (_data$getResultScreen2 = data.getResultScreenByFilterShop_v2) != null && _data$getResultScreen2.pageInfo ? {
            endCursor: data.getResultScreenByFilterShop_v2.pageInfo.endCursor,
            startCursor: data.getResultScreenByFilterShop_v2.pageInfo.startCursor,
            hasPreviousPage: data.getResultScreenByFilterShop_v2.pageInfo.hasPreviousPage,
            hasNextPage: data.getResultScreenByFilterShop_v2.pageInfo.hasNextPage
          } : {};
          var totalCount = data == null || (_data$getResultScreen3 = data.getResultScreenByFilterShop_v2) == null ? undefined : _data$getResultScreen3.totalCount;
          var hasNextPage = resultPageInfo == null ? undefined : resultPageInfo.hasNextPage;
          return new ShopFilterResultData(dataResult, resultPageInfo, totalCount, false, [], hasNextPage);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  var RestaurantShopData = exports.RestaurantShopData = /*#__PURE__*/(0, _createClass2.default)(function RestaurantShopData(type, data, hasError, errorPayload) {
    (0, _classCallCheck2.default)(this, RestaurantShopData);
    this.data = data;
    this.hasError = hasError;
    this.errorPayload = errorPayload;
    this.type = type;
  });
  var DineShopOffersData = exports.DineShopOffersData = /*#__PURE__*/function (_DineShopBaseClass7) {
    function DineShopOffersData(type, data, hasError, errorPayload) {
      var _this7;
      (0, _classCallCheck2.default)(this, DineShopOffersData);
      _this7 = _callSuper(this, DineShopOffersData);
      _this7.data = data;
      _this7.hasError = hasError;
      _this7.errorPayload = errorPayload;
      _this7.type = type;
      return _this7;
    }
    (0, _inherits2.default)(DineShopOffersData, _DineShopBaseClass7);
    return (0, _createClass2.default)(DineShopOffersData, [{
      key: "request",
      value: function request() {
        return new DineShopOffersData(loadingType, new DineShopDetails({}), false, []);
      }
    }, {
      key: "failure",
      value: function failure() {
        return new DineShopOffersData(defaultState, new DineShopDetails({}), true, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload6 = action.payload,
          data = _action$payload6.data,
          errors = _action$payload6.errors;
        if (data && data.getOfferDetail === null && (errors == null ? undefined : errors.length) > 0 || data && data.getOfferDetail && (errors == null ? undefined : errors.length) > 0) {
          var extractErrors = DineShopOffersData.checkErrors("getDineShopOffersDetails", errors);
          return new DineShopOffersData(loadingType, new DineShopDetails({}), extractErrors[0], extractErrors[1]);
        } else {
          var _data$getOfferDetail, _locationDetails, _locationDetails2;
          if ((data == null ? undefined : data.getOfferDetail) == null || (data == null || (_data$getOfferDetail = data.getOfferDetail) == null ? undefined : _data$getOfferDetail.locationDetails) == null) {
            return new DineShopOffersData(defaultState, new DineShopDetails({}), true, []);
          }
          var offerDetailsData = data == null ? undefined : data.getOfferDetail;
          var locationDetails = offerDetailsData == null ? undefined : offerDetailsData.locationDetails;
          if (locationDetails) {
            locationDetails = [].concat((0, _toConsumableArray2.default)(locationDetails), [{
              title: offerDetailsData == null ? undefined : offerDetailsData.section1Title,
              description: offerDetailsData == null ? undefined : offerDetailsData.section1Text
            }, {
              title: offerDetailsData == null ? undefined : offerDetailsData.section2Title,
              description: offerDetailsData == null ? undefined : offerDetailsData.section2Text
            }, {
              title: offerDetailsData == null ? undefined : offerDetailsData.section3Title,
              description: offerDetailsData == null ? undefined : offerDetailsData.section3Text
            }]);
          }
          (_locationDetails = locationDetails) == null || _locationDetails.map(function (location, index) {
            location.isExpanded = false;
            if (index === 0) {
              location.isExpanded = true;
            }
            return location;
          });
          var heroImages = offerDetailsData == null ? undefined : offerDetailsData.additionalImages.map(function (item, index) {
            return {
              orderId: index,
              backgroundImageUrl: item
            };
          });
          var locationData = ((_locationDetails2 = locationDetails) == null ? undefined : _locationDetails2.length) > 0 ? locationDetails.map(function (item) {
            return new DineShopDetailsLocationList(item);
          }) : [];
          var shopDetailsPayload = {};
          if (offerDetailsData) {
            shopDetailsPayload = {
              offerID: offerDetailsData.offerID,
              tenantID: offerDetailsData.tenantID,
              heroCarouselImage: heroImages,
              heroCarouselLogo: offerDetailsData.logo,
              title: offerDetailsData.offerTitle,
              tags: offerDetailsData.tagsToDisplay,
              locationDetails: locationData,
              buttonLink: offerDetailsData.buttonLink,
              buttonLabel: offerDetailsData.buttonLabel
            };
          } else {
            shopDetailsPayload = new DineShopDetails({});
          }
          var dineShopDetailsData = data ? new DineShopDetails(shopDetailsPayload) : new DineShopDetails({});
          return new DineShopOffersData(defaultState, dineShopDetailsData, false, []);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  var RestaurantChangiEatsData = exports.RestaurantChangiEatsData = /*#__PURE__*/(0, _createClass2.default)(function RestaurantChangiEatsData(data, hasError, errorPayload) {
    (0, _classCallCheck2.default)(this, RestaurantChangiEatsData);
    this.data = data;
    this.hasError = hasError;
    this.errorPayload = errorPayload;
  });
  var MainPromo = exports.MainPromo = /*#__PURE__*/function (_DineShopBaseClass8) {
    function MainPromo(promotion, type, errorFlag, errorPayload, isSectionContentAvailable) {
      var _this8;
      (0, _classCallCheck2.default)(this, MainPromo);
      _this8 = _callSuper(this, MainPromo);
      _this8.isSectionContentAvailable = false;
      _this8.payload = {
        promotions: promotion,
        type: type,
        defaultPayload: promotion
      };
      _this8.errorFlag = errorFlag;
      _this8.errorPayload = errorPayload;
      _this8.isSectionContentAvailable = isSectionContentAvailable;
      return _this8;
    }
    (0, _inherits2.default)(MainPromo, _DineShopBaseClass8);
    return (0, _createClass2.default)(MainPromo, [{
      key: "failure",
      value: function failure() {
        return new MainPromo([], _mainPromo.MainPromoType.default, true, [], true);
      }
    }, {
      key: "request",
      value: function request() {
        return new MainPromo([], _mainPromo.MainPromoType.loading, false, [], true);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload7 = action.payload,
          data = _action$payload7.data,
          errors = _action$payload7.errors;
        if (data && data.getMainPromoDineShop === null && errors) {
          var extractErrors = MainPromo.checkErrors("listTenant", errors);
          this.errorFlag = extractErrors[0];
          this.errorPayload = extractErrors[1];
          return new MainPromo([], _mainPromo.MainPromoType.default, this.errorFlag, this.errorPayload, false);
        } else {
          var dataHolder = [];
          if (data && (data == null ? undefined : data.getMainPromoDineShop.length) > 0) {
            dataHolder = data.getMainPromoDineShop.map(function (item, index) {
              return MainPromo.convertDto(item, index + 1);
            });
            return new MainPromo(dataHolder, _mainPromo.MainPromoType.default, false, [], dataHolder.length > 4);
          } else {
            dataHolder = [];
            return new MainPromo(dataHolder, _mainPromo.MainPromoType.default, false, [], false);
          }
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  MainPromo.convertDto = function (item, id) {
    return {
      orderId: id,
      offerId: item.offerId,
      tenantId: item.tenantId,
      backgroundImageUrl: item.image,
      subcopy: item.subCopy,
      title: item.promoTitle,
      linkURL: item.linkURL,
      navigationType: item.navigationType,
      navigationValue: item.navigationValue,
      redirect: item == null ? undefined : item.redirect
    };
  };
  var CuisinesCategoriesData = exports.CuisinesCategoriesData = /*#__PURE__*/function (_DineShopBaseClass9) {
    function CuisinesCategoriesData(data, hasError, errorPayload) {
      var _this9;
      (0, _classCallCheck2.default)(this, CuisinesCategoriesData);
      _this9 = _callSuper(this, CuisinesCategoriesData);
      _this9.data = data;
      _this9.errorFlag = hasError;
      _this9.errorPayload = errorPayload;
      return _this9;
    }
    (0, _inherits2.default)(CuisinesCategoriesData, _DineShopBaseClass9);
    return (0, _createClass2.default)(CuisinesCategoriesData, [{
      key: "failure",
      value: function failure() {
        return new CuisinesCategoriesData([], true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new CuisinesCategoriesData((0, _toConsumableArray2.default)(Array.from(Array(4)).map(function () {
          return CuisinesCategoriesData.convertCuisinesCategoriesDto({}, _cuisineCategory.CuisineCategoryType.loading);
        })), false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload8 = action.payload,
          data = _action$payload8.data,
          errors = _action$payload8.errors;
        if (data && data.getCuisineCategories === null && errors.length > 0) {
          var extractErrors = CuisinesCategoriesData.checkErrors("getCuisineCategories", errors);
          return new CuisinesCategoriesData([], extractErrors[0], extractErrors[1]);
        } else {
          var _data$getCuisineCateg;
          var dataCuisinesCategories = [];
          dataCuisinesCategories = (data == null || (_data$getCuisineCateg = data.getCuisineCategories) == null ? undefined : _data$getCuisineCateg.length) > 0 ? data.getCuisineCategories.map(function (item) {
            return CuisinesCategoriesData.convertCuisinesCategoriesDto(item, _cuisineCategory.CuisineCategoryType.default);
          }) : [];
          return new CuisinesCategoriesData(dataCuisinesCategories, false, []);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  CuisinesCategoriesData.convertCuisinesCategoriesDto = function (item, type) {
    if (item) {
      return {
        type: type,
        imageUrl: item.image,
        labelCopy: item.tagTitle,
        tagName: item.tagName,
        filterType: item.filterType
      };
    } else {
      return {
        type: type,
        imageUrl: undefined,
        labelCopy: undefined,
        tagName: undefined,
        filterType: undefined
      };
    }
  };
  var PartnerOffer = exports.PartnerOffer = /*#__PURE__*/function () {
    function PartnerOffer(type, data, logo, titleText, sectionName, screen, hasError, errorPayload) {
      (0, _classCallCheck2.default)(this, PartnerOffer);
      this.type = type;
      this.data = data;
      this.logo = logo || "";
      this.titleText = titleText || "";
      this.sectionName = sectionName || "";
      this.screen = screen;
      this.hasError = hasError;
      this.errorPayload = errorPayload;
    }
    return (0, _createClass2.default)(PartnerOffer, null, [{
      key: "getLoadingData",
      value: function getLoadingData() {
        var offerLoadingData = [new TenantOfferDetails(_tenantOffer.TenantTypeEnum.loading, {
          ribbonText: "",
          tenantName: "",
          offerTitle: "",
          validityDate: "",
          subCopy: "",
          remainingQuantity: 0,
          totalQuantity: 0,
          image: "",
          linkURL: ""
        }), new TenantOfferDetails(_tenantOffer.TenantTypeEnum.loading, {
          ribbonText: "",
          tenantName: "",
          offerTitle: "",
          validityDate: "",
          subCopy: "",
          remainingQuantity: 0,
          totalQuantity: 0,
          image: "",
          linkURL: ""
        }), new TenantOfferDetails(_tenantOffer.TenantTypeEnum.loading, {
          ribbonText: "",
          tenantName: "",
          offerTitle: "",
          validityDate: "",
          subCopy: "",
          remainingQuantity: 0,
          totalQuantity: 0,
          image: "",
          linkURL: ""
        })];
        return new PartnerOffer("loading", offerLoadingData, "", "", "", "", false, "");
      }
    }]);
  }();
  var TenantOfferDetails = exports.TenantOfferDetails = /*#__PURE__*/(0, _createClass2.default)(function TenantOfferDetails(pageState, data) {
    (0, _classCallCheck2.default)(this, TenantOfferDetails);
    this.type = pageState;
    this.offerText = data.ribbonText ? data.ribbonText : "";
    this.tenantName = data.tenantName ? data.tenantName : "";
    this.offerTitle = data.offerTitle ? data.offerTitle : "";
    this.validityDate = data.validityDate ? data.validityDate : "";
    this.freeText = data.subCopy ? data.subCopy : "";
    this.remainingQuantity = data.remainingQuantity;
    this.totalQuantity = data.totalQuantity;
    this.imageUrl = data.image ? data.image : "";
    this.linkURL = data.linkURL ? data.linkURL : "";
    this.offerMechanic = data.subCopy ? _tenantOffer.TenantOfferMechanicEnum.freeText : _tenantOffer.TenantOfferMechanicEnum.validityDate;
  });
  var RestaurantBlogsAndReviews = exports.RestaurantBlogsAndReviews = /*#__PURE__*/(0, _createClass2.default)(function RestaurantBlogsAndReviews(data, hasError, errorPayload) {
    (0, _classCallCheck2.default)(this, RestaurantBlogsAndReviews);
    this.errorPayload = errorPayload;
    this.hasError = hasError;
    this.data = data;
  });
  var RestaurantChangiRewardsData = exports.RestaurantChangiRewardsData = /*#__PURE__*/(0, _createClass2.default)(function RestaurantChangiRewardsData(data, hasError, errorPayload) {
    (0, _classCallCheck2.default)(this, RestaurantChangiRewardsData);
    this.data = data;
    this.hasError = hasError;
    this.errorPayload = errorPayload;
  });
  var ChangiRewards = exports.ChangiRewards = /*#__PURE__*/(0, _createClass2.default)(function ChangiRewards(changiRewardsType, changiRewardsData) {
    (0, _classCallCheck2.default)(this, ChangiRewards);
    this.rewardTitle = changiRewardsData == null ? undefined : changiRewardsData.title;
    this.rewardText = changiRewardsData == null ? undefined : changiRewardsData.text;
    this.rewardLinkText = changiRewardsData == null ? undefined : changiRewardsData.linkText;
    this.rewardLink = changiRewardsData == null ? undefined : changiRewardsData.link;
    this.rewardJsonFile = changiRewardsData == null ? undefined : changiRewardsData.icon;
    this.type = changiRewardsType;
    this.rewardState = _infoBanner.InfoBannerState.permanent;
  });
  var RestaurantShopDetails = exports.RestaurantShopDetails = /*#__PURE__*/(0, _createClass2.default)(function RestaurantShopDetails(data) {
    (0, _classCallCheck2.default)(this, RestaurantShopDetails);
    this.title = data.title;
    this.tags = data.tags;
    this.locationDetails = data.locationDetails;
    this.foodPrice = data.price;
    this.heroCarouselImage = data == null ? undefined : data.heroCarouselImage;
    this.description = data == null ? undefined : data.description;
    this.heroCarouselLogo = data == null ? undefined : data.heroCarouselLogo;
    this.websiteViewLink = data.websiteLink;
    this.viewMenuLink = data.viewMenuURL;
    this.buttonLink = data.bookTableCTA;
    this.iSCAvailability = data.iSCAvailability;
    this.iSCURL = data.iSCURL;
    this.localRef = data.localRef;
    this.exploreCategories = data.exploreCategories;
    this.chopeUrl = data.chopeUrl;
    this.aemTenantDetails = data == null ? undefined : data.aemTenantDetails;
  });
  var DineShopDetails = exports.DineShopDetails = /*#__PURE__*/(0, _createClass2.default)(function DineShopDetails(data) {
    (0, _classCallCheck2.default)(this, DineShopDetails);
    this.offerID = data == null ? undefined : data.offerID;
    this.tenantID = data == null ? undefined : data.tenantID;
    this.heroCarouselImage = data == null ? undefined : data.heroCarouselImage;
    this.heroCarouselLogo = data == null ? undefined : data.heroCarouselLogo;
    this.title = data.title;
    this.tags = data.tags;
    this.locationDetails = data.locationDetails;
    this.buttonLink = data.buttonLink;
    this.buttonLabel = data.buttonLabel;
    this.websiteLink = data.websiteLink;
  });
  var DineShopDetailsLocationList = exports.DineShopDetailsLocationList = /*#__PURE__*/(0, _createClass2.default)(function DineShopDetailsLocationList(data) {
    (0, _classCallCheck2.default)(this, DineShopDetailsLocationList);
    this.locationMainTitle = data.locationMainTitle;
    this.location = data.location;
    this.openCloseStatus = data.openCloseStatus;
    this.openingHours = data.openingHours;
    this.contactNumber = data.contactNumber;
    this.title = data.title;
    this.websiteLink = data.websiteLink;
    this.viewMenuLink = data.menuLink;
    this.description = data.description;
    this.isExpanded = data.isExpanded;
    this.terminal = data.terminal;
    this.unitNo = data.unitNo;
    this.localRef = data.localRef;
  });
  var LocationList = exports.LocationList = /*#__PURE__*/(0, _createClass2.default)(function LocationList(data, details) {
    (0, _classCallCheck2.default)(this, LocationList);
    this.area = data.area;
    this.description = data.description;
    this.hourComment = data.hourComment;
    this.isExpanded = data.isExpanded;
    this.mapName = data.mapName;
    this.openCloseStatus = data.openCloseStatus;
    this.phoneDetails = data.contact;
    this.terminal = data.terminal;
    this.timingsInfo = data.timingsInfo;
    this.unitNo = data.unitNo;
    this.buttonLink = details.bookTableCTA;
    this.iSCAvailability = details.iSCAvailability;
    this.iSCURL = details.iSCURL;
    this.localRef = details.localRef;
    this.locationDetails = details.locationDetails;
    this.viewMenuLink = details.viewMenuURL;
    this.websiteViewLink = details.websiteLink;
  });
  var SpotlightBrandOffer = exports.SpotlightBrandOffer = /*#__PURE__*/function (_DineShopBaseClass0) {
    function SpotlightBrandOffer(products, isTenantNameAvailable, logo, sectionTitle, sectionSubTitle, type, errorFlag, errorPayload) {
      var _this0;
      (0, _classCallCheck2.default)(this, SpotlightBrandOffer);
      _this0 = _callSuper(this, SpotlightBrandOffer);
      _this0.products = products;
      _this0.isTenantNameAvailable = isTenantNameAvailable;
      _this0.errorFlag = errorFlag;
      _this0.errorPayload = errorPayload;
      _this0.logo = logo || "";
      _this0.sectionTitle = sectionTitle || "";
      _this0.sectionSubTitle = sectionSubTitle || "";
      _this0.type = type;
      return _this0;
    }
    (0, _inherits2.default)(SpotlightBrandOffer, _DineShopBaseClass0);
    return (0, _createClass2.default)(SpotlightBrandOffer, [{
      key: "failure",
      value: function failure() {
        return new SpotlightBrandOffer([], true, undefined, undefined, undefined, _productOffer.ProductOfferType.error, true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new SpotlightBrandOffer((0, _toConsumableArray2.default)(Array.from(Array(3)).map(function () {
          return SpotlightBrandOffer.productsConvertDTO({}, _productOffer.ProductOfferType.loading);
        })), true, undefined, undefined, undefined, _productOffer.ProductOfferType.loading, false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload9 = action.payload,
          data = _action$payload9.data,
          errors = _action$payload9.errors;
        if ((data == null ? undefined : data.getShopOfferSwimLane) === null && (errors || errors.length > 0)) {
          var extractErrors = errors.length > 0 ? SpotlightBrandOffer.checkErrors("getShopOfferSwimLane", errors) : [true, ""];
          return new SpotlightBrandOffer([], true, undefined, undefined, undefined, _productOffer.ProductOfferType.error, extractErrors[0], extractErrors[1]);
        } else {
          var _spotLightBrandOfferD;
          var spotLightBrandOfferData = data == null ? undefined : data.getShopOfferSwimLane;
          var dataHolder = spotLightBrandOfferData != null && spotLightBrandOfferData.products && (spotLightBrandOfferData == null || (_spotLightBrandOfferD = spotLightBrandOfferData.products) == null ? undefined : _spotLightBrandOfferD.length) > 0 ? spotLightBrandOfferData.products.map(function (item) {
            return SpotlightBrandOffer.productsConvertDTO(item, _productOffer.ProductOfferType.default);
          }) : [];
          var isTenantName = dataHolder.map(function (item) {
            return item.isTenantName;
          }).includes(true);
          return new SpotlightBrandOffer(dataHolder, isTenantName, spotLightBrandOfferData == null ? undefined : spotLightBrandOfferData.logo, spotLightBrandOfferData == null ? undefined : spotLightBrandOfferData.sectionTitle, spotLightBrandOfferData == null ? undefined : spotLightBrandOfferData.sectionSubTitle, _productOffer.ProductOfferType.default, false, []);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  SpotlightBrandOffer.productsConvertDTO = function (item, type) {
    if (item) {
      return {
        productID: item.productID,
        type: type,
        imageUrl: item.image,
        tenantName: item.brandName,
        ribbonText: item.ribbonText,
        productName: item.productName,
        salePrice: item.salePrice,
        originalPrice: item.mainPrice,
        iSCLink: item.iSCLink,
        isTenantName: !!item.brandName,
        navigationType: item.navigationType,
        navigationValue: item.navigationValue,
        redirect: item.redirect
      };
    } else {
      return {
        type: type,
        productName: undefined,
        originalPrice: undefined
      };
    }
  };
  var NotToBeMissed = exports.NotToBeMissed = /*#__PURE__*/function (_DineShopBaseClass1) {
    function NotToBeMissed(tenantOffer, hasError, errorPayload) {
      var _this1;
      (0, _classCallCheck2.default)(this, NotToBeMissed);
      _this1 = _callSuper(this, NotToBeMissed);
      _this1.data = tenantOffer;
      _this1.errorFlag = hasError;
      _this1.errorPayload = errorPayload;
      return _this1;
    }
    (0, _inherits2.default)(NotToBeMissed, _DineShopBaseClass1);
    return (0, _createClass2.default)(NotToBeMissed, [{
      key: "failure",
      value: function failure() {
        return new NotToBeMissed([], true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new NotToBeMissed((0, _toConsumableArray2.default)(Array.from(Array(3)).map(function () {
          return NotToBeMissed.convertTenantOfferDto({}, _tenantOffer.TenantTypeEnum.loading);
        })), false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload0 = action.payload,
          data = _action$payload0.data,
          errors = _action$payload0.errors;
        if (data && data.getNotMissedOffers === null && errors.length > 0) {
          var extractErrors = NotToBeMissed.checkErrors("getNotMissedOffers", errors);
          return new NotToBeMissed([], extractErrors[0], extractErrors[1]);
        } else {
          var _data$getNotMissedOff;
          var dataNotToBeMissed = [];
          dataNotToBeMissed = (data == null || (_data$getNotMissedOff = data.getNotMissedOffers) == null ? undefined : _data$getNotMissedOff.length) > 0 ? data.getNotMissedOffers.map(function (item) {
            return NotToBeMissed.convertTenantOfferDto(item, _tenantOffer.TenantTypeEnum.default);
          }) : [];
          return new NotToBeMissed(dataNotToBeMissed, false, []);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  NotToBeMissed.convertTenantOfferDto = function (item, type) {
    if (item) {
      var _item$isReedeemable;
      return {
        type: type,
        offerText: item.ribbonText,
        tenantName: item.tenantName,
        freeText: item.subCopy,
        imageUrl: item.image,
        linkURL: item.linkURL,
        offerTitle: item.offerTitle,
        isRedeemable: ((_item$isReedeemable = item.isReedeemable) == null ? undefined : _item$isReedeemable.toLowerCase()) === redeemable,
        validityDate: item.expiryDate,
        offerMechanic: _tenantOffer.TenantOfferMechanicEnum.cta
      };
    } else {
      return {
        type: type
      };
    }
  };
  var DineFilterResultData = exports.DineFilterResultData = /*#__PURE__*/function (_DineShopBaseClass10) {
    function DineFilterResultData(tenants, pageInfo, totalCount, errorFlag, errorPayload, hasNextPage) {
      var _this10;
      (0, _classCallCheck2.default)(this, DineFilterResultData);
      _this10 = _callSuper(this, DineFilterResultData);
      _this10.data = tenants;
      _this10.pageInfo = pageInfo;
      _this10.totalCount = totalCount;
      _this10.errorFlag = errorFlag;
      _this10.errorPayload = errorPayload;
      _this10.hasNextPage = hasNextPage;
      return _this10;
    }
    (0, _inherits2.default)(DineFilterResultData, _DineShopBaseClass10);
    return (0, _createClass2.default)(DineFilterResultData, [{
      key: "failure",
      value: function failure() {
        return new DineFilterResultData([], {
          endCursor: "",
          startCursor: "",
          hasPreviousPage: false,
          hasNextPage: false
        }, 0, true, [], false);
      }
    }, {
      key: "request",
      value: function request() {
        return new DineFilterResultData((0, _toConsumableArray2.default)(Array.from(Array(4)).map(function () {
          return convertTenantListingHorizontal({}, _tenantListingHorizontal.TenantListingHorizontalType.loading);
        })), {
          endCursor: "",
          startCursor: "",
          hasPreviousPage: false,
          hasNextPage: false
        }, 0, false, [], false);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload1 = action.payload,
          data = _action$payload1.data,
          errors = _action$payload1.errors;
        if ((data == null ? undefined : data.getResultScreenByFilterDine_v2) === null && errors.length > 0) {
          var extractErrors = DineFilterResultData.checkErrors("getResultScreenByFilterDine_v2", errors);
          return new DineFilterResultData([], extractErrors[0], extractErrors[1]);
        } else {
          var _data$getResultScreen4, _data$getResultScreen5, _data$getResultScreen6;
          var dataResult = [];
          dataResult = (data == null || (_data$getResultScreen4 = data.getResultScreenByFilterDine_v2) == null || (_data$getResultScreen4 = _data$getResultScreen4.nodes) == null ? undefined : _data$getResultScreen4.length) > 0 ? Array.isArray(data.getResultScreenByFilterDine_v2.nodes) && data.getResultScreenByFilterDine_v2.nodes.map(function (item) {
            return convertTenantListingHorizontal(item, _tenantListingHorizontal.TenantListingHorizontalType.default);
          }) : [];
          var resultPageInfo = data != null && (_data$getResultScreen5 = data.getResultScreenByFilterDine_v2) != null && _data$getResultScreen5.pageInfo ? {
            endCursor: data.getResultScreenByFilterDine_v2.pageInfo.endCursor,
            startCursor: data.getResultScreenByFilterDine_v2.pageInfo.startCursor,
            hasPreviousPage: data.getResultScreenByFilterDine_v2.pageInfo.hasPreviousPage,
            hasNextPage: data.getResultScreenByFilterDine_v2.pageInfo.hasNextPage
          } : {};
          var totalCount = data == null || (_data$getResultScreen6 = data.getResultScreenByFilterDine_v2) == null ? undefined : _data$getResultScreen6.totalCount;
          var hasNextPage = resultPageInfo == null ? undefined : resultPageInfo.hasNextPage;
          return new DineFilterResultData(dataResult, resultPageInfo, totalCount, false, [], hasNextPage);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  var PopularRestaurants = exports.PopularRestaurants = /*#__PURE__*/function (_DineShopBaseClass11) {
    function PopularRestaurants(tenants, errorFlag, errorPayload) {
      var _this11;
      (0, _classCallCheck2.default)(this, PopularRestaurants);
      _this11 = _callSuper(this, PopularRestaurants);
      _this11.data = tenants;
      _this11.errorFlag = errorFlag;
      _this11.errorPayload = errorPayload;
      return _this11;
    }
    (0, _inherits2.default)(PopularRestaurants, _DineShopBaseClass11);
    return (0, _createClass2.default)(PopularRestaurants, [{
      key: "failure",
      value: function failure() {
        return new PopularRestaurants([], true, []);
      }
    }, {
      key: "request",
      value: function request() {
        return new PopularRestaurants((0, _toConsumableArray2.default)(Array.from(Array(3)).map(function () {
          return PopularRestaurants.convertDto({}, _tenantListingHorizontal.TenantListingHorizontalType.loading);
        })), false, []);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload10 = action.payload,
          data = _action$payload10.data,
          errors = _action$payload10.errors;
        if ((data == null ? undefined : data.getTopRestaurantDefault) === null && errors.length > 0) {
          var extractErrors = PopularRestaurants.checkErrors("getTopRestaurantDefault", errors);
          return new PopularRestaurants([], extractErrors[0], extractErrors[1]);
        } else {
          var _data$getTopRestauran;
          var dataHolder = [];
          dataHolder = (data == null || (_data$getTopRestauran = data.getTopRestaurantDefault) == null ? undefined : _data$getTopRestauran.length) > 0 ? Array.isArray(data.getTopRestaurantDefault) && data.getTopRestaurantDefault.map(function (item) {
            return PopularRestaurants.convertDto(item, _tenantListingHorizontal.TenantListingHorizontalType.default);
          }) : [];
          return new PopularRestaurants(dataHolder, false, []);
        }
      }
    }]);
  }(_dineShopBaseClass.DineShopBaseClass);
  PopularRestaurants.convertDto = function (item, type) {
    if (item) {
      var _item$aemTenantDetail12, _item$aemTenantDetail13, _item$aemTenantDetail14, _item$aemTenantDetail15, _item$aemTenantDetail16;
      return {
        location: Tenants.handleExploreMoreLocation(item.location_display, item == null || (_item$aemTenantDetail12 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail12.categories, item == null || (_item$aemTenantDetail13 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail13.price),
        imageUrlArray: item == null || (_item$aemTenantDetail14 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail14.additionalTileImages,
        tenantName: item.title,
        rewardTitle: item == null || (_item$aemTenantDetail15 = item.aemTenantDetails) == null || (_item$aemTenantDetail15 = _item$aemTenantDetail15.rewards) == null ? undefined : _item$aemTenantDetail15.title,
        logoUrl: item == null || (_item$aemTenantDetail16 = item.aemTenantDetails) == null ? undefined : _item$aemTenantDetail16.logoImage,
        tenentId: item.id,
        type: type
      };
    }
    return {
      location: undefined,
      imageUrlArray: undefined,
      logoUrl: undefined,
      tenantName: undefined,
      rewardTitle: undefined,
      tenentId: undefined,
      type: type
    };
  };
  var DineReservationList = exports.DineReservationList = /*#__PURE__*/function () {
    function DineReservationList() {
      (0, _classCallCheck2.default)(this, DineReservationList);
    }
    return (0, _createClass2.default)(DineReservationList, [{
      key: "success",
      value:
      /**
       *onPressed,
        label,
        title,
        icon,
        style,
        imageImport,
        disabled,
        isCustomIcon,
        testID = "ShortcutLink",
        accessibilityLabel = "ShortcutLink",
        ////////////////////////////////////////////////////////////////////////////////
        buttonLabel: null
        icon: "http://ec2-13-250-229-20.ap-southeast-1.compute.amazonaws.com/content/dam/ichangi/images/module-for-you/shortcut-links-images/qlink_TrackBaggage.png"
        label: ""
        navigationType: "deep-link"
        navigationValue: "baggage_tracker"
        sequenceNumber: "4"
        title: "Track Baggage"
        url: null
       */
      function success(_payload) {
        return [];
      }
    }]);
  }();
