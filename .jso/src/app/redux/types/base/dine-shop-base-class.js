  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DineShopBaseClass = undefined;
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var DineShopBaseClass = exports.DineShopBaseClass = /*#__PURE__*/(0, _createClass2.default)(function DineShopBaseClass() {
    (0, _classCallCheck2.default)(this, DineShopBaseClass);
  });
  DineShopBaseClass.checkErrors = function (pathName, errors) {
    var extractErrors = errors.find(function (err) {
      return err.path.includes(pathName);
    });
    if (Object.keys(extractErrors).length > 0) {
      return [true, extractErrors.message];
    }
    return [false, ""];
  };
