  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlyBaseClass = undefined;
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var FlyBaseClass = exports.FlyBaseClass = /*#__PURE__*/(0, _createClass2.default)(function FlyBaseClass() {
    (0, _classCallCheck2.default)(this, FlyBaseClass);
  });
  FlyBaseClass.checkErrors = function (pathName, errors) {
    var extractErrors = errors.find(function (err) {
      return err.path.includes(pathName);
    });
    if (Object.keys(extractErrors).length > 0) {
      return [true, extractErrors.message];
    }
    return [false, ""];
  };
