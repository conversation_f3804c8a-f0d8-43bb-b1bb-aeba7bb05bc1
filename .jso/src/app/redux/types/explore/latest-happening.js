  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LatestHappenings = exports.LatestHappeningDetail = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _latestHappening = _$$_REQUIRE(_dependencyMap[7]);
  var _exploreBaseClass = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _latestHappeningsProps = _$$_REQUIRE(_dependencyMap[10]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[11]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var LatestHappeningDetail = exports.LatestHappeningDetail = /*#__PURE__*/(0, _createClass2.default)(function LatestHappeningDetail(data) {
    (0, _classCallCheck2.default)(this, LatestHappeningDetail);
    this.gradientInfo = data.gradientInfo;
    this.orientation = data.orientation;
    this.imageUrl = data.imageUrl;
    this.title = data.title;
    this.subtitle = data.subtitle;
    this.campaignCode = data.campaignCode;
    this.packageCode = data.packageCode;
    this.navigationType = data.navigationType;
    this.navigationValue = data.navigationValue;
    this.offerId = data.offerId;
    this.sequenceNumber = data.sequenceNumber;
    this.redirectToShowOnListing = data.redirectToShowOnListing;
    this.tabName = data.tabName;
    this.fragmentTitle = data.fragmentTitle;
  });
  var LatestHappenings = exports.LatestHappenings = /*#__PURE__*/function (_ExploreBaseClass) {
    function LatestHappenings(latestHappenings, hasError) {
      var _this;
      (0, _classCallCheck2.default)(this, LatestHappenings);
      _this = _callSuper(this, LatestHappenings);
      _this.data = latestHappenings;
      _this.hasError = hasError;
      return _this;
    }
    (0, _inherits2.default)(LatestHappenings, _ExploreBaseClass);
    return (0, _createClass2.default)(LatestHappenings, [{
      key: "request",
      value: function request() {
        return LatestHappenings.getLoadingData();
      }
    }, {
      key: "success",
      value: function success(action) {
        var latestHappeningsPayload = action.payload;
        var isFeatureFlagON = action.isFeatureFlagON;
        var latestHappeningsData = {
          data: []
        };
        (0, _lodash.isArray)(latestHappeningsPayload) && (latestHappeningsPayload == null ? undefined : latestHappeningsPayload.forEach(function (latestHappening) {
          if (latestHappening.navigationValue === _navigationHelper.NavigationValueDeepLink.appscapade && !isFeatureFlagON) {
            return;
          }
          if (latestHappening.orientation === _latestHappening.LatestHappeningOrientation.tall) {
            latestHappeningsData.data.push(latestHappening);
          } else {
            var latestHappeningsLastDataIndex = latestHappeningsData.data.length - 1;
            var latestHappeningsLastData = latestHappeningsData.data[latestHappeningsLastDataIndex];
            var isLatestHappenningsDataArray = (0, _lodash.isArray)(latestHappeningsLastData);
            if (!isLatestHappenningsDataArray) {
              latestHappeningsData.data.push([latestHappening]);
            } else {
              if (latestHappeningsLastData.length < _latestHappeningsProps.HORIZONTAL_TILES_COUNT) {
                latestHappeningsLastData.push(latestHappening);
              } else {
                latestHappeningsData.data.push([latestHappening]);
              }
            }
          }
        }));
        return latestHappeningsData;
      }
    }, {
      key: "failure",
      value: function failure() {
        return {
          data: [],
          hasError: true
        };
      }
    }], [{
      key: "getLoadingData",
      value: function getLoadingData() {
        return {
          data: [new LatestHappeningDetail({
            gradientInfo: _latestHappening.LatestHappeningType.loading,
            orientation: _latestHappening.LatestHappeningOrientation.tall
          }), (0, _toConsumableArray2.default)(Array(_latestHappeningsProps.HORIZONTAL_TILES_COUNT).keys()).map(function () {
            return new LatestHappeningDetail({
              gradientInfo: _latestHappening.LatestHappeningType.loading,
              orientation: _latestHappening.LatestHappeningOrientation.short
            });
          })]
        };
      }
    }]);
  }(_exploreBaseClass.ExploreBaseClass);
