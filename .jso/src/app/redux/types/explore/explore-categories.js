  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ExploreCategoryEnum = exports.ExploreCategoryDetail = exports.ExploreCategories = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _tabBarButton = _$$_REQUIRE(_dependencyMap[7]);
  var _exploreBaseClass = _$$_REQUIRE(_dependencyMap[8]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var ExploreCategoryEnum = exports.ExploreCategoryEnum = /*#__PURE__*/function (ExploreCategoryEnum) {
    ExploreCategoryEnum["all"] = "All";
    ExploreCategoryEnum["attractions"] = "Attractions";
    ExploreCategoryEnum["events"] = "Events";
    ExploreCategoryEnum["familyKids"] = "Family & Kids";
    ExploreCategoryEnum["outdoor"] = "Outdoor";
    return ExploreCategoryEnum;
  }({});
  var ExploreCategoryDetail = exports.ExploreCategoryDetail = /*#__PURE__*/(0, _createClass2.default)(function ExploreCategoryDetail(data, type) {
    (0, _classCallCheck2.default)(this, ExploreCategoryDetail);
    this.type = type || _tabBarButton.TabBarButtonType.unSelected;
    this.text = data == null ? undefined : data.tabName;
  });
  var MWExploreCategoryDetail = /*#__PURE__*/(0, _createClass2.default)(function MWExploreCategoryDetail() {
    (0, _classCallCheck2.default)(this, MWExploreCategoryDetail);
  });
  var ExploreCategories = exports.ExploreCategories = /*#__PURE__*/function (_ExploreBaseClass) {
    function ExploreCategories(exploreCategories, hasError) {
      var _this;
      (0, _classCallCheck2.default)(this, ExploreCategories);
      _this = _callSuper(this, ExploreCategories);
      _this.data = exploreCategories;
      _this.hasError = hasError;
      return _this;
    }
    (0, _inherits2.default)(ExploreCategories, _ExploreBaseClass);
    return (0, _createClass2.default)(ExploreCategories, [{
      key: "request",
      value: function request() {
        return Object.assign({}, ExploreCategories.getLoadingData(), {
          isLoading: true
        });
      }
    }, {
      key: "success",
      value: function success(action) {
        var _action$payload;
        var exploreCategories = (action == null || (_action$payload = action.payload) == null || (_action$payload = _action$payload.getExploreChangi) == null ? undefined : _action$payload.categories) || [];
        var exploreCategoriesData = exploreCategories == null ? undefined : exploreCategories.map(function (exploreCategory, index) {
          return {
            id: index,
            text: exploreCategory == null ? undefined : exploreCategory.category,
            value: exploreCategory == null ? undefined : exploreCategory.categoryCode,
            type: index === 0 ? _tabBarButton.TabBarButtonType.selected : _tabBarButton.TabBarButtonType.unSelected
          };
        });
        return {
          data: exploreCategoriesData,
          isLoading: false
        };
      }
    }, {
      key: "failure",
      value: function failure() {
        return {
          data: [],
          hasError: true,
          isLoading: false
        };
      }
    }, {
      key: "updateSelected",
      value: function updateSelected(state, action) {
        var _action$payload2;
        var category = action == null || (_action$payload2 = action.payload) == null ? undefined : _action$payload2.text;
        var exploreCategories = state.exploreCategoriesPayload.data;
        return {
          data: exploreCategories == null ? undefined : exploreCategories.map(function (exploreCategory) {
            return Object.assign({}, exploreCategory, {
              type: exploreCategory.text === category ? _tabBarButton.TabBarButtonType.selected : _tabBarButton.TabBarButtonType.unSelected
            });
          })
        };
      }
    }, {
      key: "sortActionData",
      value: function sortActionData(action) {
        var _action$payload3;
        var exploreCategories = (action == null || (_action$payload3 = action.payload) == null || (_action$payload3 = _action$payload3.data) == null || (_action$payload3 = _action$payload3.getExploreCategories) == null ? undefined : _action$payload3.exploreCategories) || [];
        if (!exploreCategories) return action;
        exploreCategories.sort(function (a, b) {
          return parseFloat(a.sequenceNumber) - parseFloat(b.sequenceNumber);
        });
        var distinctExploreCategories = [];
        exploreCategories == null || exploreCategories.forEach(function (exploreCategory) {
          !distinctExploreCategories.find(function (exploreCategoryItem) {
            return exploreCategoryItem.tabName === exploreCategory.tabName;
          }) && distinctExploreCategories.push(exploreCategory);
        });
        action.payload.data.getExploreCategories.exploreCategories = distinctExploreCategories;
        return action;
      }
    }], [{
      key: "getLoadingData",
      value: function getLoadingData() {
        var loadingItem = new ExploreCategoryDetail(undefined, _tabBarButton.TabBarButtonType.loading);
        return new ExploreCategories((0, _toConsumableArray2.default)(Array(6).keys()).map(function () {
          return loadingItem;
        }));
      }
    }]);
  }(_exploreBaseClass.ExploreBaseClass);
