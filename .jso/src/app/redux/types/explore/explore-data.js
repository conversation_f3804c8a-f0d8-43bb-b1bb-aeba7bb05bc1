  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ExploreDataDetail = exports.ExploreData = undefined;
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _exploreBaseClass = _$$_REQUIRE(_dependencyMap[6]);
  var _exploreDataItem = _$$_REQUIRE(_dependencyMap[7]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var ExploreDataDetail = exports.ExploreDataDetail = /*#__PURE__*/(0, _createClass2.default)(function ExploreDataDetail() {
    (0, _classCallCheck2.default)(this, ExploreDataDetail);
  });
  var ExploreData = exports.ExploreData = /*#__PURE__*/function (_ExploreBaseClass) {
    function ExploreData(exploreData, _hasError) {
      var _this;
      (0, _classCallCheck2.default)(this, ExploreData);
      _this = _callSuper(this, ExploreData);
      _this.data = exploreData;
      return _this;
    }
    (0, _inherits2.default)(ExploreData, _ExploreBaseClass);
    return (0, _createClass2.default)(ExploreData, [{
      key: "request",
      value: function request(state) {
        return ExploreData.getLoadingData(state);
      }
    }, {
      key: "success",
      value: function success(action, state) {
        var _state$exploreDataPay;
        var _ref = action.payload || {},
          errors = _ref.errors,
          getExploreChangi = _ref.getExploreChangi;
        if ((errors == null ? undefined : errors.length) > 0) {
          return this.failure();
        }
        var paging = getExploreChangi == null ? undefined : getExploreChangi.paging;
        var exploreCategoriesDataState = (state == null || (_state$exploreDataPay = state.exploreDataPayload) == null ? undefined : _state$exploreDataPay.exploreCategoriesData) || [];
        var categoryData = (getExploreChangi == null ? undefined : getExploreChangi.dataByCategory) || [];
        var exploreCategoriesData = (paging == null ? undefined : paging.pageNumber) === 1 ? getExploreChangi == null ? undefined : getExploreChangi.dataByCategory : exploreCategoriesDataState.concat(categoryData);
        var uniqueNames = Array.from(new Set(exploreCategoriesData));
        return {
          isLoading: false,
          hasError: false,
          perkHeroImage: getExploreChangi == null ? undefined : getExploreChangi.perkHeroImage,
          heroImage: getExploreChangi == null ? undefined : getExploreChangi.heroImage,
          perkTexts: getExploreChangi == null ? undefined : getExploreChangi.perkTexts,
          isPerksAvailable: getExploreChangi == null ? undefined : getExploreChangi.isPerksAvailable,
          exploreCategoriesData: uniqueNames,
          paging: getExploreChangi == null ? undefined : getExploreChangi.paging
        };
      }
    }, {
      key: "failure",
      value: function failure(state) {
        var _state$exploreCategor;
        var selectedCategory = (0, _exploreDataItem.getSelectedCategoryItemText)(state == null || (_state$exploreCategor = state.exploreCategoriesPayload) == null ? undefined : _state$exploreCategor.data);
        var exploreData = (state == null ? undefined : state.exploreDataPayload) || {};
        if (exploreData != null && exploreData[selectedCategory]) {
          exploreData[selectedCategory].isLoading = false;
          exploreData[selectedCategory].hasError = true;
        }
        var exploreDataPageNumbers = (state == null ? undefined : state.exploreDataPageNumbers) || {};
        var pageNumber = exploreDataPageNumbers[selectedCategory];
        if (typeof pageNumber === "number") exploreDataPageNumbers[selectedCategory] -= 1;
        return Object.assign({}, state, exploreDataPageNumbers, {
          exploreDataPayload: Object.assign({}, exploreData)
        });
      }
    }], [{
      key: "getLoadingData",
      value: function getLoadingData(state) {
        var exploreData = (state == null ? undefined : state.exploreDataPayload) || {};
        if (exploreData) {
          exploreData.isLoading = true;
          exploreData.hasError = false;
        }
        return Object.assign({}, exploreData);
      }
    }]);
  }(_exploreBaseClass.ExploreBaseClass);
