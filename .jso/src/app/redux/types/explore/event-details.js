  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PROCESS_CRTRANSACTIONS_THRESHOLD = exports.MWEvent = exports.LocationType = exports.EventTokenBannerData = exports.EventStatusCodeSlotsAvailability = exports.EventStatusBannerData = exports.EventDetailsType = exports.EventDetailsData = exports.EventDetails = undefined;
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _calloutBanner = _$$_REQUIRE(_dependencyMap[6]);
  var _infoBanner = _$$_REQUIRE(_dependencyMap[7]);
  var _exploreBaseClass = _$$_REQUIRE(_dependencyMap[8]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[9]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  /**
   * Threshold time to call process CR Transactions. Default threshold set to 5 minutes(300 Seconds)
   */
  var PROCESS_CRTRANSACTIONS_THRESHOLD = exports.PROCESS_CRTRANSACTIONS_THRESHOLD = 300;
  var EventDetailsType = exports.EventDetailsType = /*#__PURE__*/function (EventDetailsType) {
    EventDetailsType["default"] = "default";
    EventDetailsType["loading"] = "loading";
    return EventDetailsType;
  }(EventDetailsType || {});
  var LocationType = exports.LocationType = /*#__PURE__*/function (LocationType) {
    LocationType["multipleLocations"] = "Multiple locations";
    return LocationType;
  }(LocationType || {});
  var EventStatusCodeSlotsAvailability = exports.EventStatusCodeSlotsAvailability = /*#__PURE__*/function (EventStatusCodeSlotsAvailability) {
    EventStatusCodeSlotsAvailability["fullyBooked"] = "404";
    EventStatusCodeSlotsAvailability["available"] = "200";
    EventStatusCodeSlotsAvailability["serverError"] = "500";
    return EventStatusCodeSlotsAvailability;
  }(EventStatusCodeSlotsAvailability || {});
  var EventDetails = exports.EventDetails = /*#__PURE__*/(0, _createClass2.default)(function EventDetails(data, type) {
    (0, _classCallCheck2.default)(this, EventDetails);
    this.categoryType = _exploreItemType.ExploreItemCategoryEnum.event;
    this.type = type || _exploreItemType.ExploreItemTypeEnum.default;
    this.packageCode = data == null ? undefined : data.packageCode;
    this.title = data == null ? undefined : data.title;
    this.imageThumbnail = data == null ? undefined : data.imageThumbnail;
    this.imageUrl = data == null ? undefined : data.imageListing;
    this.imageHero = data == null ? undefined : data.imageHero;
    this.tags = data == null ? undefined : data.tags;
    this.location = data == null ? undefined : data.location;
    this.eventStart = data == null ? undefined : data.eventStart;
    this.eventEnd = data == null ? undefined : data.eventEnd;
    this.dateDetails = data == null ? undefined : data.dateDetails;
    this.ticketPrices = data == null ? undefined : data.ticketPrices;
    this.aboutDetails = data == null ? undefined : data.aboutDetails;
    this.aboutLink = data == null ? undefined : data.aboutLink;
    this.entryRequirements = data == null ? undefined : data.entryRequirements;
    this.termAndConditions = data == null ? undefined : data.termAndConditions;
    this.sequenceNumber = data == null ? undefined : data.sequenceNumber;
    this.isPinned = data == null ? undefined : data.isPinned;
    this.bookingUrl = data == null ? undefined : data.bookingUrl;
  });
  var MWEvent = exports.MWEvent = /*#__PURE__*/function () {
    function MWEvent() {
      (0, _classCallCheck2.default)(this, MWEvent);
      this.categoryType = _exploreItemType.ExploreItemCategoryEnum.event;
    }
    return (0, _createClass2.default)(MWEvent, [{
      key: "mapData",
      value: function mapData(data) {
        return Object.assign({}, data, {
          categoryType: _exploreItemType.ExploreItemCategoryEnum.event
        });
      }
    }]);
  }();
  var EventDetailsData = exports.EventDetailsData = /*#__PURE__*/function (_ExploreBaseClass) {
    function EventDetailsData(type, data, hasError, errorPayload) {
      var _this;
      (0, _classCallCheck2.default)(this, EventDetailsData);
      _this = _callSuper(this, EventDetailsData);
      _this.eventDetails = data;
      _this.hasError = hasError;
      _this.errorPayload = errorPayload;
      _this.type = type;
      return _this;
    }
    (0, _inherits2.default)(EventDetailsData, _ExploreBaseClass);
    return (0, _createClass2.default)(EventDetailsData, [{
      key: "request",
      value: function request() {
        return new EventDetailsData(EventDetailsType.loading, undefined, false);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _data$getPackageDetai;
        var _ref = action.payload || {},
          data = _ref.data,
          errors = _ref.errors;
        if ((errors == null ? undefined : errors.length) > 0) {
          return this.failure();
        }
        var mwEventDetails = data == null || (_data$getPackageDetai = data.getPackageDetails) == null ? undefined : _data$getPackageDetai.data;
        return new EventDetailsData(EventDetailsType.default, mwEventDetails ? new EventDetails(mwEventDetails) : undefined, false);
      }
    }, {
      key: "failure",
      value: function failure() {
        return new EventDetailsData(undefined, undefined, true);
      }
    }]);
  }(_exploreBaseClass.ExploreBaseClass);
  var EventStatusBanner = /*#__PURE__*/(0, _createClass2.default)(function EventStatusBanner(data) {
    (0, _classCallCheck2.default)(this, EventStatusBanner);
    this.iconUrl = data == null ? undefined : data.iconUrl;
    this.colorContentCode = data == null ? undefined : data.colorContentCode;
    this.colorLayoutCode = data == null ? undefined : data.colorLayoutCode;
    this.mainMessage = data == null ? undefined : data.messageBold;
    this.subMessage = data == null ? undefined : data.messageNormal;
    this.isDisplay = data == null ? undefined : data.isDisplay;
  });
  var EventStatusBannerData = exports.EventStatusBannerData = /*#__PURE__*/function (_ExploreBaseClass2) {
    function EventStatusBannerData(type, eventStatusBanner, hasError, statusCodeSlotsAvailability) {
      var _this2;
      (0, _classCallCheck2.default)(this, EventStatusBannerData);
      _this2 = _callSuper(this, EventStatusBannerData);
      _this2.type = type;
      _this2.eventStatus = eventStatusBanner;
      _this2.hasError = hasError;
      _this2.statusCodeSlotsAvailability = statusCodeSlotsAvailability;
      return _this2;
    }
    (0, _inherits2.default)(EventStatusBannerData, _ExploreBaseClass2);
    return (0, _createClass2.default)(EventStatusBannerData, [{
      key: "request",
      value: function request() {
        return new EventStatusBannerData(_calloutBanner.CalloutBannerType.loading, null, false, undefined);
      }
    }, {
      key: "success",
      value: function success(action) {
        var bannerStatus = action.bannerStatus,
          statusCheckSlotsAvailability = action.statusCheckSlotsAvailability;
        var bannerStatusData = Array.isArray(bannerStatus) && (bannerStatus == null ? undefined : bannerStatus.length) > 0 ? bannerStatus[0] : {};
        return new EventStatusBannerData(_calloutBanner.CalloutBannerType.default, bannerStatusData, false, statusCheckSlotsAvailability == null ? undefined : statusCheckSlotsAvailability.statusCode);
      }
    }, {
      key: "failure",
      value: function failure() {
        return new EventStatusBannerData(_calloutBanner.CalloutBannerType.default, null, true, undefined);
      }
    }]);
  }(_exploreBaseClass.ExploreBaseClass);
  var EventTokenBanner = /*#__PURE__*/(0, _createClass2.default)(function EventTokenBanner(data) {
    (0, _classCallCheck2.default)(this, EventTokenBanner);
    this.iconUrl = data == null ? undefined : data.iconUrl;
    this.title = data == null ? undefined : data.title;
    this.description = data == null ? undefined : data.description;
    this.linkText = data == null ? undefined : data.linkText;
    this.linkUrl = data == null ? undefined : data.linkUrl;
    this.navigationType = data == null ? undefined : data.navigationType;
    this.navigationValue = data == null ? undefined : data.navigationValue;
  });
  var EventTokenBannerData = exports.EventTokenBannerData = /*#__PURE__*/function (_ExploreBaseClass3) {
    function EventTokenBannerData(type, eventBannerTokens, hasError) {
      var _this3;
      (0, _classCallCheck2.default)(this, EventTokenBannerData);
      _this3 = _callSuper(this, EventTokenBannerData);
      _this3.type = type;
      _this3.bannerToken = eventBannerTokens;
      _this3.hasError = hasError;
      return _this3;
    }
    (0, _inherits2.default)(EventTokenBannerData, _ExploreBaseClass3);
    return (0, _createClass2.default)(EventTokenBannerData, [{
      key: "request",
      value: function request() {
        return new EventTokenBannerData(_infoBanner.InfoBannerType.loading, null, false);
      }
    }, {
      key: "success",
      value: function success(action) {
        var bannerToken = action.bannerToken;
        var bannerTokenData = Array.isArray(bannerToken) && bannerToken.length > 0 ? bannerToken[0] : {};
        return new EventTokenBannerData(_infoBanner.InfoBannerType.default, bannerTokenData, false);
      }
    }, {
      key: "failure",
      value: function failure() {
        return new EventTokenBannerData(_infoBanner.InfoBannerType.default, null, true);
      }
    }]);
  }(_exploreBaseClass.ExploreBaseClass);
