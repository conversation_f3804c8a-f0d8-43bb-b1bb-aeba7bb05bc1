  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.MWAttraction = exports.Attraction = undefined;
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[3]);
  var Attraction = exports.Attraction = /*#__PURE__*/(0, _createClass2.default)(function Attraction(data, _type) {
    (0, _classCallCheck2.default)(this, Attraction);
    this.categoryType = _exploreItemType.ExploreItemCategoryEnum.attraction;
    this.attractionId = data == null ? undefined : data.attractionId;
    this.title = data == null ? undefined : data.title;
    this.imageUrl = data == null ? undefined : data.image;
    this.eventStart = data == null ? undefined : data.startDate;
    this.eventEnd = data == null ? undefined : data.expiryDate;
    this.operatingHours = data == null ? undefined : data.operatingHours;
    this.location = data == null ? undefined : data.location;
    this.sequenceNumber = data == null ? undefined : data.sequenceNumber;
    this.isPinned = data == null ? undefined : data.isPinned;
    this.navigation = data == null ? undefined : data.navigation;
  });
  var MWAttraction = exports.MWAttraction = /*#__PURE__*/function () {
    function MWAttraction() {
      (0, _classCallCheck2.default)(this, MWAttraction);
      this.categoryType = _exploreItemType.ExploreItemCategoryEnum.attraction;
    }
    return (0, _createClass2.default)(MWAttraction, [{
      key: "mapData",
      value: function mapData(data) {
        return Object.assign({}, data, {
          categoryType: _exploreItemType.ExploreItemCategoryEnum.attraction
        });
      }
    }]);
  }();
