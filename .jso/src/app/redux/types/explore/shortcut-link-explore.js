  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ShortcutLinksExploreData = exports.ShortcutLinksExplore = undefined;
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _exploreBaseClass = _$$_REQUIRE(_dependencyMap[6]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var ShortcutLinks = /*#__PURE__*/(0, _createClass2.default)(function ShortcutLinks(data) {
    (0, _classCallCheck2.default)(this, ShortcutLinks);
    this.buttonLabel = data == null ? undefined : data.buttonLabel;
    this.icon = data == null ? undefined : data.icon;
    this.navigationValue = data == null ? undefined : data.navigationValue;
    this.label = data == null ? undefined : data.label;
    this.title = data == null ? undefined : data.title;
    this.url = data == null ? undefined : data.url;
    this.sequenceNumber = data == null ? undefined : data.sequenceNumber;
    this.navigationType = data == null ? undefined : data.navigationType;
  });
  var ShortcutLinksExploreData = exports.ShortcutLinksExploreData = /*#__PURE__*/(0, _createClass2.default)(function ShortcutLinksExploreData(data) {
    (0, _classCallCheck2.default)(this, ShortcutLinksExploreData);
    this.shortcutLinks = data == null ? undefined : data.shortcutLinks;
    this.userType = data == null ? undefined : data.userType;
  });
  var ShortcutLinksExplore = exports.ShortcutLinksExplore = /*#__PURE__*/function (_ExploreBaseClass) {
    function ShortcutLinksExplore(eventConfirmationDetail, hasError, isLoading) {
      var _this;
      (0, _classCallCheck2.default)(this, ShortcutLinksExplore);
      _this = _callSuper(this, ShortcutLinksExplore);
      _this.data = eventConfirmationDetail;
      _this.hasError = hasError;
      _this.isLoading = isLoading;
      return _this;
    }
    (0, _inherits2.default)(ShortcutLinksExplore, _ExploreBaseClass);
    return (0, _createClass2.default)(ShortcutLinksExplore, [{
      key: "request",
      value: function request() {
        return new ShortcutLinksExplore(undefined, false, true);
      }
    }, {
      key: "success",
      value: function success(action) {
        var _ref = action.payload || {},
          _ref$getShortcutLinks = _ref.getShortcutLinksExplore,
          getShortcutLinksExplore = _ref$getShortcutLinks === undefined ? {} : _ref$getShortcutLinks,
          errors = _ref.errors;
        if ((errors == null ? undefined : errors.length) > 0) {
          return this.failure();
        }
        if (getShortcutLinksExplore) {
          return new ShortcutLinksExplore(getShortcutLinksExplore, false, false);
        }
        return new ShortcutLinksExplore(undefined, false, false);
      }
    }, {
      key: "failure",
      value: function failure() {
        return new ShortcutLinksExplore(undefined, true, false);
      }
    }]);
  }(_exploreBaseClass.ExploreBaseClass);
