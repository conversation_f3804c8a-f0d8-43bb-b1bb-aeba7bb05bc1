  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.BookingPageUrlDetail = exports.BookingPageUrl = undefined;
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _exploreBaseClass = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var BookingPageUrlDetail = exports.BookingPageUrlDetail = /*#__PURE__*/(0, _createClass2.default)(function BookingPageUrlDetail(data) {
    (0, _classCallCheck2.default)(this, BookingPageUrlDetail);
    this.editBookingPageUrl = data == null ? undefined : data.editBookingPageUrl;
    this.timetsMessage = data == null ? undefined : data.timetsMessage;
    this.labelGoToCart = data == null ? undefined : data.labelGoToCart;
    this.timeTs = data == null ? undefined : data.timeTs;
  });
  var BookingPageUrl = exports.BookingPageUrl = /*#__PURE__*/function (_ExploreBaseClass) {
    function BookingPageUrl(bookingPageUrlDetail, hasError, isLoading) {
      var _this;
      (0, _classCallCheck2.default)(this, BookingPageUrl);
      _this = _callSuper(this, BookingPageUrl);
      _this.data = bookingPageUrlDetail;
      _this.hasError = hasError;
      _this.isLoading = isLoading;
      return _this;
    }
    (0, _inherits2.default)(BookingPageUrl, _ExploreBaseClass);
    return (0, _createClass2.default)(BookingPageUrl, [{
      key: "request",
      value: function request() {
        return new BookingPageUrl(undefined, false, true);
      }
    }, {
      key: "success",
      value: function success(action) {
        var isError = false;
        var isLoading = false;
        var _ref = action.payload || {},
          data = _ref.data,
          errors = _ref.errors;
        if ((errors == null ? undefined : errors.length) > 0) {
          return this.failure();
        }
        var _ref2 = (data == null ? undefined : data.getEditBookingPageUrl) || {},
          editBookingPageUrls = _ref2.editBookingPageUrls;
        if (editBookingPageUrls && (0, _lodash.isArray)(editBookingPageUrls) && editBookingPageUrls != null && editBookingPageUrls.length) {
          return new BookingPageUrl(editBookingPageUrls == null ? undefined : editBookingPageUrls[0], isError, isLoading);
        }
        return new BookingPageUrl(undefined, isError, isLoading);
      }
    }, {
      key: "failure",
      value: function failure() {
        return new BookingPageUrl(undefined, true, false);
      }
    }]);
  }(_exploreBaseClass.ExploreBaseClass);
