  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.UpcomingEvent = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _i18n = _$$_REQUIRE(_dependencyMap[7]);
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[9]);
  var _lodash = _$$_REQUIRE(_dependencyMap[10]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var UpcomingEventClass = /*#__PURE__*/(0, _createClass2.default)(function UpcomingEventClass() {
    (0, _classCallCheck2.default)(this, UpcomingEventClass);
  });
  var handleDescription = function handleDescription(item) {
    if ((item == null ? undefined : item.direction) === "DEP") {
      return `${item == null ? undefined : item.flightNumber} to ${item == null ? undefined : item.destinationPlace}`;
    }
    return `${item == null ? undefined : item.flightNumber} from ${item == null ? undefined : item.departingPlace}`;
  };
  var UpcomingEvent = exports.UpcomingEvent = /*#__PURE__*/function (_UpcomingEventClass) {
    function UpcomingEvent() {
      (0, _classCallCheck2.default)(this, UpcomingEvent);
      return _callSuper(this, UpcomingEvent, arguments);
    }
    (0, _inherits2.default)(UpcomingEvent, _UpcomingEventClass);
    return (0, _createClass2.default)(UpcomingEvent, [{
      key: "handleData",
      value: function handleData(data) {
        var _get2, _data$getSavedFlights;
        var getPlaypassBookings = [];
        (_get2 = (0, _lodash.get)(data, "getPlaypassBookings_v2.data")) == null || _get2.forEach(function (element) {
          if (element != null && element.isActive) {
            var _element$playpassEven;
            var elementEndTime = !(0, _lodash.isEmpty)(element == null ? undefined : element.endTime) ? element.endTime : "23:59:59";
            var elementTitleKey = "titleV2";
            getPlaypassBookings.push(Object.assign({}, element, {
              titleSort: element == null || (_element$playpassEven = element.playpassEventCard) == null ? undefined : _element$playpassEven[elementTitleKey],
              timeStamp: (element == null ? undefined : element.type) === "event" ? (0, _dateTime.convertDateTimeToTimeStampSingapore)(`${element == null ? undefined : element.startDate} ${element == null ? undefined : element.startTime}`, "YYYY-MM-DD HH:mm:ss") : (0, _dateTime.convertDateTimeToTimeStampSingapore)(`${element == null ? undefined : element.endDate} ${elementEndTime}`, "YYYY-MM-DD HH:mm:ss"),
              upcomingEventType: _constants.UpComingEventType.playpassBookings
            }));
          }
        });
        var getSavedFlights = [];
        data == null || (_data$getSavedFlights = data.getSavedFlights) == null || _data$getSavedFlights.forEach(function (item) {
          var titleSort = handleDescription(item);
          getSavedFlights.push(Object.assign({}, item, {
            titleSort: titleSort,
            timeStamp: (0, _dateTime.convertDateTimeToTimeStampSingapore)((item == null ? undefined : item.scheduledDate) + " " + (item == null ? undefined : item.scheduledTime)),
            upcomingEventType: _constants.UpComingEventType.savedFlights
          }));
        });
        var upComingEventList = (0, _lodash.sortBy)([].concat(getPlaypassBookings, getSavedFlights), ["timeStamp", "titleSort"]);
        if (upComingEventList.length > 5) {
          var seeMore = {
            title: (0, _i18n.translate)("exploreScreen.seeMore"),
            isSeeMore: true,
            isCustomIcon: true
          };
          return [].concat((0, _toConsumableArray2.default)(upComingEventList.slice(0, 5)), [seeMore]);
        } else {
          return upComingEventList;
        }
      }
    }]);
  }(UpcomingEventClass);
