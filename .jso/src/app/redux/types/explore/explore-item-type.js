  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PlayPassEntryPoint = exports.ExploreItemTypeEnum = exports.ExploreItemCategoryEnum = undefined;
  var ExploreItemTypeEnum = exports.ExploreItemTypeEnum = /*#__PURE__*/function (ExploreItemTypeEnum) {
    ExploreItemTypeEnum["default"] = "default";
    ExploreItemTypeEnum["loading"] = "loading";
    return ExploreItemTypeEnum;
  }({});
  var ExploreItemCategoryEnum = exports.ExploreItemCategoryEnum = /*#__PURE__*/function (ExploreItemCategoryEnum) {
    ExploreItemCategoryEnum["attraction"] = "Attraction";
    ExploreItemCategoryEnum["event"] = "Event";
    return ExploreItemCategoryEnum;
  }({});
  var PlayPassEntryPoint = exports.PlayPassEntryPoint = /*#__PURE__*/function (PlayPassEntryPoint) {
    PlayPassEntryPoint["EXPLORE_CHANGI"] = "Explore Changi";
    PlayPassEntryPoint["CHANGI_STICKY_CART"] = "Changi Sticky Car";
    PlayPassEntryPoint["FAQ_UPDATE_RECEIPT"] = "FAQ_Upload Receipt";
    PlayPassEntryPoint["LATEST_HAPPENING"] = "Latest Happening";
    PlayPassEntryPoint["PASS_DETAIL_EDIT_BOOKING"] = "Pass Details_Edit Booking";
    PlayPassEntryPoint["PASS_DETAIL_EDIT_DETAILS"] = "Pass Details_Event Details";
    PlayPassEntryPoint["PERK_DETAILS"] = "Perk Details";
    PlayPassEntryPoint["PERK_PAGE"] = "Perk Page";
    PlayPassEntryPoint["DINE_SHOP_EPIC"] = "Dine Shop EPIC";
    return PlayPassEntryPoint;
  }({});
