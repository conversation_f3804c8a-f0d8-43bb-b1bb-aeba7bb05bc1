  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EventConfirmationDetail = exports.EventConfirmation = undefined;
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _exploreBaseClass = _$$_REQUIRE(_dependencyMap[6]);
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var EventPassDetail = /*#__PURE__*/(0, _createClass2.default)(function EventPassDetail(data) {
    (0, _classCallCheck2.default)(this, EventPassDetail);
    this.title = data == null ? undefined : data.title;
    this.qrContent = data == null ? undefined : data.qrContent;
    this.countOfTickets = data == null ? undefined : data.countOfTickets;
    this.location = data == null ? undefined : data.location;
    this.startDate = data == null ? undefined : data.startDate;
    this.startTime = data == null ? undefined : data.startTime;
  });
  var PaymentDetail = /*#__PURE__*/(0, _createClass2.default)(function PaymentDetail(data) {
    (0, _classCallCheck2.default)(this, PaymentDetail);
    this.amountPaidTitle = data == null ? undefined : data.amountPaidTitle;
    this.amountPaidValue = data == null ? undefined : data.amountPaidValue;
    this.paymentVia = data == null ? undefined : data.paymentVia;
  });
  var ViewBreakDown = /*#__PURE__*/(0, _createClass2.default)(function ViewBreakDown(data) {
    (0, _classCallCheck2.default)(this, ViewBreakDown);
    this.slotName = data == null ? undefined : data.amountPaidTitle;
    this.countText = data == null ? undefined : data.amountPaidValue;
    this.passName = data == null ? undefined : data.paymentVia;
    this.beforePrice = data == null ? undefined : data.amountPaidTitle;
    this.sgdPrice = data == null ? undefined : data.amountPaidValue;
    this.addOns = data == null ? undefined : data.paymentVia;
  });
  var EventConfirmationDetail = exports.EventConfirmationDetail = /*#__PURE__*/(0, _createClass2.default)(function EventConfirmationDetail(data) {
    (0, _classCallCheck2.default)(this, EventConfirmationDetail);
    this.totalPaid = data == null ? undefined : data.totalPaid;
    this.eventPassDetails = data == null ? undefined : data.eventPassDetails;
    this.paymentDetails = data == null ? undefined : data.paymentDetails;
    this.viewBreakDown = data == null ? undefined : data.viewBreakDown;
    this.statusGetAllBookings = data == null ? undefined : data.statusGetAllBookings;
  });
  var EventConfirmation = exports.EventConfirmation = /*#__PURE__*/function (_ExploreBaseClass) {
    function EventConfirmation(eventConfirmationDetail, hasError, isLoading) {
      var _this;
      (0, _classCallCheck2.default)(this, EventConfirmation);
      _this = _callSuper(this, EventConfirmation);
      _this.data = eventConfirmationDetail;
      _this.hasError = hasError;
      _this.isLoading = isLoading;
      return _this;
    }
    (0, _inherits2.default)(EventConfirmation, _ExploreBaseClass);
    return (0, _createClass2.default)(EventConfirmation, [{
      key: "request",
      value: function request() {
        return new EventConfirmation(undefined, false, true);
      }
    }, {
      key: "success",
      value: function success(action) {
        var isError = false;
        var isLoading = false;
        var _ref = action.payload || {},
          data = _ref.data,
          errors = _ref.errors;
        if ((errors == null ? undefined : errors.length) > 0) {
          return this.failure();
        }
        var getEventConfirmation = (data == null ? undefined : data.getEventConfirmation) || {};
        if (getEventConfirmation) {
          return new EventConfirmation(getEventConfirmation, isError, isLoading);
        }
        return new EventConfirmation(undefined, isError, isLoading);
      }
    }, {
      key: "failure",
      value: function failure() {
        return new EventConfirmation(undefined, true, false);
      }
    }]);
  }(_exploreBaseClass.ExploreBaseClass);
