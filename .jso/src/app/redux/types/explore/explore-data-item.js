  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.setEventsAttractions = exports.getSelectedCategoryItemText = exports.getSelectedCategoryItem = exports.EXPLORE_ITEM_COUNT_PER_PAGE = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _attractions = _$$_REQUIRE(_dependencyMap[2]);
  var _eventDetails = _$$_REQUIRE(_dependencyMap[3]);
  var _lodash = _$$_REQUIRE(_dependencyMap[4]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[5]);
  var _tabBarButton = _$$_REQUIRE(_dependencyMap[6]);
  var EXPLORE_ITEM_COUNT_PER_PAGE = exports.EXPLORE_ITEM_COUNT_PER_PAGE = 10;
  var EXPLORE_ITEM_OBJECT_KEY_NAME = "exploreItem";
  var ExploreItemPrimaryKeyEnum = /*#__PURE__*/function (ExploreItemPrimaryKeyEnum) {
    ExploreItemPrimaryKeyEnum["attraction"] = "attractionId";
    ExploreItemPrimaryKeyEnum["event"] = "packageCode";
    ExploreItemPrimaryKeyEnum["empty"] = "id";
    return ExploreItemPrimaryKeyEnum;
  }(ExploreItemPrimaryKeyEnum || {});
  var getItemIndexByKey = function getItemIndexByKey(itemArray, itemObject, itemObjectKey, itemKey) {
    return itemArray.findIndex(function (data) {
      var _data$itemObjectKey;
      return (data == null || (_data$itemObjectKey = data[itemObjectKey]) == null ? undefined : _data$itemObjectKey[itemKey]) === (itemObject == null ? undefined : itemObject[itemKey]);
    });
  };
  var setNewExploreItem = function setNewExploreItem(item) {
    if (item.categoryType === _exploreItemType.ExploreItemCategoryEnum.event) {
      return new _eventDetails.EventDetails(item);
    } else if (item.categoryType === _exploreItemType.ExploreItemCategoryEnum.attraction) {
      return new _attractions.Attraction(item);
    } else return item;
  };
  var getItemPrimaryKey = function getItemPrimaryKey(item) {
    if (item.categoryType === _exploreItemType.ExploreItemCategoryEnum.event) {
      return ExploreItemPrimaryKeyEnum.event;
    } else if (item.categoryType === _exploreItemType.ExploreItemCategoryEnum.attraction) {
      return ExploreItemPrimaryKeyEnum.attraction;
    } else return ExploreItemPrimaryKeyEnum.empty;
  };
  var setNewExploreData = function setNewExploreData(mwEventsAttractions, eventsAttractions) {
    var newEventsAttractions = [];
    mwEventsAttractions == null || mwEventsAttractions.forEach(function (mwExploreItem) {
      var itemObjectKey = EXPLORE_ITEM_OBJECT_KEY_NAME;
      var itemKey = getItemPrimaryKey(mwExploreItem);
      var eventIndex = getItemIndexByKey(eventsAttractions, mwExploreItem, itemObjectKey, itemKey);
      var newExploreItem = setNewExploreItem(mwExploreItem);
      if (eventIndex === -1) {
        newEventsAttractions.push({
          exploreItem: newExploreItem
        });
      } else {
        eventsAttractions[eventIndex].exploreItem = newExploreItem;
      }
    });
    return newEventsAttractions;
  };
  var sortExploreData = function sortExploreData(exploreCategoryItem) {
    var filterPinnedItems = function filterPinnedItems(array) {
      return array.filter(function (item) {
        return item.isPinned;
      });
    };
    var filterUnPinnedItems = function filterUnPinnedItems(array) {
      return array.filter(function (item) {
        return !item.isPinned;
      });
    };
    var sortBySequenceNumber = function sortBySequenceNumber(array) {
      return (0, _toConsumableArray2.default)(array).sort(function (a, b) {
        return parseFloat(a.sequenceNumber) - parseFloat(b.sequenceNumber);
      });
    };
    var mwExploreEventsAttractions = exploreCategoryItem || {};
    var _mwExploreEventsAttra = mwExploreEventsAttractions.events,
      mwEvents = _mwExploreEventsAttra === undefined ? [] : _mwExploreEventsAttra,
      _mwExploreEventsAttra2 = mwExploreEventsAttractions.attractions,
      mwAttractions = _mwExploreEventsAttra2 === undefined ? [] : _mwExploreEventsAttra2;
    var mappedMWEvents = (0, _toConsumableArray2.default)(mwEvents.map(function (item) {
      return new _eventDetails.MWEvent().mapData(item);
    }));
    var mappedMWAttractions = (0, _toConsumableArray2.default)(mwAttractions.map(function (item) {
      return new _attractions.MWAttraction().mapData(item);
    }));
    var pinnedData = [].concat((0, _toConsumableArray2.default)(filterPinnedItems(mappedMWEvents)), (0, _toConsumableArray2.default)(filterPinnedItems(mappedMWAttractions)));
    var unPinnedData = [].concat((0, _toConsumableArray2.default)(filterUnPinnedItems(mappedMWEvents)), (0, _toConsumableArray2.default)(filterUnPinnedItems(mappedMWAttractions)));
    var sortedPinnedData = sortBySequenceNumber(pinnedData);
    var newSortedPinnedData = [];
    sortedPinnedData.forEach(function (item) {
      newSortedPinnedData.push(item);
      newSortedPinnedData.push({
        id: `${item.attractionId || item.packageCode}${"-"}${ExploreItemPrimaryKeyEnum.empty}`
      });
    });
    var sortedUnPinnedData = sortBySequenceNumber(unPinnedData);
    return [].concat(newSortedPinnedData, (0, _toConsumableArray2.default)(sortedUnPinnedData));
  };
  var setEventsAttractions = exports.setEventsAttractions = function setEventsAttractions(exploreCategoryItem, exploreData, tabName) {
    var _exploreData$tabName;
    var mwEventsAttractions = sortExploreData(exploreCategoryItem);
    var eventsAttractions = (0, _toConsumableArray2.default)((exploreData == null || (_exploreData$tabName = exploreData[tabName]) == null ? undefined : _exploreData$tabName.eventsAttractions) || []);
    if ((0, _lodash.isArray)(mwEventsAttractions)) {
      var newEventsAttractions = setNewExploreData(mwEventsAttractions, eventsAttractions);
      eventsAttractions = [].concat((0, _toConsumableArray2.default)(eventsAttractions), (0, _toConsumableArray2.default)(newEventsAttractions));
    }
    return eventsAttractions;
  };
  var getSelectedCategoryItemText = exports.getSelectedCategoryItemText = function getSelectedCategoryItemText(data) {
    var _data$find;
    return data == null || (_data$find = data.find(function (item) {
      return item.type === _tabBarButton.TabBarButtonType.selected;
    })) == null ? undefined : _data$find.text;
  };
  var getSelectedCategoryItem = exports.getSelectedCategoryItem = function getSelectedCategoryItem(data) {
    return data == null ? undefined : data.find(function (item) {
      return item.type === _tabBarButton.TabBarButtonType.selected;
    });
  };
