  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _staffPerkFilterBar = _$$_REQUIRE(_dependencyMap[4]);
  var _staffPerkFilterBar2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _filterPill = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _filterBottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _filterBottomSheet2 = _$$_REQUIRE(_dependencyMap[15]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _get2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _isEqual2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _adobe = _$$_REQUIRE(_dependencyMap[20]);
  var _staffPerkCategoryListing = _$$_REQUIRE(_dependencyMap[21]);
  var _isNumber2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _utils = _$$_REQUIRE(_dependencyMap[23]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[24]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[25]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_2824427159804_init_data = {
    code: "function staffPerkFilterBarTsx1(){const{vOffset,withTiming}=this.__closure;let marginTop=undefined;if(vOffset.value>=275){marginTop=withTiming(52,{duration:200});}else{marginTop=withTiming(0,{duration:200});}return{marginTop:marginTop};}"
  };
  var _worklet_307994651362_init_data = {
    code: "function handleIndexGesture_staffPerkFilterBarTsx2(e){const{INDEX_ITEM_HEIGHT,currentIndexOffset,ALPHABETICAL_INDEX_LIST,runOnJS,handlePressIndexSearch}=this.__closure;var index=Math.floor(e.y/INDEX_ITEM_HEIGHT);if(index===currentIndexOffset.current)return;if(index>=0&&index<ALPHABETICAL_INDEX_LIST.length){var _ALPHABETICAL_INDEX_L;runOnJS(handlePressIndexSearch)((_ALPHABETICAL_INDEX_L=ALPHABETICAL_INDEX_LIST[index])==null?void 0:_ALPHABETICAL_INDEX_L.value);currentIndexOffset.current=index;}}"
  };
  var StaffPerkFilterBar = function StaffPerkFilterBar(_ref) {
    var _get$find, _get$find2;
    var disabled = _ref.disabled,
      fetchData = _ref.fetchData,
      isFirstRequest = _ref.isFirstRequest,
      listData = _ref.listData,
      offsetRecalculationCount = _ref.offsetRecalculationCount,
      perkItemOffsetListRef = _ref.perkItemOffsetListRef,
      rootItemOffsetRef = _ref.rootItemOffsetRef,
      rootListRef = _ref.rootListRef,
      visible = _ref.visible,
      vOffset = _ref.vOffset;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isBSVisible = _useState2[0],
      setIsBSVisible = _useState2[1];
    var _useState3 = (0, _react.useState)([]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      areaFilters = _useState4[0],
      setAreaFilters = _useState4[1];
    var _useState5 = (0, _react.useState)([]),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      terminalFilters = _useState6[0],
      setTerminalFilters = _useState6[1];
    var _useState7 = (0, _react.useState)([]),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      categoryFilters = _useState8[0],
      setCategoryFilters = _useState8[1];
    var _useState9 = (0, _react.useState)(_filterBottomSheet2.SortBy.LatestAddedDate),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      sortBy = _useState0[0],
      setSortBy = _useState0[1];
    var _useState1 = (0, _react.useState)(true),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      isFirstLoad = _useState10[0],
      setIsFirstLoad = _useState10[1];
    var _useState11 = (0, _react.useState)(_filterBottomSheet2.FILTER_SECTION.LOCATION),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      focusTab = _useState12[0],
      setFocusTab = _useState12[1];
    var dispatch = (0, _reactRedux.useDispatch)();
    var aemFilterData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FILTER_LOCATION_FACILITIES_SERVICE));
    var areaFilterData = (_get$find = (0, _get2.default)(aemFilterData, "data.list", []).find(function (item) {
      return item.tagName === "areas";
    })) == null ? undefined : _get$find.childTags;
    var terminalFilterData = (_get$find2 = (0, _get2.default)(aemFilterData, "data.list", []).find(function (item) {
      return item.tagName === "locations";
    })) == null ? undefined : _get$find2.childTags;
    var originalFiltersRef = (0, _react.useRef)({});
    var currentIndexOffset = (0, _react.useRef)(-1);
    var locationDisplayText = (0, _react.useMemo)(function () {
      if (!(areaFilters != null && areaFilters.length) && !(terminalFilters != null && terminalFilters.length)) {
        return (0, _i18n.translate)("staffPerkListing.filterBar.locationTextDefault");
      }
      // Get area display text
      var areaDisplayText = "staffPerkListing.filterBar.locationText.publicNTransitAreas";
      if (areaFilters != null && areaFilters.length && (areaFilters == null ? undefined : areaFilters.length) < (areaFilterData == null ? undefined : areaFilterData.length)) {
        areaDisplayText = "staffPerkListing.filterBar.locationText.publicArea";
        if (areaFilters != null && areaFilters.some != null && areaFilters.some(function (area) {
          return (area == null ? undefined : area.tagName) === "transit";
        })) {
          areaDisplayText = "staffPerkListing.filterBar.locationText.transitArea";
        }
      }
      // Get terminal display text
      var terminalDisplayText = (0, _i18n.translate)("staffPerkListing.filterBar.locationText.allTerminals");
      if (terminalFilters != null && terminalFilters.length && (terminalFilters == null ? undefined : terminalFilters.length) < (terminalFilterData == null ? undefined : terminalFilterData.length)) {
        var _terminalFilters$map;
        terminalDisplayText = terminalFilters == null || terminalFilters.map == null || (_terminalFilters$map = terminalFilters.map(function (terminal) {
          var _terminal$tagCode;
          if ((terminal == null ? undefined : terminal.tagCode) === "tj") {
            return terminal == null ? undefined : terminal.tagTitle;
          }
          return terminal == null || (_terminal$tagCode = terminal.tagCode) == null || _terminal$tagCode.toUpperCase == null ? undefined : _terminal$tagCode.toUpperCase();
        })) == null || _terminalFilters$map.join == null ? undefined : _terminalFilters$map.join(", ");
      }
      return (0, _i18n.translate)("staffPerkListing.filterBar.locationText.displayFormat", {
        area: (0, _i18n.translate)(areaDisplayText),
        terminal: terminalDisplayText
      });
    }, [JSON.stringify(areaFilters), JSON.stringify(terminalFilters), JSON.stringify(areaFilterData), JSON.stringify(terminalFilterData)]);
    var indexData = (0, _react.useMemo)(function () {
      var result = {};
      for (var i = 0; i < (listData == null ? undefined : listData.length); i++) {
        var _listData$i, _tenantName$;
        var tenantName = (_listData$i = listData[i]) == null ? undefined : _listData$i.tenantName;
        var firstLetter = (_tenantName$ = tenantName[0]) == null || _tenantName$.toLowerCase == null ? undefined : _tenantName$.toLowerCase();
        if (!firstLetter) continue;
        if (!/[a-z]/.test(firstLetter)) firstLetter = "#";
        if (result[firstLetter] || result[firstLetter] === 0) continue;
        result[firstLetter] = i;
      }
      var alphabetList = "#ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");
      var missingIndexes = [];
      for (var _i = 0; _i < alphabetList.length; _i++) {
        var letter = alphabetList[_i].toLowerCase();
        if (!(0, _isNumber2.default)(result[letter])) {
          missingIndexes.push(letter);
        } else {
          for (var j = 0; j < missingIndexes.length; j++) {
            result[missingIndexes[j]] = result[letter];
          }
          missingIndexes = [];
        }
      }
      if (missingIndexes.length) {
        for (var _i2 = 0; _i2 < missingIndexes.length; _i2++) {
          result[missingIndexes[_i2]] = (listData == null ? undefined : listData.length) - 1;
        }
      }
      return result;
    }, [JSON.stringify(listData)]);
    var perkItemAccumulateHeights = (0, _react.useMemo)(function () {
      return listData.reduce(function (result, _item, index) {
        var totalHeights = 0;
        for (var j = 0; j < index; j++) {
          totalHeights += perkItemOffsetListRef.current[j];
        }
        return result.concat(totalHeights);
      }, []);
    }, [JSON.stringify(listData), offsetRecalculationCount]);
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var staffPerkFilterBarTsx1 = function staffPerkFilterBarTsx1() {
        var marginTop = undefined;
        if (vOffset.value >= 275) {
          marginTop = (0, _reactNativeReanimated.withTiming)(52, {
            duration: 200
          });
        } else {
          marginTop = (0, _reactNativeReanimated.withTiming)(0, {
            duration: 200
          });
        }
        return {
          marginTop: marginTop
        };
      };
      staffPerkFilterBarTsx1.__closure = {
        vOffset: vOffset,
        withTiming: _reactNativeReanimated.withTiming
      };
      staffPerkFilterBarTsx1.__workletHash = 2824427159804;
      staffPerkFilterBarTsx1.__initData = _worklet_2824427159804_init_data;
      return staffPerkFilterBarTsx1;
    }(), [vOffset.value]);
    var handlePressFilterIcon = function handlePressFilterIcon(sectionTab) {
      if (sectionTab) {
        setFocusTab(sectionTab);
      }
      setIsBSVisible(true);
    };
    var handlePressFilterPill = function handlePressFilterPill(item, newValue) {
      setCategoryFilters(function (list) {
        var newList = list == null || list.filter == null ? undefined : list.filter(function (val) {
          return val !== (item == null ? undefined : item.value);
        });
        if (newValue) {
          newList = list == null || list.concat == null ? undefined : list.concat(item == null ? undefined : item.value);
        }
        originalFiltersRef.current = Object.assign({}, originalFiltersRef.current, {
          categoryFilters: newList
        });
        return newList;
      });
    };
    var handleToggleSortBy = function handleToggleSortBy() {
      setSortBy(function (val) {
        if (val === _filterBottomSheet2.SortBy.LatestAddedDate) {
          return _filterBottomSheet2.SortBy.AZ;
        }
        return _filterBottomSheet2.SortBy.LatestAddedDate;
      });
    };
    (0, _react.useEffect)(function () {
      // Fetch location filter data
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.FILTER_LOCATION_FACILITIES_SERVICE,
        pathName: "getLocationFilterFacilitiesService"
      }));
    }, []);
    var trackingFilter = function trackingFilter() {
      var dataSortBy = sortBy === _filterBottomSheet2.SortBy.LatestAddedDate ? "Latest Added Date" : "A-Z";
      var dataTrackingArea = areaFilters == null ? undefined : areaFilters.map(function (item) {
        return `${item == null ? undefined : item.tagTitle} Area`;
      }).join(", ");
      var dataTrackingTerminal = terminalFilters == null ? undefined : terminalFilters.map(function (item) {
        return `${item == null ? undefined : item.tagTitle}`;
      }).join(", ");
      var dataTrackingCategory = _staffPerkFilterBar.STAFF_PERK_CATEGORY_PILLS.filter(function (item) {
        return categoryFilters == null ? undefined : categoryFilters.includes(item == null ? undefined : item.value);
      }).map(function (item) {
        return (0, _i18n.translate)(item == null ? undefined : item.label);
      }).join(", ");
      var dataTrackingAreaEmpty = "Public Area, Transit Area";
      var dataTrackingTerminalEmpty = terminalFilterData == null ? undefined : terminalFilterData.map(function (item) {
        return `${item == null ? undefined : item.tagTitle}`;
      }).join(", ");
      var dataTrackingCategoryEmpty = _filterBottomSheet2.CATEGORY_LIST == null ? undefined : _filterBottomSheet2.CATEGORY_LIST.map(function (item) {
        return `${(0, _i18n.translate)(item == null ? undefined : item.label)}`;
      }).join(", ");
      var haveArea = (dataTrackingArea == null ? undefined : dataTrackingArea.length) > 0;
      var haveTerminal = (dataTrackingTerminal == null ? undefined : dataTrackingTerminal.length) > 0;
      var haveCategory = (dataTrackingCategory == null ? undefined : dataTrackingCategory.length) > 0;
      if (haveArea || haveTerminal || haveCategory) {
        var stringArea = `${haveArea ? dataTrackingArea : dataTrackingAreaEmpty} `;
        var stringTerminal = `: ${haveTerminal ? dataTrackingTerminal : dataTrackingTerminalEmpty} `;
        var stringCategory = `| ${haveCategory ? dataTrackingCategory : dataTrackingCategoryEmpty} `;
        var stringSortBy = `| ${dataSortBy}`;
        var dataTrackingGalaxy = `Apply Filter | ${stringArea}${stringTerminal}${stringCategory}${stringSortBy}`;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyFilters, dataTrackingGalaxy));
      } else if (!haveArea && !haveTerminal && !haveCategory) {
        var _stringSortBy = ` | ${dataSortBy}`;
        var _dataTrackingGalaxy = `Apply Filter | ${dataTrackingAreaEmpty}: ${dataTrackingTerminalEmpty} | ${dataTrackingCategoryEmpty}${_stringSortBy}`;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyFilters, _dataTrackingGalaxy));
      }
    };
    var handlePressIndexSearch = function handlePressIndexSearch(letter) {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        var _rootItemOffsetRef$cu, _rootItemOffsetRef$cu2, _rootListRef$current;
        var dataTrackingGalaxy = `ABC Jump | ${letter.toUpperCase()}`;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyLanding, dataTrackingGalaxy));
        var indexToScroll = indexData[letter];
        if (!indexToScroll && indexToScroll !== 0) return;
        var totalPerkItemHeights = perkItemAccumulateHeights[indexToScroll];
        var offset = Number((_rootItemOffsetRef$cu = rootItemOffsetRef.current) == null ? undefined : _rootItemOffsetRef$cu[0]) + Number((_rootItemOffsetRef$cu2 = rootItemOffsetRef.current) == null ? undefined : _rootItemOffsetRef$cu2[1]) - 140;
        offset = offset + totalPerkItemHeights;
        (_rootListRef$current = rootListRef.current) == null || _rootListRef$current.scrollToOffset({
          animated: false,
          offset: offset,
          viewPosition: 0
        });
      });
    };
    var handleIndexGesture = function () {
      const handleIndexGesture = function (e) {
        var index = Math.floor(e.y / _staffPerkFilterBar2.INDEX_ITEM_HEIGHT);
        if (index === currentIndexOffset.current) return;
        if (index >= 0 && index < _staffPerkCategoryListing.ALPHABETICAL_INDEX_LIST.length) {
          var _ALPHABETICAL_INDEX_L;
          (0, _reactNativeReanimated.runOnJS)(handlePressIndexSearch)((_ALPHABETICAL_INDEX_L = _staffPerkCategoryListing.ALPHABETICAL_INDEX_LIST[index]) == null ? undefined : _ALPHABETICAL_INDEX_L.value);
          currentIndexOffset.current = index;
        }
      };
      handleIndexGesture.__closure = {
        INDEX_ITEM_HEIGHT: _staffPerkFilterBar2.INDEX_ITEM_HEIGHT,
        currentIndexOffset,
        ALPHABETICAL_INDEX_LIST: _staffPerkCategoryListing.ALPHABETICAL_INDEX_LIST,
        runOnJS: _reactNativeReanimated.runOnJS,
        handlePressIndexSearch
      };
      handleIndexGesture.__workletHash = 307994651362;
      handleIndexGesture.__initData = _worklet_307994651362_init_data;
      return handleIndexGesture;
    }();
    var gesture = _reactNativeGestureHandler.Gesture.Pan().onBegin(handleIndexGesture).onUpdate(handleIndexGesture).runOnJS(true);
    (0, _react.useEffect)(function () {
      trackingFilter();
      if (isFirstLoad) {
        setIsFirstLoad(false);
        return;
      }
      if (isBSVisible || (0, _isEqual2.default)(originalFiltersRef.current, {
        areaFilters: areaFilters,
        categoryFilters: categoryFilters,
        terminalFilters: terminalFilters
      })) return;
      fetchData({
        areaFilters: areaFilters,
        categoryFilters: categoryFilters,
        sortBy: sortBy,
        terminalFilters: terminalFilters
      });
    }, [JSON.stringify(areaFilters), JSON.stringify(terminalFilters), JSON.stringify(categoryFilters), isBSVisible]);

    // Reload data when changing Sort By option
    (0, _react.useEffect)(function () {
      fetchData({
        areaFilters: areaFilters,
        categoryFilters: categoryFilters,
        sortBy: sortBy,
        terminalFilters: terminalFilters
      });
    }, [sortBy]);

    // Reload data when toggling quick filter pills
    (0, _react.useEffect)(function () {
      if (isBSVisible) return;
      fetchData({
        areaFilters: areaFilters,
        categoryFilters: categoryFilters,
        sortBy: sortBy,
        terminalFilters: terminalFilters
      });
    }, [JSON.stringify(categoryFilters)]);
    if (isFirstRequest && !visible) return null;
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        accessibilityLabel: `${_staffPerkFilterBar.COMPONENT_NAME}_Container`,
        onLayout: function onLayout(e) {
          var _e$nativeEvent;
          return rootItemOffsetRef.current[1] = e == null || (_e$nativeEvent = e.nativeEvent) == null || (_e$nativeEvent = _e$nativeEvent.layout) == null ? undefined : _e$nativeEvent.height;
        },
        style: [_staffPerkFilterBar2.styles.containerStyle, animatedStyle],
        testID: `${_staffPerkFilterBar.COMPONENT_NAME}_Container`,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          accessibilityLabel: `${_staffPerkFilterBar.COMPONENT_NAME}_Title`,
          style: _staffPerkFilterBar2.styles.titleTextStyle,
          testID: `${_staffPerkFilterBar.COMPONENT_NAME}_Title`,
          tx: "staffPerkListing.filterBar.title"
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          accessibilityLabel: `${_staffPerkFilterBar.COMPONENT_NAME}_Icon_Container`,
          style: _staffPerkFilterBar2.styles.filterIconsContainerStyle,
          testID: `${_staffPerkFilterBar.COMPONENT_NAME}_Icon_Container`,
          children: [(0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
            accessibilityLabel: `${_staffPerkFilterBar.COMPONENT_NAME}_Icon_Location`,
            disabled: disabled,
            onPress: function onPress() {
              return handlePressFilterIcon(_filterBottomSheet2.FILTER_SECTION.LOCATION);
            },
            style: [_staffPerkFilterBar2.styles.filterIconContainerStyle, _staffPerkFilterBar2.styles.filterIconFullContainerStyle],
            testID: `${_staffPerkFilterBar.COMPONENT_NAME}_Icon_Location`,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _staffPerkFilterBar2.styles.filterIconTextStyle,
              numberOfLines: 1,
              text: locationDisplayText,
              ellipsizeMode: "tail"
            }), (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
              color: _theme.color.palette.darkestGrey,
              height: 12,
              width: 12
            }), (!!(areaFilters != null && areaFilters.length) || !!(terminalFilters != null && terminalFilters.length)) && (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _staffPerkFilterBar2.styles.activeDotIconStyle
            })]
          }), (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
            accessibilityLabel: `${_staffPerkFilterBar.COMPONENT_NAME}_Icon_Category`,
            disabled: disabled,
            onPress: function onPress() {
              return handlePressFilterIcon(_filterBottomSheet2.FILTER_SECTION.CATEGORIES);
            },
            style: [_staffPerkFilterBar2.styles.filterIconContainerStyle, _staffPerkFilterBar2.styles.filterIconIconOnlyContainerStyle],
            testID: `${_staffPerkFilterBar.COMPONENT_NAME}_Icon_Category`,
            children: [(0, _jsxRuntime.jsx)(_icons.Filter, {
              color: _theme.color.palette.darkestGrey,
              height: 12,
              width: 12
            }), !!(categoryFilters != null && categoryFilters.length) && (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _staffPerkFilterBar2.styles.activeDotIconStyle
            })]
          }), (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
            accessibilityLabel: `${_staffPerkFilterBar.COMPONENT_NAME}_Icon_Sort`,
            disabled: disabled,
            onPress: handleToggleSortBy,
            style: [_staffPerkFilterBar2.styles.filterIconContainerStyle, _staffPerkFilterBar2.styles.filterIconIconOnlyContainerStyle],
            testID: `${_staffPerkFilterBar.COMPONENT_NAME}_Icon_Sort`,
            children: [(0, _jsxRuntime.jsx)(_icons.SortWithArrowIcon, {
              color: _theme.color.palette.darkestGrey
            }), sortBy !== _filterBottomSheet2.SortBy.LatestAddedDate && (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _staffPerkFilterBar2.styles.activeDotIconStyle
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
          accessibilityLabel: `${_staffPerkFilterBar.COMPONENT_NAME}_Pill_Container`,
          horizontal: true,
          showsHorizontalScrollIndicator: false,
          style: _staffPerkFilterBar2.styles.filterPillsScrollViewStyle,
          testID: `${_staffPerkFilterBar.COMPONENT_NAME}_Pill_Container`,
          contentContainerStyle: _staffPerkFilterBar2.styles.filterPillsContainerStyle,
          children: _staffPerkFilterBar.STAFF_PERK_CATEGORY_PILLS.map(function (item) {
            var isActive = categoryFilters.some(function (val) {
              return val === item.value;
            });
            return (0, _jsxRuntime.jsx)(_filterPill.default, {
              accessibilityLabel: `${_staffPerkFilterBar.COMPONENT_NAME}_Pill_Item_${item.value}`,
              active: isActive,
              disabled: disabled,
              IconComponent: item == null ? undefined : item.icon,
              label: (0, _i18n.translate)(item.label),
              onPress: function onPress() {
                return handlePressFilterPill(item, !isActive);
              },
              variant: "outline"
            }, `${item.value}${isActive ? "_active" : ""}`);
          })
        }), (0, _utils.ifAllTrue)([sortBy === _filterBottomSheet2.SortBy.AZ, !!(listData != null && listData.length)]) && (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.GestureDetector, {
          gesture: _reactNativeGestureHandler.Gesture.Simultaneous(gesture, _reactNativeGestureHandler.Gesture.Native()),
          children: (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _staffPerkFilterBar2.styles.alphabeticalIndexSearchContainerStyle,
            children: _staffPerkCategoryListing.ALPHABETICAL_INDEX_LIST.map(function (item) {
              return (0, _jsxRuntime.jsx)(_reactNative.View, {
                style: _staffPerkFilterBar2.styles.indexContainerStyle,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  style: _staffPerkFilterBar2.styles.indexTextStyle,
                  text: item.label
                })
              });
            })
          })
        })]
      }), (0, _jsxRuntime.jsx)(_filterBottomSheet.default, {
        areaFilterData: areaFilterData,
        areaFilters: areaFilters,
        categoryFilters: categoryFilters,
        focusTab: focusTab,
        originalFiltersRef: originalFiltersRef,
        setAreaFilters: setAreaFilters,
        setCategoryFilters: setCategoryFilters,
        setTerminalFilters: setTerminalFilters,
        setVisible: setIsBSVisible,
        terminalFilterData: terminalFilterData,
        terminalFilters: terminalFilters,
        visible: isBSVisible
      })]
    });
  };
  var _default = exports.default = StaffPerkFilterBar;
