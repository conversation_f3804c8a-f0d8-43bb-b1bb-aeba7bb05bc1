  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.StaffPerkCategory = exports.STAFF_PERK_CATEGORY_PILLS = exports.COMPONENT_NAME = undefined;
  var _icons = _$$_REQUIRE(_dependencyMap[0]);
  var COMPONENT_NAME = exports.COMPONENT_NAME = "StaffPerkFilterBar";
  var StaffPerkCategory = exports.StaffPerkCategory = /*#__PURE__*/function (StaffPerkCategory) {
    StaffPerkCategory["NewlyAdded"] = "NEW_STAFF_PERKS";
    StaffPerkCategory["Dining"] = "DINING_PERKS";
    StaffPerkCategory["Shopping"] = "SHOPPING_PERKS";
    StaffPerkCategory["IShopChangiOffers"] = "ISHOPCHANGI_OFFERS";
    StaffPerkCategory["Services"] = "SERVICES";
    StaffPerkCategory["LimitedOffers"] = "SEASONAL";
    return StaffPerkCategory;
  }({});
  var STAFF_PERK_CATEGORY_PILLS = exports.STAFF_PERK_CATEGORY_PILLS = [{
    label: "staffPerkListing.filterBar.categoryPill.newlyAdded",
    value: StaffPerkCategory.NewlyAdded
  }, {
    label: "staffPerkListing.filterBar.categoryPill.dining",
    value: StaffPerkCategory.Dining
  }, {
    label: "staffPerkListing.filterBar.categoryPill.shopping",
    value: StaffPerkCategory.Shopping
  }, {
    icon: _icons.IShopChangiIcon,
    label: "staffPerkListing.filterBar.categoryPill.iShopChangi",
    value: StaffPerkCategory.IShopChangiOffers
  }, {
    label: "staffPerkListing.filterBar.categoryPill.services",
    value: StaffPerkCategory.Services
  }, {
    icon: _icons.SaleTagIcon,
    label: "staffPerkListing.filterBar.categoryPill.limitedOffer",
    value: StaffPerkCategory.LimitedOffers
  }];
