  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.COMPONENT_NAME = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _backgrounds = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _staffPerkListingTopBanner = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _groupbuyBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _staffPerkRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _staffPerkLoading = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = exports.COMPONENT_NAME = "StaffPerkListingTopBanner";
  var StaffPerkListingTopBanner = function StaffPerkListingTopBanner(props) {
    var refresh = props.refresh,
      rootItemOffsetRef = props.rootItemOffsetRef;
    var dispatch = (0, _reactRedux.useDispatch)();
    var callAEMData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.STAFF_PERK_LISTING_TOP_BANNER));
    var topBannerData = callAEMData == null ? undefined : callAEMData.data;
    var groupbuyBannerPayload = (0, _reactRedux.useSelector)(_staffPerkRedux.StaffPerkSelectors.groupbuyBannerPayload);
    var groupbuyBannerLoading = (0, _reactRedux.useSelector)(_staffPerkRedux.StaffPerkSelectors.groupbuyBannerLoading);
    var groupbuyBannerError = (0, _reactRedux.useSelector)(_staffPerkRedux.StaffPerkSelectors.groupbuyBannerError);
    (0, _react.useEffect)(function () {
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.STAFF_PERK_LISTING_TOP_BANNER,
        pathName: "getStaffPerkTopBanner",
        forceRequest: true
      }));
    }, []);
    (0, _react.useEffect)(function () {
      if (refresh) {
        dispatch(_aemRedux.default.getAemConfigData({
          name: _aemRedux.AEM_PAGE_NAME.STAFF_PERK_LISTING_TOP_BANNER,
          pathName: "getStaffPerkTopBanner",
          forceRequest: true
        }));
      }
    }, [refresh]);
    var contentTopBanner = function contentTopBanner() {
      if (refresh || groupbuyBannerLoading) {
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _staffPerkListingTopBanner.styles.wrapContentTopBanner,
          children: [refresh && (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
            size: 'small',
            color: _theme.color.palette.almostWhiteGrey
          }), (0, _jsxRuntime.jsx)(_staffPerkLoading.LoadingBanner, {})]
        });
      }
      if (groupbuyBannerError) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _staffPerkListingTopBanner.styles.wrapContentTopBanner,
          children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _staffPerkListingTopBanner.styles.viewError,
            children: [(0, _jsxRuntime.jsx)(_icons.BannerErrorIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "screenError.refresh",
              style: _staffPerkListingTopBanner.styles.txtError
            })]
          })
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _staffPerkListingTopBanner.styles.wrapContentTopBanner,
        children: (groupbuyBannerPayload == null ? undefined : groupbuyBannerPayload.length) > 0 && !groupbuyBannerLoading ? (0, _jsxRuntime.jsx)(_groupbuyBanner.default, {}) : (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _staffPerkListingTopBanner.styles.viewEmpty,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: topBannerData == null ? undefined : topBannerData.caption1,
            style: _staffPerkListingTopBanner.styles.caption1TopBanner,
            testID: `${COMPONENT_NAME}_BannerName`,
            accessibilityLabel: topBannerData == null ? undefined : topBannerData.caption1
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: topBannerData == null ? undefined : topBannerData.caption2,
            style: _staffPerkListingTopBanner.styles.caption2TopBanner,
            testID: `${COMPONENT_NAME}_BannerDescription`,
            accessibilityLabel: topBannerData == null ? undefined : topBannerData.caption2
          })]
        })
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      onLayout: function onLayout(e) {
        var _e$nativeEvent;
        return rootItemOffsetRef.current[0] = e == null || (_e$nativeEvent = e.nativeEvent) == null || (_e$nativeEvent = _e$nativeEvent.layout) == null ? undefined : _e$nativeEvent.height;
      },
      style: _staffPerkListingTopBanner.styles.viewContainer,
      children: [(0, _jsxRuntime.jsx)(_reactNative.Image, {
        source: _backgrounds.StaffPerkListingBackgroundV2,
        style: _staffPerkListingTopBanner.styles.imageBackground
      }), (groupbuyBannerPayload == null ? undefined : groupbuyBannerPayload.length) > 0 && !groupbuyBannerLoading && (0, _jsxRuntime.jsxs)(_react.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _staffPerkListingTopBanner.styles.groupbuyBannerBgContainer,
          children: (0, _jsxRuntime.jsx)(_reactNative.Image, {
            source: _icons.GroupbuyBannerBg,
            style: _staffPerkListingTopBanner.styles.groupbuyBannerBg
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _staffPerkListingTopBanner.styles.backgroundCurveContainer,
          children: (0, _jsxRuntime.jsx)(_reactNative.Image, {
            source: _backgrounds.StaffPerkListingBackgroundCurve,
            style: _staffPerkListingTopBanner.styles.backgroundCurve
          })
        })]
      }), contentTopBanner()]
    });
  };
  var _default = exports.default = StaffPerkListingTopBanner;
