  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerListView: {
      flex: 1,
      minHeight: 620,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      paddingBottom: 24,
      paddingTop: 16
    },
    contentContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      paddingBottom: 24,
      paddingTop: 16
    },
    emptyContentStyle: {
      alignItems: "center",
      marginHorizontal: 24,
      marginTop: -8
    },
    emptyContentTextStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.almostBlack<PERSON><PERSON>,
      textAlign: "center"
    }),
    itemSeparator: {
      height: 12
    },
    containerLoading: {
      flex: 1,
      paddingHorizontal: 16,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    txtTitle: {
      fontFamily: _theme.typography.black,
      fontSize: 16,
      lineHeight: 20,
      fontWeight: "900",
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 23
    }
  });
