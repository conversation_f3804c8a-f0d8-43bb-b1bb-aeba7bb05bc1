  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _exploreStaffPerkItem = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _tabContent = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _footerLoading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _exploreStaffPerk = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _explore = _$$_REQUIRE(_dependencyMap[10]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[11]);
  var _staffPerkTileV = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _staffPerkLoading = _$$_REQUIRE(_dependencyMap[13]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[14]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _isEqual2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var PERK_ITEM_FIXED_MARGIN = 12;
  var ItemComponent = (0, _react.memo)(function (_ref) {
    var dataLength = _ref.dataLength,
      index = _ref.index,
      item = _ref.item,
      isStaffPerkTilesV2 = _ref.isStaffPerkTilesV2,
      navigation = _ref.navigation,
      offsetRecalculationCount = _ref.offsetRecalculationCount,
      perkItemOffsetListRef = _ref.perkItemOffsetListRef,
      setOffsetRecalculationCount = _ref.setOffsetRecalculationCount;
    if (isStaffPerkTilesV2) {
      return (0, _jsxRuntime.jsx)(_staffPerkTileV.default, {
        dataLength: dataLength,
        index: index,
        item: item,
        navigation: navigation,
        onLayout: function onLayout(event) {
          var _Object$keys;
          var layoutHeight = event.nativeEvent.layout.height + PERK_ITEM_FIXED_MARGIN;
          if (!perkItemOffsetListRef) return;
          if (!(perkItemOffsetListRef != null && perkItemOffsetListRef.current)) {
            perkItemOffsetListRef.current = (0, _defineProperty2.default)({}, index, layoutHeight);
          } else {
            perkItemOffsetListRef.current[index] = layoutHeight;
          }
          if (((_Object$keys = Object.keys(perkItemOffsetListRef.current)) == null ? undefined : _Object$keys.length) === dataLength) {
            setOffsetRecalculationCount(offsetRecalculationCount + 1);
          }
        }
      });
    }
    var nDate = (0, _exploreStaffPerk.handleDate)(item == null ? undefined : item.campaignStartDate, item == null ? undefined : item.campaignEndDate);
    return (0, _jsxRuntime.jsx)(_exploreStaffPerkItem.default, {
      item: Object.assign({}, item, {
        date: nDate
      }),
      navigation: navigation,
      isListing: true
    }, item == null ? undefined : item.id);
  }, function (prevProps, nextProps) {
    return !(0, _isEqual2.default)(prevProps, nextProps);
  });
  var TabContent = function TabContent(_ref2) {
    var error = _ref2.error,
      hasNextPageStaffPerk = _ref2.hasNextPageStaffPerk,
      isFirstRequest = _ref2.isFirstRequest,
      listData = _ref2.listData,
      loading = _ref2.loading,
      loadMoreData = _ref2.loadMoreData,
      navigation = _ref2.navigation,
      offsetRecalculationCount = _ref2.offsetRecalculationCount,
      perkItemOffsetListRef = _ref2.perkItemOffsetListRef,
      reloadData = _ref2.reloadData,
      setOffsetRecalculationCount = _ref2.setOffsetRecalculationCount;
    var _useContext = (0, _react.useContext)(_explore.ExploreContext),
      staffPerksTilesV2Flag = _useContext.staffPerksTilesV2Flag;
    var isStaffPerkTilesV2 = (0, _remoteConfig.isFlagOnCondition)(staffPerksTilesV2Flag);
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var ehr21 = errorData == null ? undefined : errorData.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR2.1";
    });
    var emptyContent = function emptyContent() {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _tabContent.styles.emptyContentStyle,
        accessible: false,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: _tabContent.styles.emptyContentTextStyle,
          tx: "staffPerk.emptyContent",
          testID: `TabContent_ExploreStaffPerkDescription`,
          accessibilityLabel: (0, _i18n.translate)("staffPerk.emptyContent")
        })
      });
    };
    var footerComponent = function footerComponent() {
      if (!hasNextPageStaffPerk) return null;
      return (0, _jsxRuntime.jsx)(_footerLoading.default, {});
    };
    if (loading) {
      if (isStaffPerkTilesV2) {
        return (0, _jsxRuntime.jsx)(_staffPerkLoading.LoadingContentV2, {
          isFirstRequest: isFirstRequest
        });
      } else {
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _tabContent.styles.contentContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_exploreStaffPerkItem.default, {
            item: null,
            navigation: navigation,
            isListing: true,
            isLoading: true
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _tabContent.styles.itemSeparator
          }), (0, _jsxRuntime.jsx)(_exploreStaffPerkItem.default, {
            item: null,
            navigation: navigation,
            isListing: true,
            isLoading: true
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _tabContent.styles.itemSeparator
          }), (0, _jsxRuntime.jsx)(_exploreStaffPerkItem.default, {
            item: null,
            navigation: navigation,
            isListing: true,
            isLoading: true
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _tabContent.styles.itemSeparator
          }), (0, _jsxRuntime.jsx)(_exploreStaffPerkItem.default, {
            item: null,
            navigation: navigation,
            isListing: true,
            isLoading: true
          })]
        });
      }
    }
    if (error) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _tabContent.styles.containerLoading,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _tabContent.styles.txtTitle,
          tx: "staffPerkListing.filterBar.title"
        }), (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          skipStatusbar: true,
          title: ehr21 == null ? undefined : ehr21.header,
          content: ehr21 == null ? undefined : ehr21.subHeader,
          style: {
            backgroundColor: "transparent"
          },
          titleStyle: {
            marginTop: 16
          },
          buttonStyle: {
            width: "100%"
          },
          onPress: reloadData
        })]
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
      nestedScrollEnabled: true,
      data: listData,
      renderItem: function renderItem(_ref3) {
        var index = _ref3.index,
          item = _ref3.item;
        return (0, _jsxRuntime.jsx)(ItemComponent, {
          dataLength: listData == null ? undefined : listData.length,
          index: index,
          item: item,
          isStaffPerkTilesV2: isStaffPerkTilesV2,
          navigation: navigation,
          offsetRecalculationCount: offsetRecalculationCount,
          perkItemOffsetListRef: perkItemOffsetListRef,
          setOffsetRecalculationCount: setOffsetRecalculationCount
        });
      },
      keyExtractor: function keyExtractor(e, i) {
        if (i === (listData == null ? undefined : listData.length) - 1) {
          return `${e == null ? undefined : e.id}-${i}_${listData == null ? undefined : listData.length}`;
        }
        return `${e == null ? undefined : e.id}-${i}`;
      },
      showsVerticalScrollIndicator: false,
      contentContainerStyle: _tabContent.styles.contentContainerStyle,
      ItemSeparatorComponent: function ItemSeparatorComponent() {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _tabContent.styles.itemSeparator
        });
      },
      ListEmptyComponent: emptyContent,
      style: _tabContent.styles.containerListView,
      onEndReachedThreshold: 0.4,
      onEndReached: loadMoreData,
      ListFooterComponent: footerComponent,
      initialNumToRender: 1000,
      maxToRenderPerBatch: 50,
      windowSize: 50
    });
  };
  var _default = exports.default = TabContent;
