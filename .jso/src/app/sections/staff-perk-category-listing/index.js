  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _tabContent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _staffPerkListingTopBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _stickyHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNativeReanimated = _$$_REQUIRE(_dependencyMap[8]);
  var _staffPerkFilterBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _staffPerkCategoryListing = _$$_REQUIRE(_dependencyMap[10]);
  var _native = _$$_REQUIRE(_dependencyMap[11]);
  var _staffPerkRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _staffPerkCategoryListing2 = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var StaffPerkCategoryListing = function StaffPerkCategoryListing(_ref) {
    var navigation = _ref.navigation;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)("light-content"),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      barStyle = _useState2[0],
      setBarStyle = _useState2[1];
    var _useState3 = (0, _react.useState)(0),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      offsetRecalculationCount = _useState4[0],
      setOffsetRecalculationCount = _useState4[1];
    var vOffset = (0, _reactNativeReanimated.useSharedValue)(0);
    var _useStaffPerkListingR = (0, _staffPerkCategoryListing.useStaffPerkListingRequests)(),
      currentFilterParams = _useStaffPerkListingR.currentFilterParams,
      fetchData = _useStaffPerkListingR.fetchData,
      hasError = _useStaffPerkListingR.hasError,
      hasNextPageStaffPerk = _useStaffPerkListingR.hasNextPageStaffPerk,
      isFirstRequest = _useStaffPerkListingR.isFirstRequest,
      listData = _useStaffPerkListingR.listData,
      loadingStaffPerkList = _useStaffPerkListingR.loadingStaffPerkList,
      loadMoreData = _useStaffPerkListingR.loadMoreData,
      refresh = _useStaffPerkListingR.refresh,
      setRefresh = _useStaffPerkListingR.setRefresh;
    var rootListRef = (0, _react.useRef)();
    var rootItemOffsetRef = (0, _react.useRef)({});
    var perkItemOffsetListRef = (0, _react.useRef)({});
    var handleContent = function handleContent(id) {
      switch (id) {
        case 1:
          return (0, _jsxRuntime.jsx)(_staffPerkListingTopBanner.default, {
            refresh: refresh,
            rootItemOffsetRef: rootItemOffsetRef
          });
        case 2:
          return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
            children: hasError ? (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {}) : (0, _jsxRuntime.jsx)(_staffPerkFilterBar.default, {
              disabled: loadingStaffPerkList,
              fetchData: fetchData,
              isFirstRequest: isFirstRequest,
              listData: listData,
              offsetRecalculationCount: offsetRecalculationCount,
              perkItemOffsetListRef: perkItemOffsetListRef,
              rootItemOffsetRef: rootItemOffsetRef,
              rootListRef: rootListRef,
              visible: !loadingStaffPerkList,
              vOffset: vOffset
            })
          });
        case 3:
          return (0, _jsxRuntime.jsx)(_tabContent.default, {
            error: hasError,
            hasNextPageStaffPerk: hasNextPageStaffPerk,
            isFirstRequest: isFirstRequest,
            listData: listData,
            loading: loadingStaffPerkList,
            loadMoreData: loadMoreData,
            navigation: navigation,
            offsetRecalculationCount: offsetRecalculationCount,
            perkItemOffsetListRef: perkItemOffsetListRef,
            reloadData: function reloadData() {
              return fetchData(currentFilterParams);
            },
            setOffsetRecalculationCount: setOffsetRecalculationCount
          });
      }
    };
    var onScroll = function onScroll(event) {
      var _event$nativeEvent;
      var newOffset = event == null || (_event$nativeEvent = event.nativeEvent) == null || (_event$nativeEvent = _event$nativeEvent.contentOffset) == null ? undefined : _event$nativeEvent.y;
      vOffset.value = newOffset;
      if (newOffset >= 100) {
        setBarStyle("dark-content");
      } else {
        setBarStyle("light-content");
      }
    };
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      dispatch(_staffPerkRedux.default.getGroupbuyBannerRequest());
    }, []));
    var onRefresh = function onRefresh() {
      setRefresh(true);
      fetchData(currentFilterParams);
      dispatch(_staffPerkRedux.default.getGroupbuyBannerRequest());
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
        translucent: true,
        barStyle: barStyle
      }), (0, _jsxRuntime.jsx)(_stickyHeader.default, {
        navigation: navigation,
        vOffset: vOffset
      }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        data: _staffPerkCategoryListing2.ROOT_STRUCTURE,
        renderItem: function renderItem(_ref2) {
          var item = _ref2.item;
          return handleContent(item.id);
        },
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        },
        showsVerticalScrollIndicator: false,
        scrollEventThrottle: 16,
        stickyHeaderIndices: [1],
        viewabilityConfig: {
          viewAreaCoveragePercentThreshold: 10
        },
        onScroll: onScroll,
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: false,
          onRefresh: onRefresh,
          progressViewOffset: -300
        }),
        ref: rootListRef
      })]
    });
  };
  var _default = exports.default = StaffPerkCategoryListing;
