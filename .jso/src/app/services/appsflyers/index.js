  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  Object.defineProperty(exports, "appsFlyer", {
    enumerable: true,
    get: function get() {
      return _reactNativeAppsflyer.default;
    }
  });
  exports.useAppsFlyer = exports.initAppsFlyer = exports.appsFlyerOnInstallConversionData = exports.appsFlyerOnDeepLink = undefined;
  var _reactNativeAppsflyer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _appsflyers = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var appsFlyerOptions = {
    devKey: "B7YvgeKMXZX5oczMcZfXzh",
    isDebug: false,
    appId: "1562618613",
    onInstallConversionDataListener: true,
    //Optional
    onDeepLinkListener: true,
    //Optional
    timeToWaitForATTUserAuthorization: 10 //for iOS 14.5
  };
  var initAppsFlyer = exports.initAppsFlyer = function initAppsFlyer() {
    _reactNativeAppsflyer.default.initSdk(appsFlyerOptions, function (result) {
      console.log("AppsFlyer__result", result);
    }, function (error) {
      console.log("AppsFlyer__error", error);
    });
  };
  var appsFlyerOnDeepLink = exports.appsFlyerOnDeepLink = function appsFlyerOnDeepLink(callback) {
    _reactNativeAppsflyer.default.onDeepLink(function (res) {
      var _res$data;
      if ((res == null ? undefined : res.deepLinkStatus) !== "NOT_FOUND" && res != null && (_res$data = res.data) != null && _res$data.af_dp) {
        console.log("AppsFlyer__onDeepLink", res == null ? undefined : res.data);
        callback(res);
      }
    });
  };
  var appsFlyerOnInstallConversionData = exports.appsFlyerOnInstallConversionData = function appsFlyerOnInstallConversionData(callback) {
    // appsFlyer.onInstallConversionData(callback)
    _reactNativeAppsflyer.default.onInstallConversionData(function (res) {
      var _res$data2, _res$data3;
      if (res != null && (_res$data2 = res.data) != null && _res$data2.is_first_launch && JSON.parse(res == null || (_res$data3 = res.data) == null ? undefined : _res$data3.is_first_launch) == true) {
        var _res$data4, _res$data5;
        if ((res == null || (_res$data4 = res.data) == null ? undefined : _res$data4.af_status) === "Non-organic") {
          console.log("This is first launch and a Non-Organic install. Media source: ");
          callback(res);
        } else if ((res == null || (_res$data5 = res.data) == null ? undefined : _res$data5.af_status) === "Organic") {
          console.log("This is first launch and a Organic Install");
        }
      } else {
        console.log("This is not first launch");
      }
    });
  };
  var useAppsFlyer = exports.useAppsFlyer = function useAppsFlyer() {
    var _useAppsFlyerStore = (0, _appsflyers.useAppsFlyerStore)(),
      setDeepLinkData = _useAppsFlyerStore.setDeepLinkData;
    var handleDeepLink = function handleDeepLink(res) {
      setDeepLinkData(res == null ? undefined : res.data);
    };
    var handleInstallConversionData = function handleInstallConversionData(res) {
      setDeepLinkData(res == null ? undefined : res.data);
    };
    (0, _react.useEffect)(function () {
      appsFlyerOnDeepLink(handleDeepLink);
      appsFlyerOnInstallConversionData(handleInstallConversionData);
      initAppsFlyer();
    }, []);
  };
