  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.triggerSecurityEvent = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _loginHelper = _$$_REQUIRE(_dependencyMap[4]);
  var _deviceUtils = _$$_REQUIRE(_dependencyMap[5]);
  var _storage = _$$_REQUIRE(_dependencyMap[6]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[7]);
  var _analytics = _$$_REQUIRE(_dependencyMap[8]);
  var triggerSecurityEvent = exports.triggerSecurityEvent = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (SCREEN_NAME, EVENT_NAME, EVENT_DETAIL, profilePayload) {
      var _lastLoggedInUser;
      var isEmulator = yield (0, _deviceUtils.isBotDetected)();
      if (!isEmulator) return;
      var lastLoggedInUserEmail = "";
      var currentEmail = "";
      var lastLoggedInUser = null;
      var lastLoggedInTimeStamp = null;

      // if lastLoggedInUser is not null, we will get the last logged in userinfo.
      if (profilePayload) {
        var _yield$Promise$all = yield Promise.all([(0, _storage.loadFromEncryptedStorage)(_storageKey.StorageKey.lastLoggedInUser), (0, _storage.load)(_storageKey.StorageKey.lastLoggedInTimeStamp)]),
          _yield$Promise$all2 = (0, _slicedToArray2.default)(_yield$Promise$all, 2),
          lastUser = _yield$Promise$all2[0],
          lastTimeStamp = _yield$Promise$all2[1];
        lastLoggedInUser = lastUser;
        lastLoggedInTimeStamp = lastTimeStamp;
        lastLoggedInUserEmail = (0, _loginHelper.maskEmail)((lastUser == null ? undefined : lastUser.email) || "");
        currentEmail = (0, _loginHelper.maskEmail)((profilePayload == null ? undefined : profilePayload.email) || "");
      }
      (0, _analytics.dtBizEvent)(SCREEN_NAME, EVENT_NAME, _analytics.DT_SECURITY_EVENT, Object.assign({}, profilePayload && (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _analytics.SECURITY_EVENT_KEYS.LAST_LOGGED_IN_EMAIL, lastLoggedInUserEmail), _analytics.SECURITY_EVENT_KEYS.LAST_LOGGED_IN_UID, (_lastLoggedInUser = lastLoggedInUser) == null ? undefined : _lastLoggedInUser.uid), _analytics.SECURITY_EVENT_KEYS.UID, profilePayload == null ? undefined : profilePayload.id), _analytics.SECURITY_EVENT_KEYS.LAST_LOGGED_IN_TIMESTAMP, lastLoggedInTimeStamp), _analytics.SECURITY_EVENT_KEYS.CURRENT_EMAIL, currentEmail), (0, _defineProperty2.default)({}, _analytics.SECURITY_EVENT_KEYS.DETAIL, EVENT_DETAIL)));
    });
    return function triggerSecurityEvent(_x, _x2, _x3, _x4) {
      return _ref.apply(this, arguments);
    };
  }();
