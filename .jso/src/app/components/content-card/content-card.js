  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ContentCard = ContentCard;
  exports.defaultView = defaultView;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _button = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _contentCard = _$$_REQUIRE(_dependencyMap[8]);
  var _arrowRight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: styles.lightGreyLoadingColors,
        shimmerStyle: styles.imageStyle
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.skeletonTextContainerStyle,
        children: styles.skeletonLayout.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: styles.lighterGreyLoadingColors,
              shimmerStyle: item
            })
          }, index);
        })
      })]
    });
  };
  var loadDescription = function loadDescription(description, _ref) {
    var testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel;
    if (!description) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_text.Text, {
      text: description,
      preset: "caption1Regular",
      numberOfLines: 3,
      testID: `${testID}__Description`,
      accessibilityLabel: `${accessibilityLabel}__Description`
    });
  };

  /**
   * Describe your component here
   */
  function defaultView(props) {
    var type = props.type,
      titleName = props.titleName,
      link = props.link,
      description = props.description,
      imageUrl = props.imageUrl,
      onPressed = props.onPressed,
      _props$numberOfLinesT = props.numberOfLinesTitle,
      numberOfLinesTitle = _props$numberOfLinesT === undefined ? 1 : _props$numberOfLinesT,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ContentCard" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ContentCard" : _props$accessibilityL;
    var source = imageUrl ? {
      uri: imageUrl
    } : _$$_REQUIRE(_dependencyMap[13]);
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: props.shadow ? styles.containerWithShadow : {},
      onPress: onPressed,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        style: styles.imageStyle,
        source: source,
        testID: `${testID}__Image`,
        accessibilityLabel: `${accessibilityLabel}__Image`
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.textContainerStyle,
        children: [type === _contentCard.ContentCardType.titleLink ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.titleLinkDescriptionContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: titleName,
            preset: "bodyTextBold",
            numberOfLines: 2,
            style: styles.titleStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.linkStyle,
            children: [(0, _jsxRuntime.jsx)(_button.Button, {
              style: styles.buttonStyle,
              typePreset: "textLink",
              text: link,
              sizePreset: "small",
              statePreset: "disabled",
              testID: `${testID}__TextLink`,
              accessibilityLabel: `${accessibilityLabel}__TextLink`
            }), (0, _jsxRuntime.jsx)(_arrowRight.default, {
              style: styles.iconStyle
            })]
          })]
        }) : null, type === _contentCard.ContentCardType.descriptionLink ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.titleLinkDescriptionContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: description,
            preset: "bodyTextRegular",
            numberOfLines: 2,
            style: styles.textDescribeStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.linkStyle,
            children: [(0, _jsxRuntime.jsx)(_button.Button, {
              style: styles.buttonStyle,
              typePreset: "textLink",
              text: link,
              sizePreset: "small",
              statePreset: "disabled",
              testID: `${testID}__TextLink`,
              accessibilityLabel: `${accessibilityLabel}__TextLink`
            }), (0, _jsxRuntime.jsx)(_arrowRight.default, {
              style: styles.iconStyle
            })]
          })]
        }) : null, type === _contentCard.ContentCardType.description ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.titleLinkDescriptionContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: description,
            preset: "bodyTextRegular",
            numberOfLines: 3,
            style: styles.descriptionTextStyle,
            testID: `${testID}__Description`,
            accessibilityLabel: `${accessibilityLabel}__Description`
          })
        }) : null, type === _contentCard.ContentCardType.titleDescription ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.titleDescriptionContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: titleName,
            preset: "bodyTextBold",
            numberOfLines: numberOfLinesTitle,
            style: styles.titleStyle
          }), loadDescription(description, {
            testID: testID,
            accessibilityLabel: accessibilityLabel
          })]
        }) : null]
      })]
    });
  }
  function ContentCard(props) {
    var isLoading = props.state === _contentCard.ContentCardState.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.cardContainer,
      children: isLoading ? loadingView() : defaultView(props)
    });
  }
