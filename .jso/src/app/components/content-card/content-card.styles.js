  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.titleStyle = exports.titleLinkDescriptionContainer = exports.titleDescriptionContainer = exports.textDescribeStyle = exports.textContainerStyle = exports.skeletonTextContainerStyle = exports.skeletonLayout = exports.linkStyle = exports.lighterGreyLoadingColors = exports.lightGreyLoadingColors = exports.imageStyle = exports.iconStyle = exports.descriptionTextStyle = exports.containerWithShadow = exports.cardContainer = exports.buttonStyle = undefined;
  var _color = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var cardContainer = exports.cardContainer = {
    height: 222,
    width: 192
  };
  var containerWithShadow = exports.containerWithShadow = Object.assign({}, _theme.shadow.primaryShadow, {
    borderRadius: 16
  });
  var imageStyle = exports.imageStyle = {
    height: 120,
    width: 192,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    backgroundColor: _color.color.palette.lighterGrey
  };
  var titleLinkDescriptionContainer = exports.titleLinkDescriptionContainer = {
    marginTop: 16,
    marginLeft: 16,
    marginRight: 16
  };
  var titleDescriptionContainer = exports.titleDescriptionContainer = Object.assign({}, titleLinkDescriptionContainer, {
    marginTop: 12
  });
  var textContainerStyle = exports.textContainerStyle = {
    backgroundColor: _color.color.palette.whiteGrey,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    height: 102
  };
  var skeletonTextContainerStyle = exports.skeletonTextContainerStyle = Object.assign({}, textContainerStyle, {
    height: 90
  });
  var descriptionTextStyle = exports.descriptionTextStyle = {
    color: _color.color.palette.almostBlackGrey
  };
  var titleStyle = exports.titleStyle = Object.assign({}, descriptionTextStyle, {
    marginBottom: 4
  });
  var buttonStyle = exports.buttonStyle = {
    backgroundColor: _color.color.palette.whiteGrey
  };
  var iconStyle = exports.iconStyle = {
    marginLeft: 8,
    marginTop: 9
  };
  var linkStyle = exports.linkStyle = {
    flexDirection: "row",
    width: 138
  };
  var textDescribeStyle = exports.textDescribeStyle = {
    color: _color.color.palette.almostBlackGrey,
    marginBottom: 4
  };
  var lightGreyLoadingColors = exports.lightGreyLoadingColors = [_color.color.palette.lightGrey, _color.color.background, _color.color.palette.lightGrey];
  var lighterGreyLoadingColors = exports.lighterGreyLoadingColors = [_color.color.palette.lighterGrey, _color.color.background, _color.color.palette.lighterGrey];
  var skeletonLayout = exports.skeletonLayout = [{
    backgroundColor: _color.color.palette.lighterGrey,
    width: 160,
    height: 13,
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 17
  }, {
    backgroundColor: _color.color.palette.lighterGrey,
    width: 160,
    height: 13,
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 8
  }, {
    backgroundColor: _color.color.palette.lighterGrey,
    width: 104,
    height: 13,
    marginTop: 8,
    borderRadius: 4,
    marginLeft: 16
  }];
