  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.NoPermissionNoti = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _text2 = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var NoPermissionNoti = exports.NoPermissionNoti = _react.default.memo(function (props) {
    var containerStyle = props.containerStyle;
    var navigateToSetting = function navigateToSetting() {
      (0, _reactNativePermissions.openSettings)();
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: [styles.container, containerStyle],
      children: [(0, _jsxRuntime.jsx)(_icons.NoPermissionNotiContent, {
        width: "40",
        height: "40"
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewContent,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "notification.noPermissionNotiContent",
          style: styles.txtContent
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.viewRow,
          children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.button,
            onPress: navigateToSetting,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              tx: "notification.allowNotiButton",
              style: styles.txtButton
            }), (0, _jsxRuntime.jsx)(_icons.ArrowRightV2, {
              color: _theme.color.palette.lightPurple
            })]
          })
        })]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: '100%',
      paddingVertical: 16,
      paddingHorizontal: 20,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: _theme.color.palette.whiteGrey,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        width: 0,
        height: 1
      },
      shadowOpacity: 0.1,
      shadowRadius: 2.22,
      elevation: 3
    },
    viewContent: {
      marginLeft: 16,
      flex: 1
    },
    txtContent: Object.assign({}, _text2.presets.caption1Regular),
    txtButton: {
      fontSize: 14,
      lineHeight: 18,
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.lightPurple,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      marginRight: 8
    },
    button: {
      marginTop: 13,
      paddingVertical: 5,
      paddingHorizontal: 10,
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderRadius: 60,
      borderColor: _theme.color.palette.purpleD5BBEA
    },
    viewRow: {
      flexDirection: 'row'
    }
  });
