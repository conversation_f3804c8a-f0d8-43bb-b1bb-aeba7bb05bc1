  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TabNavBarWithTopIconAnimationV2 = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var focusTabIconColor = {
    color: _theme.color.palette.purple8F58BE
  };
  var unfocusTabIconColor = {
    color: _theme.color.palette.midGrey
  };
  function tabItemOnPress(navigation, route, isFocused) {
    var event = navigation == null ? undefined : navigation.emit({
      type: "tabPress",
      target: route == null ? undefined : route.key
    });
    if (!isFocused && !event.defaultPrevented) {
      navigation == null || navigation.navigate(route == null ? undefined : route.name);
    }
  }
  function tabItemOnLongPress(navigation, route) {
    navigation == null || navigation.emit({
      type: "tabLongPress",
      target: route == null ? undefined : route.key
    });
  }
  var _worklet_17447039465353_init_data = {
    code: "function indexTsx1(){const{interpolate,animatedProgress,ANIMATION_VALUE,Extrapolation}=this.__closure;return{height:interpolate(animatedProgress.value,[0,1],[ANIMATION_VALUE.HEIGHT.START_VALUE,ANIMATION_VALUE.HEIGHT.END_VALUE],Extrapolation.CLAMP)};}"
  };
  var _worklet_10009161202799_init_data = {
    code: "function indexTsx2(){const{interpolate,animatedProgress,Extrapolation}=this.__closure;return{height:interpolate(animatedProgress.value,[0,1],[30,0],Extrapolation.CLAMP),opacity:interpolate(animatedProgress.value,[0,1],[1,0],Extrapolation.CLAMP),transform:[{scale:interpolate(animatedProgress.value,[0,1],[1,0],Extrapolation.CLAMP)}]};}"
  };
  var TabNavBarWithTopIconAnimationV2 = exports.TabNavBarWithTopIconAnimationV2 = function TabNavBarWithTopIconAnimationV2(topTabProps) {
    var _topTabProps$props = topTabProps.props,
      state = _topTabProps$props.state,
      descriptors = _topTabProps$props.descriptors,
      navigation = _topTabProps$props.navigation;
    var topTabParentStyle = topTabProps.topTabParentStyle,
      topTabTouchableOpacityStyle = topTabProps.topTabTouchableOpacityStyle,
      _topTabProps$topTabLa = topTabProps.topTabLabelsPresets,
      topTabLabelsPresets = _topTabProps$topTabLa === undefined ? "tabsSmall" : _topTabProps$topTabLa,
      _topTabProps$topTabAc = topTabProps.topTabActiveLabelStyle,
      topTabActiveLabelStyle = _topTabProps$topTabAc === undefined ? styles.topTabActiveLabelStyle : _topTabProps$topTabAc,
      _topTabProps$topTabIn = topTabProps.topTabInActiveLabelStyle,
      topTabInActiveLabelStyle = _topTabProps$topTabIn === undefined ? styles.topTabInActiveLabelStyle : _topTabProps$topTabIn,
      _topTabProps$topTabAc2 = topTabProps.topTabActiveIndicatorStyle,
      topTabActiveIndicatorStyle = _topTabProps$topTabAc2 === undefined ? styles.topTabActiveIndicatorStyle : _topTabProps$topTabAc2,
      _topTabProps$topIconA = topTabProps.topIconActiveStyle,
      topIconActiveStyle = _topTabProps$topIconA === undefined ? focusTabIconColor : _topTabProps$topIconA,
      _topTabProps$topIconI = topTabProps.topIconInActiveStyle,
      topIconInActiveStyle = _topTabProps$topIconI === undefined ? unfocusTabIconColor : _topTabProps$topIconI,
      topTabInActiveIndicatorStyle = topTabProps.topTabInActiveIndicatorStyle,
      topTabLabelStyleAnimation = topTabProps.topTabLabelStyleAnimation,
      callBackPress = topTabProps.callBackPress,
      animatedProgress = topTabProps.animatedProgress;
    var animatedTabContainerStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        return {
          height: (0, _reactNativeReanimated.interpolate)(animatedProgress.value, [0, 1], [_constants.ANIMATION_VALUE.HEIGHT.START_VALUE, _constants.ANIMATION_VALUE.HEIGHT.END_VALUE], _reactNativeReanimated.Extrapolation.CLAMP)
        };
      };
      indexTsx1.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animatedProgress: animatedProgress,
        ANIMATION_VALUE: _constants.ANIMATION_VALUE,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      indexTsx1.__workletHash = 17447039465353;
      indexTsx1.__initData = _worklet_17447039465353_init_data;
      return indexTsx1;
    }());
    var animatedIconStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx2 = function indexTsx2() {
        return {
          height: (0, _reactNativeReanimated.interpolate)(animatedProgress.value, [0, 1], [30, 0], _reactNativeReanimated.Extrapolation.CLAMP),
          opacity: (0, _reactNativeReanimated.interpolate)(animatedProgress.value, [0, 1], [1, 0], _reactNativeReanimated.Extrapolation.CLAMP),
          transform: [{
            scale: (0, _reactNativeReanimated.interpolate)(animatedProgress.value, [0, 1], [1, 0], _reactNativeReanimated.Extrapolation.CLAMP)
          }]
        };
      };
      indexTsx2.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animatedProgress: animatedProgress,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      indexTsx2.__workletHash = 10009161202799;
      indexTsx2.__initData = _worklet_10009161202799_init_data;
      return indexTsx2;
    }());
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      style: [topTabParentStyle || styles.topTabParentStyle, animatedTabContainerStyle],
      children: state.routes.map(function (route, index) {
        var options = descriptors[route.key].options;
        var label;
        var TabIcon;
        if (options.tabBarLabel) {
          label = options.tabBarLabel;
        } else {
          if (options.title !== undefined) {
            label = options.title;
          } else {
            label = route.name;
          }
        }
        if (options.tabBarIcon) {
          TabIcon = options.tabBarIcon;
        }
        var isFocused = state.index === index;
        var onPress = function onPress() {
          tabItemOnPress(navigation, route, isFocused);
          if (callBackPress) {
            callBackPress(route == null ? undefined : route.name, isFocused);
          }
        };
        var onLongPress = function onLongPress() {
          tabItemOnLongPress(navigation, route);
          if (callBackPress) {
            callBackPress(route == null ? undefined : route.name, isFocused);
          }
        };
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          accessibilityRole: "button",
          accessibilityLabel: options.tabBarAccessibilityLabel || `${label}__TabButton`,
          testID: options.tabBarTestID || `${label}__TabButton`,
          onPress: onPress,
          onLongPress: onLongPress,
          style: topTabTouchableOpacityStyle || styles.topTabTouchableOpacityStyle,
          children: (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
            style: styles.tabContentWrap,
            children: [(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
              style: styles.iConAndTextContainer,
              children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
                style: animatedIconStyle,
                children: (0, _jsxRuntime.jsx)(TabIcon, {
                  fill: "currentColor",
                  style: isFocused ? topIconActiveStyle : topIconInActiveStyle
                })
              }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
                style: topTabLabelStyleAnimation,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  style: isFocused ? topTabActiveLabelStyle : topTabInActiveLabelStyle,
                  preset: topTabLabelsPresets,
                  text: label
                })
              })]
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: isFocused ? topTabActiveIndicatorStyle : topTabInActiveIndicatorStyle
            })]
          })
        }, index);
      })
    });
  };
