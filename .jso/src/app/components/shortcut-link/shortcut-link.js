  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _shortcutLink = _$$_REQUIRE(_dependencyMap[7]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _shortcutLink2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _native = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayoutImage = [{
    height: 72,
    width: 72,
    borderRadius: 36
  }];
  var skeletonLayoutLabel = [{
    width: 67,
    height: 13,
    borderRadius: 4,
    marginTop: 11
  }];
  var loadingView = function loadingView(props) {
    var shimmerColorsSecondary = props.shimmerColorsSecondary;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _shortcutLink2.default.itemContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: shimmerColorsSecondary || lighterGreyLoadingColors,
        shimmerStyle: skeletonLayoutImage[0]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: shimmerColorsSecondary || lighterGreyLoadingColors,
        shimmerStyle: skeletonLayoutLabel[0]
      })]
    });
  };
  var defaultView = function defaultView(props) {
    var onPressed = props.onPressed,
      label = props.label,
      title = props.title,
      icon = props.icon,
      style = props.style,
      imageImport = props.imageImport,
      disabled = props.disabled,
      isCustomIcon = props.isCustomIcon,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ShortcutLink" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ShortcutLink" : _props$accessibilityL;
    var ImageIP = imageImport;
    var Icon = icon;
    var lottieRef = React.useRef(null);
    var getStylesOpacity = React.useMemo(function () {
      if (!disabled) {
        return {
          opacity: 1
        };
      }
      return {
        opacity: 0.5
      };
    }, [disabled]);
    (0, _native.useFocusEffect)(React.useCallback(function () {
      if (_reactNative2.Platform.OS === 'ios') {
        var _lottieRef$current;
        (_lottieRef$current = lottieRef.current) == null || _lottieRef$current.play();
      }
    }, [lottieRef]));
    var renderIcon = React.useMemo(function () {
      if (!icon) {
        return null;
      } else if (icon.toString().endsWith(".json") || icon.toString().endsWith(".lottie")) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _shortcutLink2.default.lottieViewContainer,
          children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
            style: _shortcutLink2.default.lottieStyle,
            ref: lottieRef,
            source: {
              uri: encodeURI(icon)
            },
            autoPlay: true,
            loop: true
          })
        });
      } else if (isCustomIcon) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [_shortcutLink2.default.imageBgStyle, getStylesOpacity, style],
          children: (0, _jsxRuntime.jsx)(Icon, {
            width: 72,
            height: 72
          })
        });
      } else {
        return (0, _jsxRuntime.jsx)(_baseImage.default, {
          style: [_shortcutLink2.default.imageBgStyle, getStylesOpacity, style],
          source: {
            uri: icon
          }
        });
      }
    }, [icon, isCustomIcon, getStylesOpacity, style]);
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: function onPress() {
        return onPressed == null ? undefined : onPressed(props);
      },
      style: _shortcutLink2.default.itemContainerStyle,
      disabled: disabled,
      testID: `${testID}__TouchableShortLink`,
      accessibilityLabel: `${accessibilityLabel}__TouchableShortLink`,
      accessible: false,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _shortcutLink2.default.imageBackgroundStyle,
        children: [!!imageImport && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _shortcutLink2.default.imageBgStyle,
          children: (0, _jsxRuntime.jsx)(ImageIP, {})
        }), renderIcon, label ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [_shortcutLink2.default.labelContainerStyle, getStylesOpacity],
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: label,
            preset: "XSmallBold",
            style: _shortcutLink2.default.newLabelStyle,
            numberOfLines: 1
          })
        }) : null]
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        text: title,
        preset: "caption2Bold",
        style: [_shortcutLink2.default.labelStyle, getStylesOpacity],
        numberOfLines: 2
      })]
    });
  };
  var ShortcutLink = function ShortcutLink(props) {
    var isLoading = _shortcutLink.ShortcutLinkType.loading === props.type;
    var containerStyle = props.containerStyle;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: [_shortcutLink2.default.containerStyleBase, containerStyle],
      children: isLoading ? loadingView(props) : defaultView(props)
    });
  };
  var _default = exports.default = React.memo(ShortcutLink);
