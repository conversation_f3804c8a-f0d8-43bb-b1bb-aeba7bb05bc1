  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = _reactNative.StyleSheet.create({
    containerStyleBase: {
      flex: 1
    },
    imageBackgroundStyle: {
      alignItems: "center",
      height: 72,
      justifyContent: "center",
      width: 72
    },
    imageBgStyle: {
      alignItems: "center",
      borderRadius: 36,
      height: 72,
      justifyContent: "center",
      width: 72
    },
    itemContainerStyle: {
      alignItems: "center",
      height: "auto",
      width: 96
    },
    labelContainerStyle: {
      backgroundColor: _theme.color.palette.lightRed,
      borderRadius: 4,
      bottom: 3.5,
      height: 18,
      justifyContent: "center",
      position: "absolute",
      right: -2
    },
    labelStyle: {
      alignSelf: "center",
      marginTop: 8,
      paddingHorizontal: 1,
      textAlign: "center",
      width: "80%"
    },
    newLabelStyle: {
      color: _theme.color.palette.whiteGrey,
      paddingHorizontal: 4
    },
    lottieViewContainer: {
      width: 72,
      height: 72,
      backgroundColor: 'white',
      borderRadius: 36,
      justifyContent: 'center',
      alignItems: 'center'
    },
    lottieStyle: {
      width: '100%',
      height: '100%'
    }
  });
  var _default = exports.default = styles;
