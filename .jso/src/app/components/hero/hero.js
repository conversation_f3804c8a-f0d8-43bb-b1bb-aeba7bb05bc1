  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Hero = Hero;
  exports.HeroType = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeSwiper = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _paginationIndicator = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _wave = _$$_REQUIRE(_dependencyMap[8]);
  var _utils = _$$_REQUIRE(_dependencyMap[9]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var containerStyle = {
    height: 270,
    backgroundColor: _theme.color.palette.lightGrey
  };
  var containerStyleWithoutWave = {
    height: 270,
    backgroundColor: _theme.color.palette.lightGrey,
    marginBottom: 24
  };
  var backgroundImageStyle = {
    width: "100%",
    height: 270
  };
  var loadingView = {
    flex: 1,
    left: 165,
    top: 99
  };
  var logoImageStyle = {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 40,
    bottom: 59,
    left: 25
  };
  var logoImageStyleWithoutWave = {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 40,
    bottom: 12,
    left: 16
  };
  var waveShapeStyle = {
    position: "absolute",
    left: 0,
    bottom: 0,
    width: "100%",
    tintColor: _theme.color.palette.whiteGrey
  };
  var paginationStyle = {
    position: "absolute",
    bottom: 59,
    right: 16
  };
  var paginationStyleWithout = {
    position: "absolute",
    bottom: 12,
    right: 16
  };
  var showCustomPagination = function showCustomPagination(index, total) {
    var label = `${index + 1}/${total}`;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: paginationStyle,
      children: (0, _jsxRuntime.jsx)(_paginationIndicator.PaginationIndicator, {
        label: label
      })
    });
  };
  var showCustomPaginationWithoutWave = function showCustomPaginationWithoutWave(index, total) {
    var label = `${index + 1}/${total}`;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: paginationStyleWithout,
      children: (0, _jsxRuntime.jsx)(_paginationIndicator.PaginationIndicator, {
        label: label
      })
    });
  };
  var HeroType = exports.HeroType = /*#__PURE__*/function (HeroType) {
    HeroType["default"] = "default";
    HeroType["loading"] = "loading";
    return HeroType;
  }({});
  /**
   * Describe your component here
   */
  function Hero(props) {
    var logoImageUrl = props.logoImageUrl,
      _props$heroImagesUrl = props.heroImagesUrl,
      heroImagesUrl = _props$heroImagesUrl === undefined ? [] : _props$heroImagesUrl,
      type = props.type,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "Hero" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "Hero" : _props$accessibilityL,
      _props$showWave = props.showWave,
      showWave = _props$showWave === undefined ? true : _props$showWave,
      onSwiperChange = props.onSwiperChange;
    var isLoading = type === HeroType.loading;
    var sortedImages = (0, _toConsumableArray2.default)(heroImagesUrl).sort(function (a, b) {
      return a.orderId - b.orderId;
    });
    var shouldShowPagniation = heroImagesUrl.length > 1;
    var onIndexChanged = function onIndexChanged() {
      if (onSwiperChange) {
        onSwiperChange();
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: (0, _utils.handleCondition)(showWave, containerStyle, containerStyleWithoutWave),
      children: [isLoading ? (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: loadingView,
        children: (0, _jsxRuntime.jsx)(_icons.Photo, {})
      }) : (0, _jsxRuntime.jsx)(_reactNativeSwiper.default, {
        renderPagination: (0, _utils.handleCondition)(showWave, showCustomPagination, showCustomPaginationWithoutWave),
        showsPagination: shouldShowPagniation != null ? shouldShowPagniation : false,
        testID: `${testID}__Swiper`,
        accessibilityLabel: `${accessibilityLabel}__Swiper`,
        onIndexChanged: onIndexChanged,
        children: sortedImages.map(function (item) {
          return (0, _jsxRuntime.jsx)(_baseImage.default, {
            style: backgroundImageStyle,
            source: {
              uri: item.backgroundImageUrl
            }
          }, item.orderId);
        })
      }), showWave && !isLoading && (0, _jsxRuntime.jsx)(_wave.Wave, {
        style: waveShapeStyle,
        fill: "currentColor"
      }), (0, _utils.handleCondition)(logoImageUrl, (0, _jsxRuntime.jsx)(_baseImage.default, {
        style: showWave ? logoImageStyle : logoImageStyleWithoutWave,
        source: {
          uri: logoImageUrl
        }
      }), null)]
    });
  }
