  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.NoShadowVariations = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _timelineFlyTiles = _$$_REQUIRE(_dependencyMap[2]);
  var _variation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _variation2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _variation3 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _variation4 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _variation5 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var TimelineFlyVariations = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _timelineFlyTiles.TimelineFlyTilesVariations.variation1, _variation.default), _timelineFlyTiles.TimelineFlyTilesVariations.variation2, _variation2.default), _timelineFlyTiles.TimelineFlyTilesVariations.variation3, _variation3.default), _timelineFlyTiles.TimelineFlyTilesVariations.variation4, _variation4.default), _timelineFlyTiles.TimelineFlyTilesVariations.variation5, _variation5.default);
  var NoShadowVariations = exports.NoShadowVariations = [_variation4.default];
  var _default = exports.default = TimelineFlyVariations;
