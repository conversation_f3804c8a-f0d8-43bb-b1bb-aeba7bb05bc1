  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = TimelineFlyVariation4;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _productOffer = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _productOffer2 = _$$_REQUIRE(_dependencyMap[8]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  function TimelineFlyVariation4(_ref) {
    var imageUrl = _ref.imageUrl,
      title = _ref.title,
      data = _ref.data,
      onPress = _ref.onPress;
    return (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
      style: styles.container,
      start: {
        x: 0,
        y: 1
      },
      end: {
        x: 1,
        y: 1
      },
      colors: _theme.color.purpleGradientColor,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.header,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          style: styles.image,
          source: {
            uri: imageUrl
          }
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.title,
          text: title,
          numberOfLines: 1
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        contentContainerStyle: styles.listContainer,
        data: data,
        renderItem: function renderItem(_ref2) {
          var item = _ref2.item;
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.listItem,
            children: (0, _jsxRuntime.jsx)(_productOffer.ProductOffer, Object.assign({
              type: _productOffer2.ProductOfferType.default,
              onPressed: function onPressed() {
                return onPress(item);
              }
            }, item))
          });
        },
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        }
      })]
    });
  }
