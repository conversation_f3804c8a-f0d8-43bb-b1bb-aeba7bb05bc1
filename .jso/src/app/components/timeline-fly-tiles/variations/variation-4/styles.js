  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.title = exports.listItem = exports.listContainer = exports.image = exports.header = exports.container = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var container = exports.container = {
    paddingVertical: 24,
    paddingRight: 0,
    backgroundColor: _theme.color.palette.lightPurple,
    borderTopStartRadius: 16,
    borderBottomStartRadius: 16,
    overflow: "hidden"
  };
  var header = exports.header = {
    marginHorizontal: 24,
    flexDirection: "row",
    alignItems: "center"
  };
  var image = exports.image = {
    marginEnd: 12,
    width: 60,
    height: 60,
    borderRadius: 12,
    resizeMode: "cover",
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var title = exports.title = Object.assign({}, _text.presets.subTitleBold, {
    lineHeight: 18,
    color: _theme.color.palette.whiteGrey,
    flex: 1,
    marginTop: 8
  });
  var listContainer = exports.listContainer = {
    paddingLeft: 24,
    marginTop: 16
  };
  var listItem = exports.listItem = {
    marginRight: 16
  };
