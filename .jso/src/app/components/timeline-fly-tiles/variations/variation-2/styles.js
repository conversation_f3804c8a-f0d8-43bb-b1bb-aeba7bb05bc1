  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.title = exports.sublabel = exports.linksContainer = exports.linkWrapper = exports.label = exports.image = exports.header = exports.description = exports.container = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var container = exports.container = {
    paddingTop: 24,
    paddingHorizontal: 24,
    paddingBottom: 20
  };
  var header = exports.header = {
    flexDirection: "row",
    alignItems: "center"
  };
  var image = exports.image = {
    marginRight: 12,
    width: 60,
    height: 60,
    borderRadius: 12,
    resizeMode: "cover",
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var title = exports.title = Object.assign({}, _text.presets.subTitleBold, {
    lineHeight: 22,
    color: _theme.color.palette.almostBlackGrey,
    flex: 1,
    marginTop: 8
  });
  var description = exports.description = Object.assign({}, _text.presets.caption1Regular, {
    marginTop: 12
  });
  var linksContainer = exports.linksContainer = {
    flexDirection: "row",
    marginTop: 12
  };
  var linkWrapper = exports.linkWrapper = {
    flex: 1
  };
  var label = exports.label = Object.assign({}, _text.presets.textLink, {
    marginTop: 2
  });
  var sublabel = exports.sublabel = Object.assign({}, _text.presets.XSmallRegular, {
    color: _theme.color.palette.darkestGrey
  });
