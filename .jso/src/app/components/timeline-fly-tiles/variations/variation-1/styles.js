  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.title = exports.link = exports.image = exports.header = exports.description = exports.container = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var container = exports.container = {
    padding: 24
  };
  var header = exports.header = {
    flexDirection: "row",
    alignItems: "center"
  };
  var image = exports.image = {
    marginEnd: 12,
    width: 60,
    height: 60,
    borderRadius: 12,
    resizeMode: "cover",
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var title = exports.title = Object.assign({}, _text.presets.subTitleBold, {
    lineHeight: 18,
    color: _theme.color.palette.almostBlackGrey,
    flex: 1,
    marginTop: 8
  });
  var description = exports.description = Object.assign({}, _text.presets.caption1Regular, {
    marginTop: 8
  });
  var link = exports.link = Object.assign({}, _text.presets.textLink, {
    marginTop: 8
  });
