  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = TimelineFlyVariation1;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  function TimelineFlyVariation1(_ref) {
    var imageUrl = _ref.imageUrl,
      title = _ref.title,
      description = _ref.description,
      linkLabel = _ref.linkLabel,
      onPress = _ref.onPress,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? "TimelineFlyVariation1" : _ref$testID,
      _ref$accessibilityLab = _ref.accessibilityLabel,
      accessibilityLabel = _ref$accessibilityLab === undefined ? "TimelineFlyVariation1" : _ref$accessibilityLab;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: styles.container,
      disabled: !linkLabel,
      onPress: onPress,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.header,
        children: [!!imageUrl && (0, _jsxRuntime.jsx)(_baseImage.default, {
          style: styles.image,
          source: {
            uri: imageUrl
          },
          testID: `${testID}__Image`,
          accessibilityLabel: `${accessibilityLabel}__Image`
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.title,
          text: title
        })]
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.description,
        text: description,
        testID: `${testID}__Description`,
        accessibilityLabel: `${accessibilityLabel}__Description`
      }), !!linkLabel && (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.link,
        text: linkLabel,
        testID: `${testID}__Label`,
        accessibilityLabel: `${accessibilityLabel}__Label`
      })]
    });
  }
