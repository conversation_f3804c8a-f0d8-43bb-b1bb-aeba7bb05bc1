  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.TimelineFlyVariation3 = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _contentCard = _$$_REQUIRE(_dependencyMap[4]);
  var _contentCard2 = _$$_REQUIRE(_dependencyMap[5]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TimelineFlyVariation3 = exports.TimelineFlyVariation3 = function TimelineFlyVariation3(_ref) {
    var title = _ref.title,
      data = _ref.data,
      onPress = _ref.onPress,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? "TimelineFlyVariation3" : _ref$testID,
      _ref$accessibilityLab = _ref.accessibilityLabel,
      accessibilityLabel = _ref$accessibilityLab === undefined ? "TimelineFlyVariation3" : _ref$accessibilityLab;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.titleStyle,
        text: title,
        numberOfLines: 1,
        testID: `${testID}__Title`,
        accessibilityLabel: `${accessibilityLabel}__Title`
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        contentContainerStyle: styles.contentContainerStyle,
        data: data,
        renderItem: function renderItem(_ref2) {
          var item = _ref2.item;
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.contentViewStyle,
            children: (0, _jsxRuntime.jsx)(_contentCard.ContentCard, {
              shadow: true,
              state: _contentCard2.ContentCardState.default,
              onPressed: function onPressed() {
                return onPress(item);
              },
              imageUrl: item == null ? undefined : item.imageUrl,
              titleName: item == null ? undefined : item.titleName,
              type: _contentCard2.ContentCardType.titleDescription,
              numberOfLinesTitle: 3,
              testID: `${testID}__Card`,
              accessibilityLabel: `${accessibilityLabel}__Card`
            })
          });
        },
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        }
      })]
    });
  };
  var _default = exports.default = TimelineFlyVariation3;
