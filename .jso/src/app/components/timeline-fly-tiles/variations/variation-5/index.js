  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = TimelineFlyVariation5;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _passes = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var cardWidthWhenMoreThan1 = width * 0.95;
  var limitation = 5;
  function TimelineFlyVariation5(_ref) {
    var imageUrl = _ref.imageUrl,
      title = _ref.title,
      description = _ref.description,
      cards = _ref.cards,
      addLabel = _ref.addLabel,
      onAdd = _ref.onAdd,
      onViewAll = _ref.onViewAll;
    var data = (0, _react.useMemo)(function () {
      return cards.slice(0, limitation);
    }, [cards]);
    var isMoreThan1 = data.length > 1;
    var isMoreThanLimitation = cards.length > data.length;
    var Button = function Button() {
      if (!cards[0]) {
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.addContainer,
          onPress: onAdd,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.addLink,
            text: addLabel
          })
        });
      }
      if (cards[0]) {
        return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: styles.addContainer,
          onPress: onAdd,
          children: [(0, _jsxRuntime.jsx)(_icons.Add, {
            width: "24",
            height: "24"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.addText,
            text: addLabel
          })]
        });
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.header,
        children: [!!imageUrl && (0, _jsxRuntime.jsx)(_baseImage.default, {
          style: styles.image,
          source: {
            uri: imageUrl
          }
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.title,
          text: title,
          numberOfLines: 2
        })]
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.description,
        text: description,
        numberOfLines: 3
      }), !!cards[0] && (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        style: styles.scrollContainer,
        contentContainerStyle: styles.contentContainer,
        data: data,
        renderItem: function renderItem(_ref2) {
          var item = _ref2.item;
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: Object.assign({}, styles.item, isMoreThan1 ? styles.itemWithGutter : {}, isMoreThan1 ? {
              width: cardWidthWhenMoreThan1
            } : {}),
            children: (0, _jsxRuntime.jsx)(_passes.Passes, Object.assign({}, item))
          });
        },
        keyExtractor: function keyExtractor(_, index) {
          return `${index}`;
        },
        ListFooterComponent: function ListFooterComponent() {
          return isMoreThanLimitation && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.viewAllContainer,
            onPress: onViewAll,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              tx: "timelineFlyTiles.linkText",
              style: styles.viewAllText
            }), (0, _jsxRuntime.jsx)(_icons.ArrowRight, {
              width: "24",
              height: "24"
            })]
          });
        },
        showsHorizontalScrollIndicator: false,
        horizontal: true
      }), !!addLabel && (0, _jsxRuntime.jsx)(Button, {})]
    });
  }
