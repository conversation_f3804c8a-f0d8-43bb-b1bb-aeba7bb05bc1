  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.viewAllText = exports.viewAllContainer = exports.title = exports.scrollContainer = exports.itemWithGutter = exports.item = exports.image = exports.header = exports.description = exports.contentContainer = exports.container = exports.addText = exports.addLink = exports.addContainer = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var container = exports.container = {
    paddingTop: 24,
    paddingBottom: 16
  };
  var header = exports.header = {
    flexDirection: "row",
    paddingHorizontal: 24
  };
  var image = exports.image = {
    marginEnd: 12,
    width: 60,
    height: 60,
    borderRadius: 12,
    resizeMode: "cover",
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var title = exports.title = Object.assign({}, _text.presets.subTitleBold, {
    flex: 1,
    fontWeight: "normal",
    color: _theme.color.palette.almostBlackGrey,
    marginTop: 8
  });
  var description = exports.description = Object.assign({}, _text.presets.caption1Regular, {
    marginTop: 10,
    paddingHorizontal: 24
  });
  var scrollContainer = exports.scrollContainer = {
    marginTop: 12
  };
  var contentContainer = exports.contentContainer = {
    minWidth: "100%",
    alignItems: "center",
    backgroundColor: _theme.color.palette.lightestGrey,
    paddingVertical: 16,
    paddingStart: 18,
    paddingEnd: 16
  };
  var item = exports.item = {
    width: "100%"
  };
  var itemWithGutter = exports.itemWithGutter = {
    marginHorizontal: 6
  };
  var viewAllContainer = exports.viewAllContainer = {
    flexDirection: "row",
    alignItems: "center",
    marginStart: 18,
    paddingVertical: 12 // Increase tap zone
  };
  var viewAllText = exports.viewAllText = Object.assign({}, _text.presets.textLink, {
    fontWeight: "normal",
    marginEnd: 8
  });
  var addContainer = exports.addContainer = {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 16,
    marginHorizontal: 24
  };
  var addText = exports.addText = Object.assign({}, _text.presets.textLink, {
    fontWeight: "normal",
    marginStart: 4
  });
  var addLink = exports.addLink = Object.assign({}, _text.presets.textLink, {
    fontWeight: "normal"
  });
