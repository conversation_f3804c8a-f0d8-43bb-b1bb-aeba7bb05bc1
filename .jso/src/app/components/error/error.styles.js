  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.wrapperError = exports.waveShapeWhite = exports.waveShapeStyle = exports.textStyle = exports.textReloadPurple = exports.standardContainer = exports.reloadImageTextContainer = exports.flightCFTitleStyle = exports.flightCFTextStyle = exports.flightCFRowStyle = exports.flightCFContainerWithTitleStyle = exports.flightCFContainerStyle = exports.flightCFButtonTextStyle = exports.flightCFButtonStyle = exports.filterTextStyle = exports.filterContentStyle = exports.filterContainerStyle = exports.curveVerticalContainer = exports.curveContainer = exports.cloudTextStyle = exports.cloudIconStyle = exports.cloudHeaderStyle = exports.cloudContainerStyle = exports.cloudButtonStyle = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var filterContainerStyle = exports.filterContainerStyle = {
    height: 28,
    borderRadius: 24,
    borderWidth: 1,
    alignSelf: "flex-start",
    flexDirection: "row",
    width: 92,
    borderColor: "transparent",
    backgroundColor: _theme.color.palette.lighterGrey
  };
  var filterContentStyle = exports.filterContentStyle = {
    flexDirection: "row",
    alignItems: "center"
  };
  var filterTextStyle = exports.filterTextStyle = {
    color: _theme.color.palette.lightPurple,
    marginLeft: 12,
    marginRight: 7
  };
  var standardContainer = exports.standardContainer = {
    backgroundColor: _theme.color.palette.lighterGrey,
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 25,
    margin: 24
  };
  var curveContainer = exports.curveContainer = {
    backgroundColor: _theme.color.palette.lighterGrey,
    paddingTop: 56,
    justifyContent: "center",
    alignItems: "center",
    paddingBottom: 84
  };
  var curveVerticalContainer = exports.curveVerticalContainer = Object.assign({}, curveContainer, {
    backgroundColor: _theme.color.palette.lightestGrey,
    paddingTop: 20,
    paddingBottom: 100
  });
  var reloadImageTextContainer = exports.reloadImageTextContainer = {
    alignItems: "center"
  };
  var wrapperError = exports.wrapperError = {
    flex: 1
  };
  var textStyle = exports.textStyle = {
    color: _theme.color.palette.lightPurple,
    marginTop: 16
  };
  var textReloadPurple = exports.textReloadPurple = {
    color: _theme.color.palette.lightPurple,
    marginTop: 0
  };
  var waveShapeStyle = exports.waveShapeStyle = {
    position: "absolute",
    left: 0,
    bottom: 0,
    width: "100%",
    color: _theme.color.palette.lightestGrey
  };
  var waveShapeWhite = exports.waveShapeWhite = Object.assign({}, waveShapeStyle, {
    color: _theme.color.palette.whiteGrey
  });
  var cloudContainerStyle = exports.cloudContainerStyle = {
    backgroundColor: _theme.color.palette.lightestGrey,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 60,
    paddingBottom: 189
  };
  var cloudTextStyle = exports.cloudTextStyle = {
    marginTop: 34,
    paddingHorizontal: 62,
    textAlign: "center",
    color: _theme.color.palette.darkestGrey
  };
  var cloudHeaderStyle = exports.cloudHeaderStyle = {
    textAlign: "center",
    color: _theme.color.palette.almostBlackGrey,
    width: "100%",
    marginTop: 70
  };
  var cloudButtonStyle = exports.cloudButtonStyle = {
    borderRadius: 60,
    paddingHorizontal: 24,
    marginTop: 24
  };
  var cloudIconStyle = exports.cloudIconStyle = {
    width: 191,
    height: 104,
    resizeMode: 'contain'
  };
  var flightCFContainerStyle = exports.flightCFContainerStyle = {
    backgroundColor: _theme.color.palette.whiteGrey,
    marginHorizontal: 24,
    borderRadius: 16,
    minHeight: 90,
    padding: 16,
    justifyContent: 'center'
  };
  var flightCFContainerWithTitleStyle = exports.flightCFContainerWithTitleStyle = Object.assign({}, flightCFContainerStyle, {
    justifyContent: 'space-between'
  });
  var flightCFRowStyle = exports.flightCFRowStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 10
  };
  var flightCFTextStyle = exports.flightCFTextStyle = Object.assign({}, _text.newPresets.caption1Regular, {
    color: _theme.color.palette.almostBlackGrey
  });
  var flightCFButtonTextStyle = exports.flightCFButtonTextStyle = Object.assign({}, flightCFTextStyle, {
    color: _theme.color.palette.lightPurple
  });
  var flightCFTitleStyle = exports.flightCFTitleStyle = Object.assign({}, _text.newPresets.caption1Bold, {
    color: _theme.color.palette.almostBlackGrey
  });
  var flightCFButtonStyle = exports.flightCFButtonStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 5
  };
