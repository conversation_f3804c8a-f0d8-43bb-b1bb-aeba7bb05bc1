  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorComponent = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _errorProps = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _wave = _$$_REQUIRE(_dependencyMap[10]);
  var _button = _$$_REQUIRE(_dependencyMap[11]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _envParams = _$$_REQUIRE(_dependencyMap[15]);
  var _lodash = _$$_REQUIRE(_dependencyMap[16]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ErrorComponent = exports.ErrorComponent = function ErrorComponent(props) {
    var _dataCommonAEM$data, _env;
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig("AEM_COMMON_DATA"));
    var errorEHR25 = (dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.errors) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR2.5";
    })) || "";
    var _props$type = props.type,
      type = _props$type === undefined ? _errorProps.ErrorComponentType.standard : _props$type,
      style = props.style,
      onPressed = props.onPressed,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ErrorComponent" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ErrorComponent" : _props$accessibilityL,
      isReloadPurple = props.isReloadPurple,
      errorHeader = props.errorHeader,
      cloudTextStyle = props.cloudTextStyle,
      cloudButtonStyle = props.cloudButtonStyle,
      reloadIcon = props.reloadIcon,
      flightCFTitle = props.flightCFTitle,
      _props$flightCFContai = props.flightCFContainerStyle,
      flightCFContainerStyle = _props$flightCFContai === undefined ? {} : _props$flightCFContai;
    var renderReloadIcon = React.useMemo(function () {
      if (reloadIcon) {
        return reloadIcon;
      }
      if (isReloadPurple) {
        return (0, _jsxRuntime.jsx)(_icons.ReloadPurple, {});
      }
      return (0, _jsxRuntime.jsx)(_icons.Reload, {});
    }, []);
    switch (type) {
      case _errorProps.ErrorComponentType.curved:
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: Object.assign({}, styles.curveContainer, style),
            children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              style: styles.reloadImageTextContainer,
              onPress: onPressed,
              testID: `${testID}__TouchableCurveReload`,
              accessibilityLabel: `${accessibilityLabel}__TouchableCurveReload`,
              children: [(0, _jsxRuntime.jsx)(_icons.Reload, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                numberOfLines: 1,
                text: (0, _i18n.translate)("common.reload"),
                preset: "bodyTextBold",
                style: styles.textStyle
              })]
            })
          }), (0, _jsxRuntime.jsx)(_wave.Wave, {
            style: styles.waveShapeStyle,
            fill: "currentColor"
          })]
        });
      case _errorProps.ErrorComponentType.curvedVertical:
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.wrapperError,
          children: [(0, _jsxRuntime.jsx)(_wave.WaveGrey, {}), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: Object.assign({}, styles.curveVerticalContainer, style),
            children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              style: styles.reloadImageTextContainer,
              onPress: onPressed,
              testID: `${testID}__TouchableCurvedVerticalReload`,
              accessibilityLabel: `${accessibilityLabel}__TouchableCurvedVerticalReload`,
              children: [(0, _jsxRuntime.jsx)(_icons.ReloadPurple, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                numberOfLines: 1,
                text: (0, _i18n.translate)("common.reload"),
                preset: "bodyTextBold",
                style: styles.textStyle
              })]
            })
          }), (0, _jsxRuntime.jsx)(_wave.Wave, {
            style: styles.waveShapeWhite,
            fill: "currentColor"
          })]
        });
      case _errorProps.ErrorComponentType.filter:
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.filterContainerStyle,
          children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.filterContentStyle,
            onPress: onPressed,
            testID: `${testID}__TouchableFilterReload`,
            accessibilityLabel: `${accessibilityLabel}__TouchableFilterReload`,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 1,
              text: (0, _i18n.translate)("common.reload"),
              preset: "bodyTextBold",
              style: styles.filterTextStyle
            }), (0, _jsxRuntime.jsx)(_icons.UnionReload, {})]
          })
        });
      case _errorProps.ErrorComponentType.cloud:
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: Object.assign({}, styles.cloudContainerStyle, style),
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: ((_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL) + errorEHR25.icon
            },
            style: styles.cloudIconStyle
          }), !(0, _lodash.isEmpty)(errorHeader) && (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "header",
            tx: errorHeader,
            style: styles.cloudHeaderStyle
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            text: errorEHR25 == null ? undefined : errorEHR25.subHeader,
            style: [styles.cloudTextStyle, cloudTextStyle]
          }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: [styles.cloudButtonStyle, cloudButtonStyle],
            start: {
              x: 0,
              y: 1
            },
            end: {
              x: 1,
              y: 0
            },
            colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              sizePreset: "large",
              textPreset: "buttonLarge",
              typePreset: "secondary",
              statePreset: "default",
              backgroundPreset: "light",
              text: errorEHR25 == null ? undefined : errorEHR25.buttonLabel,
              onPress: onPressed,
              testID: `${testID}__ButtonCloudReload`,
              accessibilityLabel: `${accessibilityLabel}__ButtonCloudReload`
            })
          })]
        });
      case _errorProps.ErrorComponentType.flightCF:
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.flightCFContainerStyle, !!flightCFTitle && styles.flightCFContainerWithTitleStyle, flightCFContainerStyle],
          children: [!!flightCFTitle && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.flightCFRowStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              text: flightCFTitle,
              style: styles.flightCFTitleStyle
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.flightCFRowStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              tx: "flightDetailV2.error.unableToLoad",
              style: styles.flightCFTextStyle
            }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
              style: styles.flightCFButtonStyle,
              onPress: onPressed,
              testID: `${testID}__TouchableFlightCFReload`,
              accessibilityLabel: `${accessibilityLabel}__TouchableFlightCFReload`,
              children: [(0, _jsxRuntime.jsx)(_icons.UnionReload, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                numberOfLines: 1,
                text: (0, _i18n.translate)("common.retry"),
                preset: "bodyTextBold",
                style: styles.flightCFButtonTextStyle
              })]
            })]
          })]
        });
      default:
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: Object.assign({}, styles.standardContainer, style),
          children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: styles.reloadImageTextContainer,
            onPress: onPressed,
            testID: `${testID}__ButtonDefaultReload`,
            accessibilityLabel: `${accessibilityLabel}__ButtonDefaultReload`,
            children: [renderReloadIcon, (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 1,
              text: (0, _i18n.translate)("common.reload"),
              preset: "bodyTextBold",
              style: isReloadPurple ? styles.textReloadPurple : styles.textStyle
            })]
          })
        });
    }
  };
