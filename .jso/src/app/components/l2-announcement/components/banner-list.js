  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _useL2Announcement = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _l2Announcement = _$$_REQUIRE(_dependencyMap[7]);
  var _bannerItem = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[9]);
  var _blur = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _react = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AnimatedView = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.View);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var _worklet_10512620221344_init_data = {
    code: "function bannerListTsx1(){const{animatedTranslateXValue,animatedOpacityValue}=this.__closure;return{transform:[{translateX:animatedTranslateXValue.value}],marginTop:12,opacity:animatedOpacityValue.value};}"
  };
  var BannerListItem = function BannerListItem(props) {
    var dataLength = props.dataLength,
      index = props.index,
      item = props.item,
      onClose = props.onClose,
      onDismiss = props.onDismiss,
      onPressBanner = props.onPressBanner;
    var handleDismissBanner = function handleDismissBanner(bannerItem, dismissData) {
      onDismiss == null || onDismiss(bannerItem, dismissData);
      if (dataLength <= 1) {
        onClose == null || onClose();
      }
    };
    var handleAnimationBeforeDismiss = function handleAnimationBeforeDismiss(bannerItem, dismissData) {
      handleDismissBanner == null || handleDismissBanner(bannerItem, dismissData);
      animatedTranslateXValue.value = (0, _reactNativeReanimated.withTiming)(width - 32, {
        duration: 100
      });
      animatedOpacityValue.value = (0, _reactNativeReanimated.withTiming)(0, {
        duration: 100
      });
    };
    var animatedTranslateXValue = (0, _reactNativeReanimated.useSharedValue)(0);
    var animatedOpacityValue = (0, _reactNativeReanimated.useSharedValue)(1);
    var containerAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var bannerListTsx1 = function bannerListTsx1() {
        return {
          transform: [{
            translateX: animatedTranslateXValue.value
          }],
          marginTop: 12,
          opacity: animatedOpacityValue.value
        };
      };
      bannerListTsx1.__closure = {
        animatedTranslateXValue: animatedTranslateXValue,
        animatedOpacityValue: animatedOpacityValue
      };
      bannerListTsx1.__workletHash = 10512620221344;
      bannerListTsx1.__initData = _worklet_10512620221344_init_data;
      return bannerListTsx1;
    }());
    var handleOnPressBanner = function handleOnPressBanner(item) {
      var _item$extraJsonData, _item$extraJsonData2;
      onPressBanner(item, true);
      var dataToBeSent = `${_adobe.AdobeValueByTagName.HomeExploreNotificationBannerInterstitial}${_adobe.AdobeValueByTagName.HomeExploreNotificationBannerMultiple}${_adobe.AdobeValueByTagName.HomeExploreNotificationBannerViewMessage}${item == null || (_item$extraJsonData = item.extraJsonData) == null ? undefined : _item$extraJsonData.customLabel} | ${item == null || (_item$extraJsonData2 = item.extraJsonData) == null ? undefined : _item$extraJsonData2.summary}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.HomeExploreNotificationBanner, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.HomeExploreNotificationBanner, dataToBeSent));
    };
    return (0, _jsxRuntime.jsx)(AnimatedView, {
      style: [_l2Announcement.bannerStyles.listBannerContainerStyle, containerAnimatedStyle],
      children: (0, _jsxRuntime.jsx)(_bannerItem.default, {
        dataLength: dataLength,
        index: index,
        item: item,
        onDismissBanner: handleAnimationBeforeDismiss,
        onPressBanner: function onPressBanner(item) {
          return handleOnPressBanner(item);
        },
        viewOnList: true
      })
    });
  };
  var BannerList = function BannerList(props) {
    var data = props.data,
      onPressBanner = props.onPressBanner,
      onClose = props.onClose,
      visible = props.visible,
      navigation = props.navigation;
    var _useL2AnnouncementBan = (0, _useL2Announcement.useL2AnnouncementBanner)(),
      onDismiss = _useL2AnnouncementBan.onDismiss,
      onDismissAll = _useL2AnnouncementBan.onDismissAll;
    var backdropCustomStyle = {
      display: visible ? "flex" : "none"
    };
    var listRef = (0, _react.useRef)(null);
    var handlePressCloseAll = function handlePressCloseAll() {
      onDismissAll == null || onDismissAll(data);
      onClose == null || onClose();
      var dataToBeSent = `${_adobe.AdobeValueByTagName.HomeExploreNotificationBannerInterstitial}${_adobe.AdobeValueByTagName.HomeExploreNotificationBannerMultiple}${_adobe.AdobeValueByTagName.HomeExploreNotificationBannerDeleteAll}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.HomeExploreNotificationBanner, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.HomeExploreNotificationBanner, dataToBeSent));
    };
    (0, _react.useEffect)(function () {
      var subscription;
      if (visible) {
        var _listRef$current;
        var onBackPress = function onBackPress() {
          onClose();
          return true;
        };
        navigation == null || navigation.setOptions == null || navigation.setOptions({
          gestureEnabled: false
        });
        subscription = _reactNative.BackHandler.addEventListener("hardwareBackPress", onBackPress);
        listRef == null || (_listRef$current = listRef.current) == null || (_listRef$current = _listRef$current._listRef) == null || (_listRef$current = _listRef$current._scrollRef) == null || _listRef$current.scrollToEnd == null || _listRef$current.scrollToEnd();
      } else {
        var _subscription;
        (_subscription = subscription) == null || _subscription.remove == null || _subscription.remove();
      }
    }, [visible]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [_l2Announcement.listStyles.backdropStyle, backdropCustomStyle],
      children: [visible && (0, _jsxRuntime.jsx)(_blur.BlurView, {
        style: _l2Announcement.listStyles.blurContainerStyle,
        blurType: "dark",
        blurAmount: 10,
        reducedTransparencyFallbackColor: _theme.color.palette.shadow
      }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.FlatList, {
        ref: listRef,
        alwaysBounceVertical: false,
        showsVerticalScrollIndicator: false,
        style: _l2Announcement.listStyles.listContainerStyle,
        data: data,
        keyExtractor: function keyExtractor(item, index) {
          return `${item == null ? undefined : item.id}-${index}`;
        },
        renderItem: function renderItem(_ref) {
          var item = _ref.item,
            index = _ref.index;
          return (0, _jsxRuntime.jsx)(BannerListItem, {
            dataLength: data == null ? undefined : data.length,
            index: index,
            item: item,
            onClose: onClose,
            onDismiss: onDismiss,
            onPressBanner: onPressBanner
          }, `${item == null ? undefined : item.id}-${index}`);
        },
        itemLayoutAnimation: _reactNativeReanimated.LinearTransition.duration(300),
        inverted: true,
        initialNumToRender: 100
      }), (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        onPress: handlePressCloseAll,
        androidRippleColor: "transparent",
        style: _l2Announcement.listStyles.closeAllBtnStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _l2Announcement.listStyles.closeAllBtnLabelStyle,
          tx: "l2Announcement.details.closeAllBtn"
        }), (0, _jsxRuntime.jsx)(_icons.Cross, {
          color: _theme.color.palette.whiteGrey,
          height: 24,
          width: 24
        })]
      })]
    });
  };
  var _default = exports.default = BannerList;
