  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _l2Announcement = _$$_REQUIRE(_dependencyMap[1]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _utils = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var BannerItem = function BannerItem(props) {
    var dataLength = props.dataLength,
      index = props.index,
      item = props.item,
      onDismissBanner = props.onDismissBanner,
      onPressBanner = props.onPressBanner,
      viewOnList = props.viewOnList;
    var _item$extraJsonData = item == null ? undefined : item.extraJsonData,
      icon = _item$extraJsonData.icon,
      summary = _item$extraJsonData.summary;
    var contentStyle = {
      opacity: viewOnList || index === dataLength - 1 ? 1 : 0
    };
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      onPress: function onPress() {
        return onPressBanner(item);
      },
      style: [_l2Announcement.bannerStyles.contentContainerStyle, contentStyle],
      android_ripple: {
        color: _theme.color.palette.almostBlackGrey,
        foreground: true
      },
      children: [icon && (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: (0, _utils.mappingUrlAem)(icon)
        },
        style: _l2Announcement.bannerStyles.iconContainerStyle
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        numberOfLines: 3,
        style: _l2Announcement.bannerStyles.messageTextStyle,
        text: summary
      }), (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        onPress: function onPress() {
          return onDismissBanner(item, {
            viewOnList: viewOnList,
            dataLength: dataLength,
            fromCloseButton: true
          });
        },
        style: _l2Announcement.bannerStyles.dismissBtnStyle,
        android_ripple: {
          color: _theme.color.palette.almostBlackGrey,
          foreground: true
        },
        children: (0, _jsxRuntime.jsx)(_icons.Cross, {
          color: _theme.color.palette.whiteGrey,
          height: 24,
          width: 24
        })
      })]
    });
  };
  var _default = exports.default = BannerItem;
