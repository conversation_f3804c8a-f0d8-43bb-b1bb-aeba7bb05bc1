  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.StaffPerkItemType = exports.StaffPerkItemPageName = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _utils = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[8]);
  var _exploreStaffPerkItem = _$$_REQUIRE(_dependencyMap[9]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _staffPerkPromotionDetailController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _categoryHeader = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _icons = _$$_REQUIRE(_dependencyMap[14]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[15]);
  var _theme = _$$_REQUIRE(_dependencyMap[16]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[17]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _firebase = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  var StaffPerkItemPageName = exports.StaffPerkItemPageName = Object.assign({
    EXPLORE: "EXPLORE"
  }, _categoryHeader.CategoryTypeTabHeader);
  var StaffPerkItemType = exports.StaffPerkItemType = /*#__PURE__*/function (StaffPerkItemType) {
    StaffPerkItemType["seasonal"] = "SEASONAL";
    return StaffPerkItemType;
  }({});
  var handleStyle = function handleStyle(isShowClock, isShowLimitedOffer, showNewlyAdded) {
    if (isShowClock) {
      return _exploreStaffPerkItem.styles.nameWithClock;
    } else if (isShowLimitedOffer) {
      return _exploreStaffPerkItem.styles.nameWithIndicator;
    } else if (showNewlyAdded) {
      return _exploreStaffPerkItem.styles.nameWithNewlyAdded;
    } else {
      return _exploreStaffPerkItem.styles.name;
    }
  };
  var COMPONENT_NAME = "ExploreStaffPerkItem";
  var ExploreStaffPerkItem = function ExploreStaffPerkItem(_ref) {
    var _item$categories;
    var navigation = _ref.navigation,
      item = _ref.item,
      _ref$isListing = _ref.isListing,
      isListing = _ref$isListing === undefined ? false : _ref$isListing,
      _ref$isLoading = _ref.isLoading,
      isLoading = _ref$isLoading === undefined ? false : _ref$isLoading,
      _ref$pageName = _ref.pageName,
      pageName = _ref$pageName === undefined ? "" : _ref$pageName;
    var showClock = !isListing && (item == null ? undefined : item.type) === StaffPerkItemType.seasonal;
    var showLimitedOffer = isListing && (item == null ? undefined : item.type) === StaffPerkItemType.seasonal;
    var showNewlyAdded = !showClock && !isListing && (item == null || (_item$categories = item.categories) == null || _item$categories.some == null ? undefined : _item$categories.some(function (val) {
      return val === _categoryHeader.CategoryTypeTabHeader.NEW_STAFF_PERKS;
    }));
    var dateValue = item == null ? undefined : item.date;
    if (showNewlyAdded) {
      dateValue = item != null && item.campaignStartDate ? `From ${(0, _moment.default)(item == null ? undefined : item.campaignStartDate).format(_dateTime.DateFormats.DayMonthYear)}` : "";
    }
    var openDetail = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var UID = yield (0, _screenHelper.getViewerUID)({
          shouldReturnNull: true
        });
        if (pageName === StaffPerkItemPageName.EXPLORE) {
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyEntry, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyEntry, `Explore | ${item == null ? undefined : item.title} | Detail | ${UID}`));
        }
        var galaxyLandingTrackValue = item != null && item.title ? `${item == null ? undefined : item.title} | ${UID}` : "";
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyLanding, galaxyLandingTrackValue));
        (0, _firebase.analyticsLogEvent)(_firebase.ANALYTICS_LOG_EVENT_NAME.STAFF_PERKS_LISTING_TITLE_CLICKING, {
          ui_version: "old_ui",
          value: galaxyLandingTrackValue
        });
        (0, _firebase.analyticsLogEvent)(_firebase.ANALYTICS_LOG_EVENT_NAME.STAFF_PERKS_LISTING_TITLE_OLD, {
          value: galaxyLandingTrackValue
        });
        _staffPerkPromotionDetailController.default.showModal(navigation, {
          item: item
        });
      });
      return function openDetail() {
        return _ref2.apply(this, arguments);
      };
    }();
    if (isLoading) {
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: (0, _utils.handleCondition)(isListing, _exploreStaffPerkItem.styles.listingContainer, _exploreStaffPerkItem.styles.container),
        activeOpacity: 0.5,
        disabled: true,
        testID: `${COMPONENT_NAME}`,
        accessibilityLabel: `${COMPONENT_NAME}`,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
          shimmerStyle: (0, _utils.handleCondition)(isListing, _exploreStaffPerkItem.styles.imageListingStyle, _exploreStaffPerkItem.styles.imageStyle)
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: (0, _utils.handleCondition)(isListing, _exploreStaffPerkItem.styles.wrapContentListingItem, _exploreStaffPerkItem.styles.wrapContentItem),
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
              shimmerStyle: _exploreStaffPerkItem.styles.name
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
              shimmerStyle: _exploreStaffPerkItem.styles.title
            })]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
            shimmerStyle: _exploreStaffPerkItem.styles.date
          })]
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: (0, _utils.handleCondition)(isListing, _exploreStaffPerkItem.styles.listingContainer, _exploreStaffPerkItem.styles.container),
      activeOpacity: 0.5,
      onPress: openDetail,
      testID: `${COMPONENT_NAME}`,
      accessibilityLabel: `${COMPONENT_NAME}`,
      accessible: false,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.Image, {
        source: {
          uri: item == null ? undefined : item.imageUrl
        },
        style: (0, _utils.handleCondition)(isListing, _exploreStaffPerkItem.styles.imageListingStyle, _exploreStaffPerkItem.styles.imageStyle),
        resizeMode: "cover",
        testID: `${COMPONENT_NAME}_Image`,
        accessibilityLabel: `${item == null ? undefined : item.imageUrl}`
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: (0, _utils.handleCondition)(isListing, _exploreStaffPerkItem.styles.wrapContentListingItem, _exploreStaffPerkItem.styles.wrapContentItem),
        children: [showClock && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          accessibilityLabel: `${COMPONENT_NAME}_Label_LimitedOffer`,
          style: _exploreStaffPerkItem.styles.perkLabelContainerStyle,
          testID: `${COMPONENT_NAME}_Label_LimitedOffer`,
          children: [(0, _jsxRuntime.jsx)(_icons.StaffPerkClockIcon, {
            color: _theme.color.palette.lightOrange,
            height: 10,
            width: 10
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _exploreStaffPerkItem.styles.perkLabelTextStyle,
            tx: "exploreScreen.staffPerk.item.limitedOffer"
          })]
        }), showLimitedOffer && (0, _jsxRuntime.jsx)(_icons.StaffPerkLimitedOffer, {
          style: _exploreStaffPerkItem.styles.clockIconStyles
        }), showNewlyAdded && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          accessibilityLabel: `${COMPONENT_NAME}_Label_NewlyAdded`,
          style: _exploreStaffPerkItem.styles.perkLabelContainerStyle,
          testID: `${COMPONENT_NAME}_Label_NewlyAdded`,
          children: [(0, _jsxRuntime.jsx)(_icons.StarPerk, {
            color: _theme.color.palette.lightOrange,
            height: 10,
            width: 10
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _exploreStaffPerkItem.styles.perkLabelTextStyle,
            tx: "staffPerkListing.item.newlyAdded"
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: item == null ? undefined : item.tenantName,
            numberOfLines: 1,
            style: handleStyle(showClock, showLimitedOffer, showNewlyAdded),
            testID: `${COMPONENT_NAME}_TenantName`,
            accessibilityLabel: `${item == null ? undefined : item.tenantName}`
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: item == null ? undefined : item.title,
            numberOfLines: (0, _utils.handleCondition)(isListing, 2, 3),
            style: _exploreStaffPerkItem.styles.title,
            testID: `${COMPONENT_NAME}_Title`,
            accessibilityLabel: `${item == null ? undefined : item.title}`
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: dateValue,
          numberOfLines: 1,
          style: _exploreStaffPerkItem.styles.date,
          testID: `${COMPONENT_NAME}_Date`,
          accessibilityLabel: `${dateValue}`
        })]
      })]
    });
  };
  var _default = exports.default = _react.default.memo(ExploreStaffPerkItem);
