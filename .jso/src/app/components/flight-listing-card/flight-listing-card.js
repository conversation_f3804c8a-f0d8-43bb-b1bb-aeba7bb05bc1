  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _flightListingCard = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _plus = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _plusDisabled = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _info_filled = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[14]);
  var _i18n = _$$_REQUIRE(_dependencyMap[15]);
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _storageKey = _$$_REQUIRE(_dependencyMap[17]);
  var _storage = _$$_REQUIRE(_dependencyMap[18]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[20]);
  var _reactNativeWalkthroughTooltip = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _native = _$$_REQUIRE(_dependencyMap[22]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _utils = _$$_REQUIRE(_dependencyMap[24]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[25]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _flyRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _icons = _$$_REQUIRE(_dependencyMap[28]);
  var _AnimatedText = _$$_REQUIRE(_dependencyMap[29]);
  var _viaArrow = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _store = _$$_REQUIRE(_dependencyMap[31]);
  var _persist = _$$_REQUIRE(_dependencyMap[32]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[33]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var dotUnicode = (0, _constants.getDotUnicode)();
  var buttonStyleSaved = {
    width: 28,
    height: 28,
    borderRadius: 20,
    justifyContent: "center",
    backgroundColor: _theme.color.palette.lightPurple,
    alignItems: "center"
  };
  var styles = _reactNative2.StyleSheet.create({
    bottomTextContainer: {
      alignItems: "center",
      flexDirection: "row",
      paddingBottom: 24,
      paddingLeft: 14,
      paddingTop: 18
    },
    bottomTextStyle: {
      flex: 1
    },
    buttonDisabledStyleAdd: Object.assign({}, buttonStyleSaved, {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.midGrey,
      borderWidth: 2
    }),
    buttonStyleAdd: Object.assign({}, buttonStyleSaved, {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lightPurple,
      borderWidth: 2
    }),
    codeShareTextStyles: {
      color: _theme.color.palette.darkestGrey,
      fontFamily: _theme.typography.regular,
      fontSize: 14,
      fontStyle: "normal",
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 18,
      textAlign: "left",
      textAlignVertical: "top"
    },
    codeShareView: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      marginLeft: 4
    },
    container: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      elevation: 5,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 6,
        width: 0
      },
      shadowOpacity: 0.05,
      shadowRadius: 20,
      width: width - (0, _reactNativeSizeMatters.scale)(40)
    },
    departingDestinationCode: {
      color: _theme.color.palette.almostBlackGrey
    },
    dotUnicodeStyle: {
      alignSelf: "center",
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 4
    },
    emptyContent: {
      paddingTop: 0
    },
    fastImageStyle: {
      height: 25,
      width: 25
    },
    flightNumberStyle: {
      alignSelf: "center",
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 8
    },
    infoIcon: {
      marginRight: 4
    },
    innerContainer: {
      flexDirection: "row",
      paddingLeft: 16,
      paddingRight: 16,
      paddingTop: 16
    },
    newBottomContainer: {
      flex: 1,
      flexDirection: "row",
      marginLeft: 16,
      marginRight: 16,
      marginTop: 20,
      marginBottom: 16,
      alignItems: "center"
    },
    newBottomGateAndCheckInContainer: {
      marginRight: 12
    },
    newBottomRightContainer: {
      flexDirection: "row",
      flex: 1
    },
    newBottomSaveFlightContainer: {
      alignItems: "flex-end",
      flex: 1
    },
    newBottomTerminalContainer: {
      marginRight: 16
    },
    newFlightIcon: {
      marginRight: 8
    },
    newFlightInfoContainer: {
      flex: 1
    },
    newFlightNumberInfoView: {
      flexDirection: "row",
      marginTop: 8
    },
    newFlightPlaceInfoView: {
      flexDirection: "row"
    },
    newFlightPlaceText: {
      color: _theme.color.palette.almostBlackGrey
    },
    newFlightPlaceView: {
      flex: 1
    },
    newFlightStatus: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left"
    },
    newFlightTimeView: {
      marginRight: 12,
      width: 74
    },
    numberDaysReTimedText: {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 2,
      textAlign: "left"
    },
    oldDateLineThrough: {
      backgroundColor: _theme.color.palette.baseRed,
      height: 2,
      position: "absolute",
      top: 8,
      width: 52
    },
    oldDateView: {
      marginBottom: 2,
      position: "relative"
    },
    terminalTextColor: {
      color: _theme.color.palette.black
    },
    viaAirportContainer: {
      marginBottom: 8,
      marginTop: 16
    },
    viaAirportContentWrap: {},
    viaAirportTextContent: {
      color: _theme.color.palette.darkestGrey,
      fontFamily: _theme.typography.regular,
      fontSize: 14,
      fontWeight: "400",
      lineHeight: 18
    },
    viaAirportTitle: {
      color: _theme.color.palette.darkestGrey,
      fontFamily: _theme.typography.bold,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18,
      paddingLeft: 4
    },
    viaAirportTitleWrap: {
      alignItems: "center",
      flexDirection: "row"
    },
    wrapButtonToolTip: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 30,
      height: 60,
      justifyContent: "center",
      width: 60
    },
    wrapLogoFlight: {
      alignItems: "center",
      borderRadius: 12,
      height: 24,
      justifyContent: "center",
      overflow: "hidden",
      width: 24
    }
  });
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var loadingStyles = _reactNative2.StyleSheet.create({
    bottomLoadingSection: {
      flexDirection: "row",
      flex: 1,
      paddingTop: 20,
      paddingBottom: 16
    },
    bottomLoadingSectionCol: {
      paddingRight: 16
    },
    codeShareViewLoading: {
      flexDirection: "row"
    },
    ellipseLoading1: {
      backgroundColor: _theme.color.palette.lighterGrey,
      borderRadius: 50,
      height: 24,
      width: 24
    },
    ellipseLoading2: {
      backgroundColor: _theme.color.palette.lighterGrey,
      borderRadius: 50,
      height: 20,
      width: 20
    },
    newFlightNumberInfoViewLoading: {
      flexDirection: "row"
    },
    newFlightPlaceInfoView: {
      flexDirection: "row",
      flex: 1,
      marginBottom: 8
    },
    newFlightPlaceView: {
      flex: 1
    },
    rectangleLoading1: {
      backgroundColor: _theme.color.palette.lighterGrey,
      borderRadius: 4,
      height: 24,
      width: 52
    },
    rectangleLoading2: {
      backgroundColor: _theme.color.palette.lighterGrey,
      borderRadius: 4,
      height: 24,
      width: 56
    },
    rectangleLoading3: {
      backgroundColor: _theme.color.palette.lighterGrey,
      borderRadius: 4,
      height: 14,
      marginBottom: 2,
      width: 64
    },
    rectangleLoading4: {
      backgroundColor: _theme.color.palette.lighterGrey,
      borderRadius: 4,
      height: 14,
      width: 40
    },
    rectangleLoadingMaxWidth: {
      backgroundColor: _theme.color.palette.lighterGrey,
      borderRadius: 4,
      height: 24,
      width: "100%"
    },
    wrapLogoFlightLoading: {
      justifyContent: "center",
      marginLeft: 2,
      marginRight: 8
    }
  });
  function loadingView() {
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.innerContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.newFlightTimeView,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: loadingStyles.rectangleLoading1
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.newFlightInfoContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: loadingStyles.newFlightPlaceInfoView,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.newFlightIcon,
              children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyles.ellipseLoading1
              })
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: loadingStyles.newFlightPlaceView,
              children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyles.rectangleLoadingMaxWidth
              })
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: loadingStyles.newFlightNumberInfoViewLoading,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: loadingStyles.wrapLogoFlightLoading,
              children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyles.ellipseLoading2
              })
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: loadingStyles.codeShareViewLoading,
              children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyles.rectangleLoading2
              })
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: loadingStyles.bottomLoadingSection,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: loadingStyles.bottomLoadingSectionCol,
              children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyles.rectangleLoading3
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyles.rectangleLoading4
              })]
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: loadingStyles.bottomLoadingSectionCol,
              children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyles.rectangleLoading3
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyles.rectangleLoading4
              })]
            })]
          })]
        })]
      })
    });
  }
  var handleTopAdjustment = function handleTopAdjustment(inset) {
    if (_reactNative2.Platform.OS === "android") {
      if (inset != null && inset.top && !isNaN(inset == null ? undefined : inset.top)) {
        return -(inset == null ? undefined : inset.top);
      }
      return 0;
    }
    return 0;
  };
  var renderFlightCode = function renderFlightCode(flightNumber, codeShares) {
    if ((codeShares == null ? undefined : codeShares.length) > 0) {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Bold",
          style: styles.flightNumberStyle,
          text: flightNumber
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Bold",
          style: styles.dotUnicodeStyle,
          text: dotUnicode
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.codeShareView,
          children: (0, _utils.handleCondition)(codeShares.length > 1, (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_AnimatedText.AnimatedText, {
              delay: 2000,
              codeShares: codeShares
            }, codeShares.length.toString()), (0, _jsxRuntime.jsx)(_text.Text, {
              text: ` (+${codeShares.length - 1})`,
              style: styles.codeShareTextStyles
            })]
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: codeShares[0],
            style: styles.codeShareTextStyles
          }))
        })]
      });
    }
    return (0, _jsxRuntime.jsx)(_text.Text, {
      preset: "caption1Bold",
      style: styles.flightNumberStyle,
      text: `${flightNumber}`
    });
  };
  var renderTransitCode = function renderTransitCode(transitCodes) {
    if ((transitCodes == null ? undefined : transitCodes.length) > 0) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.bottomTextContainer,
        children: [(0, _jsxRuntime.jsx)(_info_filled.default, {
          style: styles.infoIcon
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Italic",
          style: styles.bottomTextStyle,
          text: (0, _i18n.translate)("flightListing.infoText")
        })]
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.emptyContent
    });
  };
  var checkFlightCanSave = function checkFlightCanSave(props) {
    var flightStatusCheckSave = props == null ? undefined : props.flightStatus;
    var status = flightStatusCheckSave == null ? undefined : flightStatusCheckSave.toLowerCase();
    var priorityTime = (props == null ? undefined : props.actualTimestamp) || (props == null ? undefined : props.estimatedTimestamp) || `${props == null ? undefined : props.scheduledDate} ${props == null ? undefined : props.scheduledTime}`;
    var currentTimeToUTC = (0, _momentTimezone.default)().tz("Asia/Singapore");
    var flightTime = (0, _momentTimezone.default)(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore");
    if (_flightProps.FlightDirection.departure === (props == null ? undefined : props.direction)) {
      switch (true) {
        case /departed/gim.test(status):
        case /cancelled/gim.test(status):
          return false;
        default:
          return true;
      }
    }
    switch (true) {
      case /cancelled/gim.test(status):
      case /landed/gim.test(status) && (0, _momentTimezone.default)(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") < currentTimeToUTC.format("YYYY-MM-DD HH:mm"):
        return false;
      default:
        return true;
    }
  };
  var renderCheckInRow = function renderCheckInRow(checkInRow) {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.newBottomGateAndCheckInContainer,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption2Regular",
        tx: "flightDetails.checkInRow",
        style: styles.terminalTextColor
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption2Bold",
        text: checkInRow || "-",
        style: styles.terminalTextColor
      })]
    });
  };
  var renderBaggageGate = function renderBaggageGate(displayBelt) {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.newBottomGateAndCheckInContainer,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption2Regular",
        tx: "flightDetails.baggageGate",
        style: styles.terminalTextColor
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption2Bold",
        text: displayBelt || "-",
        style: styles.terminalTextColor
      })]
    });
  };
  var viaText = function viaText(viaAirportDetails) {
    if (!viaAirportDetails) return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.viaAirportContainer,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viaAirportTitleWrap,
        children: [(0, _jsxRuntime.jsx)(_viaArrow.default, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          text: "Via",
          style: styles.viaAirportTitle
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.viaAirportContentWrap,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: viaAirportDetails == null ? undefined : viaAirportDetails.name,
          style: styles.viaAirportTextContent
        })
      })]
    });
  };
  function defaultView(props) {
    var _store$getState$flyRe;
    var logo = props.logo,
      flightNumber = props.flightNumber,
      codeShare = props.codeShare,
      flightStatusMapping = props.flightStatusMapping,
      transitCodes = props.transits,
      timeOfFlight = props.timeOfFlight,
      departingPlace = props.departingPlace,
      destinationPlace = props.destinationPlace,
      isSaved = props.isSaved,
      onPressed = props.onPressed,
      onSaved = props.onSaved,
      scheduledDate = props.scheduledDate,
      isLoggedIn = props.isLoggedIn,
      isMSError = props.isMSError,
      isSaveFlightLoading = props.isSaveFlightLoading,
      itemIndex = props.itemIndex,
      statusColor = props.statusColor,
      direction = props.direction,
      terminal = props.terminal,
      checkInRow = props.checkInRow,
      displayBelt = props.displayBelt,
      displayTimestamp = props.displayTimestamp,
      beltStatusMapping = props.beltStatusMapping,
      viaAirportDetails = props.viaAirportDetails,
      isFirstFlight = props.isFirstFlight;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      needShowToolTip = _useState2[0],
      setShowToolTip = _useState2[1];
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var route = (0, _native.useRoute)();
    var isFocused = (0, _native.useIsFocused)();
    var activeToolTipAemData = (0, _persist.usePersistStore)(function (state) {
      return state.activeToolTipAemData;
    });
    var touchableCardRef = (0, _react.useRef)(null);
    var touchableSaveRef = (0, _react.useRef)(null);
    var isCanSavedFlight = checkFlightCanSave(props);
    var dispatch = (0, _reactRedux.useDispatch)();
    var activeToolTip = (0, _react.useMemo)(function () {
      var _activeToolTipAemData;
      return activeToolTipAemData == null || (_activeToolTipAemData = activeToolTipAemData.list) == null ? undefined : _activeToolTipAemData.find(function (e) {
        return (e == null ? undefined : e.iconType) === "plus";
      });
    }, [activeToolTipAemData]);
    (0, _react.useEffect)(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        if ((0, _utils.ifAllTrue)([itemIndex === 0, (route == null ? undefined : route.name) === "flights", direction === _flightProps.FlightDirection.arrival])) {
          var checkToolTip = /*#__PURE__*/function () {
            var _ref = (0, _asyncToGenerator2.default)(function* () {
              var openFlyScreenBefore = yield (0, _storage.load)(_storageKey.StorageKey.needDisableToolTipFlyScreen);
              if ((0, _utils.ifAllTrue)([!openFlyScreenBefore, !(0, _isEmpty.default)(activeToolTipAemData == null ? undefined : activeToolTipAemData.list)])) {
                setShowToolTip(true);
                (0, _storage.save)(_storageKey.StorageKey.needDisableToolTipFlyScreen, true);
                var timeOutCloseToolTip = setTimeout(function () {
                  // set condition to scroll to top
                  dispatch(_flyRedux.FlyCreators.setScrollToTopAfterShowTooltip(true));
                  setShowToolTip(false);
                }, 5000);
                return function () {
                  clearTimeout(timeOutCloseToolTip);
                };
              }
            });
            return function checkToolTip() {
              return _ref.apply(this, arguments);
            };
          }();
          checkToolTip();
        }
      });
    }, []);
    var showFlightTime = function showFlightTime() {
      var mainTime = timeOfFlight;
      var reTimeFlag = false;
      var numberDaysDiff = 0;
      if (displayTimestamp) {
        mainTime = displayTimestamp == null ? undefined : displayTimestamp.split(" ")[1];
        var mainDate = displayTimestamp == null ? undefined : displayTimestamp.split(" ")[0];
        if ((0, _utils.ifOneTrue)([scheduledDate !== mainDate, timeOfFlight !== mainTime])) {
          reTimeFlag = true;
          numberDaysDiff = (0, _momentTimezone.default)(mainDate).diff((0, _momentTimezone.default)(scheduledDate), "days");
        }
      }
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _utils.simpleCondition)({
          condition: reTimeFlag,
          ifValue: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.oldDateView,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "h3",
              style: styles.departingDestinationCode,
              text: timeOfFlight
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.oldDateLineThrough
            })]
          }),
          elseValue: (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {})
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h3",
          style: styles.departingDestinationCode,
          text: mainTime
        }), (0, _utils.simpleCondition)({
          condition: (0, _utils.ifAllTrue)([reTimeFlag, !!numberDaysDiff]),
          ifValue: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            style: styles.numberDaysReTimedText,
            text: (0, _utils.simpleCondition)({
              condition: numberDaysDiff > 0,
              ifValue: `(+${numberDaysDiff})`,
              elseValue: `(${numberDaysDiff})`
            })
          }),
          elseValue: (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {})
        })]
      });
    };
    var shouldShowToolTip = (0, _utils.ifAllTrue)([needShowToolTip, isFirstFlight, (_store$getState$flyRe = _store.store.getState().flyReducer) == null ? undefined : _store$getState$flyRe.isShowTooltipAfterScroll, isFocused]);
    var getBottomSaveFlightContainer = (0, _react.useMemo)(function () {
      if (shouldShowToolTip) {
        return {
          position: "absolute",
          right: -16,
          top: -16
        };
      } else {
        return styles.newBottomSaveFlightContainer;
      }
    }, [isFirstFlight, needShowToolTip]);
    var renderNewBottomGateAndCheckInContainer = (0, _react.useCallback)(function () {
      if (direction === _flightProps.FlightDirection.departure) {
        return renderCheckInRow(checkInRow);
      } else {
        return renderBaggageGate(displayBelt);
      }
    }, [direction, checkInRow, displayBelt]);
    var onSavePress = (0, _react.useCallback)(function () {
      if (isSaveFlightLoading) {
        return;
      }
      onSaved(isSaved);
    }, [onSaved, isSaved]);
    var buttonSavedFlight = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onSavePress,
        style: (0, _utils.handleCondition)(isLoggedIn && isSaved, buttonStyleSaved, (0, _utils.handleCondition)((0, _utils.ifAllTrue)([isCanSavedFlight]), styles.buttonStyleAdd, styles.buttonDisabledStyleAdd)),
        disabled: (0, _utils.ifOneTrue)([isSaveFlightLoading]),
        ref: touchableSaveRef,
        children: (0, _utils.handleCondition)(isLoggedIn && isSaved, (0, _jsxRuntime.jsx)(_icons.CheckSavedFlight, {}), (0, _utils.handleCondition)((0, _utils.ifAllTrue)([isCanSavedFlight]), (0, _jsxRuntime.jsx)(_plus.default, {}), (0, _jsxRuntime.jsx)(_plusDisabled.default, {})))
      });
    }, [isSaveFlightLoading, isLoggedIn, isSaved, isCanSavedFlight]);
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: onPressed,
      disabled: isSaveFlightLoading,
      ref: touchableCardRef,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.innerContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.newFlightTimeView,
          children: showFlightTime()
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.newFlightInfoContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.newFlightPlaceInfoView,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.newFlightIcon,
              children: direction === _flightProps.FlightDirection.departure ? (0, _jsxRuntime.jsx)(_icons.FlightDepartureIconActive, {}) : (0, _jsxRuntime.jsx)(_icons.FlightArrivalIconActive, {})
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.newFlightPlaceView,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "h3",
                style: styles.newFlightPlaceText,
                numberOfLines: 2,
                text: (0, _utils.simpleCondition)({
                  condition: direction === _flightProps.FlightDirection.departure,
                  ifValue: destinationPlace,
                  elseValue: departingPlace
                })
              })
            })]
          }), viaText(viaAirportDetails), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.newFlightNumberInfoView,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.wrapLogoFlight,
              children: (0, _jsxRuntime.jsx)(_baseImage.default, {
                source: {
                  uri: logo
                },
                style: styles.fastImageStyle,
                resizeMode: "cover"
              })
            }), renderFlightCode(flightNumber, codeShare)]
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.newBottomContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.newFlightTimeView,
          children: statusColor && (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            style: [styles.newFlightStatus, {
              color: statusColor == null ? undefined : statusColor.toLowerCase()
            }],
            text: (0, _utils.simpleCondition)({
              condition: (0, _isEmpty.default)(beltStatusMapping),
              ifValue: flightStatusMapping,
              elseValue: beltStatusMapping
            }),
            numberOfLines: 2
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.newBottomRightContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.newBottomTerminalContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption2Regular",
              tx: "flightDetails.terminal",
              style: styles.terminalTextColor
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption2Bold",
              text: (0, _utils.simpleCondition)({
                condition: terminal,
                ifValue: terminal,
                elseValue: "-"
              }),
              style: styles.terminalTextColor
            })]
          }), renderNewBottomGateAndCheckInContainer(), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: getBottomSaveFlightContainer,
            children: isMSError ? null : (0, _jsxRuntime.jsx)(_reactNativeWalkthroughTooltip.default, {
              isVisible: shouldShowToolTip,
              content: (0, _jsxRuntime.jsx)(_text.Text, {
                text: (0, _utils.simpleCondition)({
                  condition: activeToolTip == null ? undefined : activeToolTip.tooltip,
                  ifValue: activeToolTip == null ? undefined : activeToolTip.tooltip,
                  elseValue: (0, _i18n.translate)("flyLanding.toolTipFly")
                }),
                preset: "caption1Regular"
              }),
              displayInsets: {
                top: 44,
                bottom: 24,
                left: width * 0.44,
                right: 20
              },
              placement: "bottom",
              onClose: function onClose() {
                // set condition to scroll to top
                dispatch(_flyRedux.FlyCreators.setScrollToTopAfterShowTooltip(true));
                setShowToolTip(false);
              },
              disableShadow: true,
              topAdjustment: handleTopAdjustment(inset),
              children: shouldShowToolTip ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.wrapButtonToolTip,
                children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: styles.buttonStyleAdd,
                  children: (0, _jsxRuntime.jsx)(_plus.default, {})
                })
              }) : buttonSavedFlight()
            })
          })]
        })]
      }), renderTransitCode(transitCodes)]
    });
  }
  var FlightListingCard = function FlightListingCard(props) {
    var isLoading = props.state === _flightListingCard.FlightListingState.loading || (props == null ? undefined : props.getSavedFlightsLoading);
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.container,
      children: isLoading ? loadingView() : defaultView(props)
    });
  };
  var _default = exports.default = _react.default.memo(FlightListingCard);
