  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.CuisineCategory = CuisineCategory;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _cuisineCategory = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var gradientColors = ["rgba(44, 37, 54, 0)", "rgba(16, 15, 26, 0.56)"];
  var COMPONENT_NAME = "CuisineCategory";
  var imageStyle = {
    borderRadius: 16
  };
  var imageStuff = {
    width: 95,
    height: 90,
    borderRadius: 16
  };
  var copyLabelContainer = {
    position: "absolute",
    marginLeft: 8,
    marginRight: 8,
    bottom: 8,
    alignSelf: "center"
  };
  var cardStyle = Object.assign({}, imageStuff, imageStyle);
  var skeletonContent = Object.assign({}, cardStyle, {
    backgroundColor: _theme.color.palette.lightGrey
  });
  var skeletonLayout = {
    width: 62,
    height: 13,
    marginTop: 67,
    marginLeft: 17,
    borderRadius: 4
  };
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: skeletonContent,
      children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: skeletonLayout
      })
    });
  };
  var defaultView = function defaultView(props) {
    var onPressed = props.onPressed,
      imageUrl = props.imageUrl,
      labelCopy = props.labelCopy;
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: onPressed,
      accessible: false,
      children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
        imageStyle: imageStuff,
        source: {
          uri: imageUrl
        },
        children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          colors: gradientColors,
          style: cardStyle,
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: copyLabelContainer,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 1,
              preset: "caption1Bold",
              testID: `${COMPONENT_NAME}__LabelCopy`,
              accessibilityLabel: labelCopy,
              children: labelCopy
            })
          })
        })
      })
    });
  };
  /**
   * Describe your component here
   */
  function CuisineCategory(props) {
    var isLoading = props.type === _cuisineCategory.CuisineCategoryType.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: cardStyle,
      children: isLoading ? loadingView() : defaultView(props)
    });
  }
