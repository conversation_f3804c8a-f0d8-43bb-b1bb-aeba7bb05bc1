  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.iconStyle = exports.InfoBannerType = exports.InfoBannerState = exports.InfoBanner = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _illustration = _$$_REQUIRE(_dependencyMap[6]);
  var _cross = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _animations = _$$_REQUIRE(_dependencyMap[10]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var container = {
    flex: 1,
    width: width - (0, _reactNativeSizeMatters.scale)(45)
  };
  var innerContainer = {
    backgroundColor: _theme.color.palette.lightestPurple,
    borderRadius: 16
  };
  var content = {
    flexDirection: "row"
  };
  var permanentLeftContent = {
    marginLeft: 6,
    marginRight: 5,
    justifyContent: "center"
  };
  var temporaryLeftContent = {
    marginLeft: 16
  };
  var closeButtonContainer = {
    top: 10,
    marginRight: 10,
    width: 10,
    alignItems: "flex-end",
    alignSelf: "flex-end"
  };
  var rightContent = {
    marginTop: 16,
    marginRight: 16,
    flex: 1,
    marginBottom: 16
  };
  var temporaryAnimationStyle = {
    width: 60,
    height: 60
  };
  var permanentAnimationStyle = {
    width: 80,
    height: 80
  };
  var temporaryRightContent = {
    flex: 1,
    marginBottom: 16
  };
  var temporaryTextContainer = {
    marginLeft: 17,
    marginRight: 42,
    flex: 1
  };
  var linkTouchStyle = {
    alignSelf: "flex-start"
  };
  var temporaryTextStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var linkTextStyle = {
    marginTop: 8,
    color: _theme.color.palette.lightPurple,
    textAlign: "left"
  };
  var temporaryLinkTextStyle = {
    color: _theme.color.palette.lightPurple,
    marginTop: 8,
    textAlign: "left"
  };
  var permanentTitleTextStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var permanentTextStyleWithoutTitle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var permanentTextStyleWithtTitle = {
    color: _theme.color.palette.almostBlackGrey,
    marginTop: 4
  };
  var iconStyle = exports.iconStyle = {
    width: 60,
    height: 60
  };
  var whiteGreyLoadingColors = [_theme.color.palette.whiteGrey, _theme.color.background, _theme.color.palette.whiteGrey];
  var loadingContainerStyle = [{
    backgroundColor: _theme.color.palette.lightGrey,
    borderRadius: 16,
    width: width - (0, _reactNativeSizeMatters.scale)(45)
  }, {
    flexDirection: "row"
  }, {
    flexDirection: "column"
  }, {
    flexDirection: "column",
    marginLeft: 18,
    marginTop: 21,
    marginBottom: 17
  }];
  var loadingStyle = [{
    flexDirection: "column"
  }, {
    width: 58,
    height: 58,
    borderRadius: 58,
    marginLeft: 18,
    marginTop: 18,
    marginBottom: 18
  }, {
    flex: 1
  }, {
    width: 188,
    height: 10,
    borderRadius: 2
  }, {
    width: 188,
    height: 10,
    marginTop: 12,
    borderRadius: 2
  }, {
    width: 84,
    height: 10,
    borderRadius: 2,
    marginTop: 10
  }];
  var InfoBannerType = exports.InfoBannerType = /*#__PURE__*/function (InfoBannerType) {
    InfoBannerType["default"] = "default";
    InfoBannerType["loading"] = "loading";
    return InfoBannerType;
  }({});
  var InfoBannerState = exports.InfoBannerState = /*#__PURE__*/function (InfoBannerState) {
    InfoBannerState["temporary"] = "temporaryContent";
    InfoBannerState["permanent"] = "permanentContent";
    return InfoBannerState;
  }({});
  var InfoBannerVariations = {
    permanentContent: permanentContent,
    temporaryContent: temporaryContent
  };
  function temporaryContent(props) {
    var text = props.text,
      linkText = props.linkText;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: temporaryRightContent,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: temporaryTextContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: temporaryTextStyle,
          numberOfLines: 4,
          text: text,
          preset: "caption1Regular"
        }), linkText ? (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 2,
          preset: "caption1Bold",
          text: linkText,
          style: temporaryLinkTextStyle
        }) : null]
      })
    });
  }
  function permanentContent(props) {
    var title = props.title,
      text = props.text,
      linkText = props.linkText,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "PermanentContent" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "PermanentContent" : _props$accessibilityL;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: rightContent,
      children: [title ? (0, _jsxRuntime.jsx)(_text.Text, {
        numberOfLines: 2,
        text: title,
        preset: "bodyTextBold",
        style: permanentTitleTextStyle
      }) : null, (0, _jsxRuntime.jsx)(_text.Text, {
        style: title ? permanentTextStyleWithtTitle : permanentTextStyleWithoutTitle,
        numberOfLines: 4,
        text: text,
        preset: "caption1Regular"
      }), linkText ? (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: linkTouchStyle,
        disabled: true,
        testID: `${testID}__TouchablePermanentContent`,
        accessibilityLabel: `${accessibilityLabel}__TouchablePermanentContent`,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 2,
          preset: "caption1Bold",
          text: linkText,
          style: linkTextStyle
        })
      }) : null]
    });
  }
  function LoadingView() {
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: loadingContainerStyle[0],
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: loadingContainerStyle[1],
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: loadingContainerStyle[2],
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: whiteGreyLoadingColors,
            shimmerStyle: loadingStyle[1]
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: loadingContainerStyle[3],
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: whiteGreyLoadingColors,
            shimmerStyle: loadingStyle[3]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: whiteGreyLoadingColors,
            shimmerStyle: loadingStyle[4]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: whiteGreyLoadingColors,
            shimmerStyle: loadingStyle[5]
          })]
        })]
      })
    });
  }
  var renderIcon = function renderIcon(isTemporary, props) {
    var title = props.title,
      text = props.text,
      linkText = props.linkText,
      iconUrl = props.iconUrl;
    var iconSource;
    if (isTemporary) {
      iconSource = _animations.Animations.giftBox;
    } else {
      if (linkText || title) {
        iconSource = _animations.Animations.coins;
      } else {
        iconSource = _animations.Animations.clock;
      }
    }
    if (iconUrl) {
      return (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: iconUrl
        },
        style: iconStyle
      });
    } else if (title || linkText || text) {
      return (0, _jsxRuntime.jsx)(_illustration.Illustration, {
        loop: true,
        progress: 0,
        style: isTemporary ? temporaryAnimationStyle : permanentAnimationStyle,
        source: iconSource
      });
    } else {
      return null;
    }
  };
  var DefaultView = function DefaultView(props) {
    var state = props.state,
      linkText = props.linkText,
      onPressed = props.onPressed,
      onClosed = props.onClosed,
      link = props.link,
      _props$testID2 = props.testID,
      testID = _props$testID2 === undefined ? "InfoBanner" : _props$testID2,
      _props$accessibilityL2 = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL2 === undefined ? "InfoBanner" : _props$accessibilityL2;
    var isTemporary = state === InfoBannerState.temporary;
    var InfoBannerVariation = InfoBannerVariations[state];
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: container,
      children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        disabled: !link && !linkText,
        onPress: onPressed,
        testID: `${testID}__TouchableInforBanner`,
        accessibilityLabel: `${accessibilityLabel}__TouchableInforBanner`,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: innerContainer,
          children: [isTemporary ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: closeButtonContainer,
            children: (0, _jsxRuntime.jsx)(_cross.default, {
              onPress: onClosed,
              color: _theme.color.palette.lightPurple
            })
          }) : null, (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: content,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: isTemporary ? temporaryLeftContent : permanentLeftContent,
              children: renderIcon(isTemporary, props)
            }), (0, _jsxRuntime.jsx)(InfoBannerVariation, Object.assign({}, props, {
              testID: `${testID}__InfoBannerVariation`,
              accessibilityLabel: `${accessibilityLabel}__InfoBannerVariation`
            }))]
          })]
        })
      })
    });
  };
  var InfoBanner = exports.InfoBanner = function InfoBanner(props) {
    var isLoading = props.type === InfoBannerType.loading;
    return isLoading ? (0, _jsxRuntime.jsx)(LoadingView, {}) : (0, _jsxRuntime.jsx)(DefaultView, Object.assign({}, props));
  };
