  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomContainer: {
      display: "flex",
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-evenly"
    },
    buttonAlertStyle: {
      alignItems: "center",
      height: 44,
      justifyContent: "center",
      margin: "auto",
      width: "100%"
    },
    buttonStyle: {
      alignItems: "center",
      borderRightColor: _theme.color.palette.lineColorCommonPopup,
      borderRightWidth: 0.5,
      height: 44,
      justifyContent: "center",
      margin: "auto",
      width: "50%"
    },
    "buttonStyle:last-child": {
      borderRightWidth: 0
    },
    contentTextStyles: {
      color: _theme.color.palette.black,
      fontSize: _responsive.default.getFontSize(13),
      letterSpacing: -0.08,
      lineHeight: _responsive.default.getFontSize(16),
      textAlign: "center"
    },
    descriptionContainer: {
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: 16,
      paddingVertical: 19
    },
    lineStyles: {
      backgroundColor: _theme.color.palette.lineColorCommonPopup,
      height: 0.5,
      width: "100%"
    },
    parentContainer: {
      backgroundColor: _theme.color.palette.backgroundCommonPopupNoOpacity,
      borderRadius: 14,
      width: 270
    },
    textBluStyles: {
      color: _theme.color.palette.lightBlue,
      fontSize: _responsive.default.getFontSize(17),
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      letterSpacing: -0.41,
      lineHeight: _responsive.default.getFontSize(22)
    },
    titleStyles: {
      color: _theme.color.palette.black,
      fontSize: _responsive.default.getFontSize(17),
      lineHeight: _responsive.default.getFontSize(22),
      marginBottom: 5
    }
  });
