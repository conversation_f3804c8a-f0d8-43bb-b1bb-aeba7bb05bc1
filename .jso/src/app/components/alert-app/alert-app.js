  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.AlertApp = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _modalCommon = _$$_REQUIRE(_dependencyMap[6]);
  var _alertApp = _$$_REQUIRE(_dependencyMap[7]);
  var _alertApp2 = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var INIT_STATE = {
    title: "Tile Alert",
    description: "Description Alert",
    labelAccept: "Yes",
    labelDeny: "No",
    onAccept: function onAccept() {
      return null;
    },
    onDeny: function onDeny() {
      return null;
    },
    testID: "AlertApp",
    accessibilityLabel: "AlertApp",
    type: _alertApp.AlertTypes.ALERT
  };
  var ComponentAlert = function ComponentAlert(_, ref) {
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isShow = _useState2[0],
      showModal = _useState2[1];
    var _useState3 = (0, _react.useState)(INIT_STATE),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      alertProperty = _useState4[0],
      setAlertProperty = _useState4[1];
    var show = function show(props) {
      if (_reactNative2.Platform.OS === "ios") {
        var listAction = (0, _utils.handleCondition)(type !== _alertApp.AlertTypes.ALERT, [{
          text: (props == null ? undefined : props.labelAccept) || (0, _i18n.translate)("flightLanding.okay"),
          onPress: function onPress() {
            return onAccept;
          }
        }, {
          text: (props == null ? undefined : props.labelDeny) || (0, _i18n.translate)("flightLanding.cancel"),
          onPress: function onPress() {
            return onDeny;
          }
        }], [{
          text: (props == null ? undefined : props.labelAccept) || (0, _i18n.translate)("flightLanding.okay"),
          onPress: function onPress() {
            return onAccept;
          }
        }]);
        _reactNative2.Alert.alert((props == null ? undefined : props.title) || (0, _i18n.translate)("flightLanding.alert"), props == null ? undefined : props.description, listAction);
        return;
      }
      showModal(true);
      setAlertProperty(Object.assign({}, alertProperty, props));
    };
    var handleAccept = function handleAccept() {
      onAccept == null || onAccept();
      showModal(false);
    };
    var handleDeny = function handleDeny() {
      onDeny == null || onDeny();
      showModal(false);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        show: show
      };
    });
    var title = alertProperty.title,
      description = alertProperty.description,
      labelAccept = alertProperty.labelAccept,
      labelDeny = alertProperty.labelDeny,
      onAccept = alertProperty.onAccept,
      onDeny = alertProperty.onDeny,
      testID = alertProperty.testID,
      accessibilityLabel = alertProperty.accessibilityLabel,
      type = alertProperty.type;
    return (0, _jsxRuntime.jsx)(_modalCommon.ModalCommonComponent, {
      visible: isShow,
      animationInTiming: 250,
      animationOutTiming: 150,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _alertApp2.styles.parentContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _alertApp2.styles.descriptionContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextBold",
            text: title,
            style: _alertApp2.styles.titleStyles
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "fieldLabel",
            text: description,
            style: _alertApp2.styles.contentTextStyles
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _alertApp2.styles.lineStyles
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _alertApp2.styles.bottomContainer,
          children: type === _alertApp.AlertTypes.ALERT ? (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: _alertApp2.styles.buttonAlertStyle,
            onPress: handleAccept,
            testID: `${testID}__Accept`,
            accessibilityLabel: `${accessibilityLabel}__Accept`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "bodyTextRegular",
              text: labelAccept,
              style: _alertApp2.styles.textBluStyles
            })
          }) : (0, _jsxRuntime.jsxs)(_react.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: _alertApp2.styles.buttonStyle,
              onPress: handleDeny,
              testID: `${testID}__Denied`,
              accessibilityLabel: `${accessibilityLabel}__Denied`,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "bodyTextRegular",
                text: labelDeny,
                style: _alertApp2.styles.textBluStyles
              })
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: _alertApp2.styles.buttonStyle,
              onPress: handleAccept,
              testID: `${testID}__Accept`,
              accessibilityLabel: `${accessibilityLabel}__Accept`,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "bodyTextBold",
                text: labelAccept,
                style: _alertApp2.styles.textBluStyles
              })
            })]
          })
        })]
      })
    });
  };
  var AlertApp = exports.AlertApp = (0, _react.forwardRef)(ComponentAlert);
