  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.InputField = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _textField = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _utils = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var InputField = exports.InputField = function InputField(props) {
    // Props
    var value = props.value,
      passwordReveal = props.passwordReveal,
      highlightOnFocused = props.highlightOnFocused,
      textContentType = props.textContentType,
      trim = props.trim,
      search = props.search,
      isInvalid = props.isInvalid,
      isDisabled = props.isDisabled,
      isFocused = props.isFocused,
      _onFocus = props.onFocus,
      _onBlur = props.onBlur,
      _onChangeText = props.onChangeText,
      onSearchClear = props.onSearchClear,
      placeHolderValue = props.placeHolderValue,
      _props$isShowClearAll = props.isShowClearAll,
      isShowClearAll = _props$isShowClearAll === undefined ? false : _props$isShowClearAll,
      customLeftIconActive = props.customLeftIconActive,
      customLeftIconInActive = props.customLeftIconInActive,
      highlightOnFocusedStyle = props.highlightOnFocusedStyle,
      selectionColor = props.selectionColor,
      alwaysShowIconPassword = props.alwaysShowIconPassword,
      _props$iconEyeShow = props.iconEyeShow,
      iconEyeShow = _props$iconEyeShow === undefined ? (0, _jsxRuntime.jsx)(_icons.EyeShow, {}) : _props$iconEyeShow,
      _props$iconEyeHide = props.iconEyeHide,
      iconEyeHide = _props$iconEyeHide === undefined ? (0, _jsxRuntime.jsx)(_icons.EyeHide, {}) : _props$iconEyeHide,
      disableStyle = props.disableStyle,
      customStyle = props.customStyle,
      iconClose = props.iconClose;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showPassword = _useState2[0],
      setShowPassword = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      focused = _useState4[0],
      setFocused = _useState4[1];

    // Show password reveal icon
    var showPasswordReveal = (0, _react.useMemo)(function () {
      return passwordReveal && (value == null ? undefined : value.length) > 0;
    }, [passwordReveal, value]);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: Object.assign({}, styles.inputGroupStyle, highlightOnFocused && (focused || isFocused) ? Object.assign({}, styles.inputGroupFocusedStyle, highlightOnFocusedStyle) : {}, isInvalid ? styles.inputGroupErrorStyle : {}, isDisabled ? Object.assign({}, styles.inputGroupDisabledStyle, disableStyle) : {}, customStyle),
      children: [search && (focused ? (0, _utils.handleCondition)(customLeftIconActive, customLeftIconActive, (0, _jsxRuntime.jsx)(_icons.SearchIcon, {
        style: styles.searchIconStyle,
        width: "18",
        height: "18"
      })) : (0, _utils.handleCondition)(customLeftIconInActive, customLeftIconInActive, (0, _jsxRuntime.jsx)(_icons.SearchIconInActive, {
        style: styles.searchIconStyle,
        width: "18",
        height: "18"
      }))), (0, _jsxRuntime.jsx)(_textField.TextField, Object.assign({
        style: styles.inputFieldStyle,
        inputStyle: Object.assign({}, styles.inputControlStyle, isDisabled ? styles.inputControlDisabledStyle : {}),
        placeholderTextColor: _theme.color.palette.midGrey,
        secureTextEntry: textContentType === "password" ? !showPassword : false,
        preset: "noMargin",
        editable: !isDisabled,
        allowFontScaling: false
      }, props, {
        onBlur: function onBlur(e) {
          setFocused(false);
          _onBlur && _onBlur(e);
        },
        onFocus: function onFocus(e) {
          setFocused(true);
          _onFocus && _onFocus(e);
        },
        onChangeText: function onChangeText(e) {
          var val = trim && e ? e.trim() : e;
          _onChangeText && _onChangeText(val);
        },
        placeholder: focused ? "" : placeHolderValue || null,
        selectionColor: selectionColor
      })), isShowClearAll && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.searchIconContainer,
        onPress: onSearchClear,
        children: iconClose ? (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: iconClose
        }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_icons.CircleGrayFilled, {
            width: "24",
            height: "24"
          }), (0, _jsxRuntime.jsx)(_icons.CrossWhite, {
            width: "10",
            height: "10",
            style: styles.searchDeleteStyle
          })]
        })
      }), alwaysShowIconPassword || showPasswordReveal ? (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.inputIconStyle,
        onPress: function onPress() {
          return setShowPassword(!showPassword);
        },
        children: (0, _utils.handleCondition)(showPassword, iconEyeShow, iconEyeHide)
      }) : null]
    });
  };
  var _default = exports.default = InputField;
