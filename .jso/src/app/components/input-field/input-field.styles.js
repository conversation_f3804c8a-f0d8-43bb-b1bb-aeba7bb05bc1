  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.searchIconStyle = exports.searchIconContainer = exports.searchDeleteStyle = exports.inputIconStyle = exports.inputGroupStyle = exports.inputGroupFocusedStyle = exports.inputGroupErrorStyle = exports.inputGroupDisabledStyle = exports.inputFieldStyle = exports.inputControlStyle = exports.inputControlDisabledStyle = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var inputGroupStyle = exports.inputGroupStyle = {
    borderColor: _theme.color.palette.lightGrey,
    flexDirection: "row",
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 44,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var inputGroupErrorStyle = exports.inputGroupErrorStyle = {
    borderColor: _theme.color.palette.baseRed
  };
  var inputGroupDisabledStyle = exports.inputGroupDisabledStyle = {
    borderColor: _theme.color.palette.lighterGrey,
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var inputGroupFocusedStyle = exports.inputGroupFocusedStyle = {
    borderColor: _theme.color.palette.lightPurple
  };
  var inputFieldStyle = exports.inputFieldStyle = {
    flex: 1
  };
  var inputControlStyle = exports.inputControlStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.almostBlackGrey,
    minHeight: 0,
    paddingTop: 11,
    paddingBottom: 0
  });
  var inputControlDisabledStyle = exports.inputControlDisabledStyle = {
    color: _theme.color.palette.midGrey,
    backgroundColor: "transparent"
  };
  var inputIconStyle = exports.inputIconStyle = {
    marginHorizontal: 5,
    justifyContent: "center"
  };
  var searchIconStyle = exports.searchIconStyle = {
    marginLeft: 5,
    marginRight: 10,
    marginTop: 12
  };
  var searchIconContainer = exports.searchIconContainer = {
    position: "relative",
    justifyContent: "center",
    alignItems: "center",
    height: 44
  };
  var searchDeleteStyle = exports.searchDeleteStyle = {
    position: "absolute",
    zIndex: 99
  };
