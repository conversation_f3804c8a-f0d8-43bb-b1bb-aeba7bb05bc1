  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _color = _$$_REQUIRE(_dependencyMap[7]);
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _latestHappening = _$$_REQUIRE(_dependencyMap[9]);
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var darkGradientColors = ["rgba(0, 0, 0, 0)", "rgba(44, 37, 54, 0)", "rgba(41, 34, 56, 0.45)", "rgba(40, 38, 56, 0.7)"];
  var lightGradientColors = ["rgba(0, 0, 0, 0)", "rgba(255, 255, 255, 0)", "rgba(255, 255, 255, 0.7)", "rgba(255, 255, 255, 0.91)"];
  var noGradientColors = ["rgba(0, 0, 0, 0)", "rgba(0, 0, 0, 0)", "rgba(0, 0, 0, 0)", "rgba(0, 0, 0, 0)"];
  var otherGradientProps = Object.assign({}, {
    useAngle: true,
    angle: 180
  });
  var shadowStyle = Object.assign({}, _reactNative2.Platform.select({
    ios: {
      shadowRadius: 2,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 3
      }
    },
    android: {
      elevation: 3
    }
  }), {
    shadowColor: _color.color.palette.almostBlackGrey,
    backgroundColor: _color.color.palette.lightGrey,
    borderRadius: 16
  });
  var imageStyle = {
    borderRadius: 16,
    overflow: "hidden"
  };
  var imageHorizontalContainerStyle = {
    width: width * 0.52,
    height: width * 0.33,
    borderRadius: 16
  };
  var imageVerticalContainerStyle = {
    width: width * 0.47,
    height: width * 0.69,
    borderRadius: 16
  };
  var containerStyleVertical = Object.assign({}, shadowStyle, imageVerticalContainerStyle);
  var containerStyleHorizontal = Object.assign({}, shadowStyle, imageHorizontalContainerStyle);
  var gradientStyle = {
    position: "absolute",
    borderRadius: 16,
    height: 220,
    bottom: 0,
    left: 0,
    right: 0
  };
  var gradientHorizontalStyle = Object.assign({}, gradientStyle, {
    height: 120
  });
  var textContainer = {
    position: "absolute",
    bottom: 0,
    paddingHorizontal: 12
  };
  var subTitleTextStyle = {
    color: _color.color.palette.whiteGrey,
    marginBottom: 12,
    marginTop: 4
  };
  var gradientLightTextStyle = {
    color: _color.color.palette.almostBlackGrey
  };
  var gradientDarkTextStyle = {
    color: _color.color.palette.whiteGrey
  };
  var bottomSkeletonStyle = {
    position: "absolute",
    bottom: 15,
    width: "100%"
  };
  var lightGreyLoadingColors = [_color.color.palette.lightGrey, _color.color.background, _color.color.palette.lightGrey];
  var whiteGreyLoadingColors = [_color.color.palette.whiteGrey, _color.color.background, _color.color.palette.whiteGrey];
  var skeletonLayout = [{
    width: 115,
    height: 13,
    borderRadius: 4,
    marginLeft: 12,
    marginTop: 0
  }, {
    width: 56,
    height: 13,
    borderRadius: 4,
    marginLeft: 12,
    marginTop: 9
  }];
  var handleGradient = function handleGradient(gradientInf) {
    switch (gradientInf) {
      case _latestHappening.LatestHappeningType.defaultDarkGradient:
        return darkGradientColors;
      case _latestHappening.LatestHappeningType.defaultLightGradient:
        return lightGradientColors;
      default:
        return noGradientColors;
    }
  };
  var defaultView = function defaultView(props) {
    var imageUrl = props.imageUrl,
      title = props.title,
      subtitle = props.subtitle,
      onPressed = props.onPressed,
      orientation = props.orientation,
      gradientInfo = props.gradientInfo,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "LatestHappening" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "LatestHappening" : _props$accessibilityL;
    var isVerticalView = orientation === _latestHappening.LatestHappeningOrientation.tall;
    var darkGradient = gradientInfo === _latestHappening.LatestHappeningType.defaultDarkGradient;
    var textStyle = darkGradient ? gradientDarkTextStyle : gradientLightTextStyle;
    var gradientColor = handleGradient(gradientInfo);
    var imageBackgroundStyle = isVerticalView ? Object.assign({}, imageVerticalContainerStyle) : Object.assign({}, imageHorizontalContainerStyle);
    var containerStyle = isVerticalView ? Object.assign({}, containerStyleVertical) : Object.assign({}, containerStyleHorizontal);
    var maxTitleLength = isVerticalView ? 35 : 20;
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: onPressed,
      style: containerStyle,
      testID: `${testID}__TouchableItemLatestHappening`,
      accessibilityLabel: `${accessibilityLabel}__TouchableItemLatestHappening`,
      accessible: false,
      children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
        style: [imageBackgroundStyle, imageStyle],
        imageStyle: [imageBackgroundStyle, imageStyle],
        source: {
          uri: imageUrl
        },
        children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, Object.assign({
          colors: gradientColor
        }, otherGradientProps, {
          style: gradientHorizontalStyle,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: textContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: isVerticalView ? 2 : 1,
              preset: "bodyTextBold",
              textBreakStrategy: "highQuality",
              style: textStyle,
              children: (title == null ? undefined : title.length) > maxTitleLength + 3 ? (title == null ? undefined : title.substring(0, maxTitleLength)) + "..." : title
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: Object.assign({}, subTitleTextStyle, textStyle),
              numberOfLines: 1,
              preset: "caption1Regular",
              children: subtitle
            })]
          })
        }))
      })
    });
  };
  var loadingView = function loadingView(props) {
    var isVerticalView = props.orientation === _latestHappening.LatestHappeningOrientation.tall;
    var imageBackgroundStyle = isVerticalView ? Object.assign({}, imageVerticalContainerStyle) : Object.assign({}, imageHorizontalContainerStyle);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: imageBackgroundStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: bottomSkeletonStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: whiteGreyLoadingColors,
          shimmerStyle: skeletonLayout[0]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: whiteGreyLoadingColors,
          shimmerStyle: skeletonLayout[1]
        })]
      })]
    });
  };
  var LatestHappening = function LatestHappening(props) {
    var isLoading = props.gradientInfo === _latestHappening.LatestHappeningType.loading;
    return isLoading ? loadingView(props) : defaultView(props);
  };
  var _default = exports.default = React.memo(LatestHappening);
