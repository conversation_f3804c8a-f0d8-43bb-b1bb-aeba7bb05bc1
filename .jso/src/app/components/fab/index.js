  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FAB = undefined;
  var _bottomNavigator = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AnimatedPressable = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.Pressable);
  var _worklet_16018150355076_init_data = {
    code: "function indexTsx1(){const{bottomTabActualHeight,bottomTabsPosition,isL2AnnouncementDisplay,l2AnnouncementPosition,opacity}=this.__closure;let bottomValue=Math.max(bottomTabActualHeight.value,80)+bottomTabsPosition.value+20;if(isL2AnnouncementDisplay.value){var _l2AnnouncementPositi;bottomValue=(_l2AnnouncementPositi=l2AnnouncementPosition)===null||_l2AnnouncementPositi===void 0?void 0:_l2AnnouncementPositi.value;}return{bottom:bottomValue,opacity:opacity.value,pointerEvents:opacity.value===1?\"auto\":\"none\"};}"
  };
  var FAB = exports.FAB = function FAB(props) {
    var opacity = props.opacity,
      _onPress = props.onPress;
    var _useContext = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      bottomTabsPosition = _useContext.bottomTabsPosition,
      bottomTabActualHeight = _useContext.bottomTabActualHeight,
      l2AnnouncementPosition = _useContext.l2AnnouncementPosition,
      isL2AnnouncementDisplay = _useContext.isL2AnnouncementDisplay;
    var animatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx1 = function indexTsx1() {
        var bottomValue = Math.max(bottomTabActualHeight.value, 80) + bottomTabsPosition.value + 20;
        if (isL2AnnouncementDisplay.value) {
          bottomValue = l2AnnouncementPosition == null ? undefined : l2AnnouncementPosition.value;
        }
        return {
          bottom: bottomValue,
          opacity: opacity.value,
          pointerEvents: opacity.value === 1 ? "auto" : "none"
        };
      };
      indexTsx1.__closure = {
        bottomTabActualHeight: bottomTabActualHeight,
        bottomTabsPosition: bottomTabsPosition,
        isL2AnnouncementDisplay: isL2AnnouncementDisplay,
        l2AnnouncementPosition: l2AnnouncementPosition,
        opacity: opacity
      };
      indexTsx1.__workletHash = 16018150355076;
      indexTsx1.__initData = _worklet_16018150355076_init_data;
      return indexTsx1;
    }(), [bottomTabsPosition == null ? undefined : bottomTabsPosition.value, bottomTabActualHeight == null ? undefined : bottomTabActualHeight.value, l2AnnouncementPosition == null ? undefined : l2AnnouncementPosition.value]);
    return (0, _jsxRuntime.jsxs)(AnimatedPressable, {
      style: [styles.fabContainerViewStyle, animatedStyle],
      testID: "scrollToTopButton",
      accessibilityLabel: "scrollToTopButton",
      onPress: function onPress() {
        return _onPress();
      },
      children: [(0, _jsxRuntime.jsx)(_icons.ArrowUp, {
        style: styles.fabArrowStyle,
        height: 24,
        width: 24
      }), (0, _jsxRuntime.jsx)(_backgrounds.FabBackground, {})]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    fabArrowStyle: {
      position: "absolute",
      zIndex: 1
    },
    fabContainerViewStyle: {
      alignItems: "center",
      alignSelf: "flex-end",
      justifyContent: "center",
      position: "absolute",
      right: 12
    }
  });
