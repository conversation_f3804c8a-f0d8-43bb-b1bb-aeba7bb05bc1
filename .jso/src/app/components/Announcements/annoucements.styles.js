  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    activeDotStyle: {
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 4,
      height: 8,
      marginLeft: 3,
      marginRight: 3,
      width: 8
    },
    announcementsContainerStyle: {
      flex: 1
    },
    buttonStyle: {
      bottom: 5,
      color: _theme.color.palette.lightPurple,
      justifyContent: "flex-start",
      position: "absolute"
    },
    buttonTextStyle: {
      alignSelf: "flex-start"
    },
    containerStyle: Object.assign({}, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        }
      },
      android: {
        elevation: 3
      }
    }), {
      alignSelf: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      paddingBottom: 10,
      width: "100%"
    }),
    contentContainer: {
      flex: 1,
      marginBottom: 8,
      marginHorizontal: 16,
      marginTop: 16
    },
    contentWithIconStyte: {
      flexDirection: "row",
      flex: 1
    },
    defaultDotStyle: {
      borderRadius: 4,
      height: 8,
      marginLeft: 3,
      marginRight: 3,
      width: 8
    },
    descriptionContainer: {
      flex: 1,
      marginBottom: 8,
      marginLeft: 16
    },
    dotStyle: {
      backgroundColor: _theme.color.palette.lightGrey,
      borderRadius: 4,
      height: 8,
      marginLeft: 3,
      marginRight: 3,
      width: 8
    },
    imageStyle: {
      height: 48,
      marginTop: 6,
      width: 48
    },
    linkMoreContainer: {
      height: 36,
      maxWidth: "70%"
    },
    paginationStyle: {
      bottom: 9,
      left: null,
      right: 16
    },
    skeletonContainerStyle: Object.assign({}, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        }
      },
      android: {
        elevation: 3
      }
    }), {
      alignSelf: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      height: 186,
      width: "100%"
    }),
    titleStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 8
    }
  });
