  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Announcements = Announcements;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeSwiper = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _announcements = _$$_REQUIRE(_dependencyMap[9]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[10]);
  var _annoucements = _$$_REQUIRE(_dependencyMap[11]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[12]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var lightGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayoutImage = [{
    height: 8,
    width: 78,
    borderRadius: 2,
    marginLeft: 16,
    marginTop: 26
  }, {
    height: 18,
    width: "90%",
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 13
  }, {
    height: 18,
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 28,
    width: "75%"
  }, {
    height: 18,
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 9,
    width: "75%"
  }, {
    height: 18,
    width: 100,
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 11,
    marginBottom: 31
  }];
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _annoucements.styles.skeletonContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayoutImage[0]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayoutImage[1]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayoutImage[2]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayoutImage[3]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayoutImage[4]
      })]
    });
  };
  var defaultView = function defaultView(props) {
    var onPressed = props.onPressed,
      announcementsData = props.announcementsData,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "Announcements" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "Announcements" : _props$accessibilityL;
    var announcementsList = announcementsData == null ? undefined : announcementsData.contents;
    var shouldShowPagination = (announcementsList == null ? undefined : announcementsList.length) > 1;
    var getTimeString = function getTimeString(time) {
      var timeAtSingapore = (0, _dateTime.convertGMTToSingapore)(time);
      var dayMonthYear = (0, _screenHelper.formatDateEnToType)(timeAtSingapore, _dateTime.DateFormats.DateTime, _dateTime.DateFormats.DayMonthYear);
      var hoursMinutes = (0, _screenHelper.formatDateEnToType)((0, _dateTime.convertGMTToSingapore)(time), _dateTime.DateFormats.DateTime, _dateTime.DateFormats.HoursMinutes);
      return dayMonthYear + " at " + hoursMinutes;
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _annoucements.styles.containerStyle,
      children: (0, _jsxRuntime.jsx)(_reactNativeSwiper.default, {
        height: 186,
        showsPagination: shouldShowPagination,
        dot: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _annoucements.styles.dotStyle
        }),
        activeDot: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _annoucements.styles.activeDotStyle
        }),
        paginationStyle: _annoucements.styles.paginationStyle,
        testID: `${testID}__SwiperAnnouncements`,
        accessibilityLabel: `${accessibilityLabel}__SwiperAnnouncements`,
        children: announcementsList == null ? undefined : announcementsList.map(function (item, index) {
          var withIcon = item.announcementType === _announcements.AnnouncementType.icon;
          var linkButtonStyle = {
            left: withIcon ? 80 : 16
          };
          return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: _annoucements.styles.announcementsContainerStyle,
            onPress: function onPress() {
              return onPressed(item);
            },
            testID: `${testID}__TouchableItemAnnouncements`,
            accessibilityLabel: `${accessibilityLabel}__TouchableItemAnnouncements`,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _annoucements.styles.contentContainer,
              children: withIcon ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _annoucements.styles.contentWithIconStyte,
                children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
                  source: {
                    uri: (0, _screenHelper.getUriImage)(item.icon)
                  },
                  style: _annoucements.styles.imageStyle,
                  resizeMode: "contain"
                }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _annoucements.styles.descriptionContainer,
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "bodyTextBold",
                    text: item == null ? undefined : item.title,
                    numberOfLines: 2,
                    style: _annoucements.styles.titleStyle
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "caption1Regular",
                    text: item == null ? undefined : item.description,
                    numberOfLines: 4,
                    style: _annoucements.styles.titleStyle
                  })]
                })]
              }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _annoucements.styles.announcementsContainerStyle,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "caption2Italic",
                  text: getTimeString(item == null ? undefined : item.startDate),
                  numberOfLines: 1
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  text: item.title,
                  numberOfLines: 2,
                  style: _annoucements.styles.titleStyle
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "caption1Regular",
                  text: item.description,
                  numberOfLines: 3,
                  style: _annoucements.styles.titleStyle
                })]
              })
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _annoucements.styles.linkMoreContainer,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "caption1Bold",
                numberOfLines: 1,
                style: [_annoucements.styles.buttonStyle, linkButtonStyle],
                text: item == null ? undefined : item.link
              })
            })]
          }, index.toString());
        })
      })
    });
  };
  function Announcements(props) {
    var _props$announcementsD;
    var isLoading = _announcements.AnnouncementsType.loading === props.type;
    var renderAnnouncements = (0, _react.useMemo)(function () {
      return defaultView(props);
    }, [props == null || (_props$announcementsD = props.announcementsData) == null ? undefined : _props$announcementsD.contents]);
    return isLoading ? loadingView() : renderAnnouncements;
  }
