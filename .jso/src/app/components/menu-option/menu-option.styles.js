  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.lighterGreyLoadingColors = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var lighterGreyLoadingColors = exports.lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 12,
      paddingVertical: 14,
      paddingHorizontal: 12,
      flexDirection: "row",
      alignItems: "center"
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    iconStyle: {
      width: 20,
      height: 20,
      resizeMode: "contain"
    },
    containerTitleStyle: {
      flex: 1,
      marginHorizontal: 10
    },
    titleStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    labelStyle: Object.assign({}, _text.presets.caption2Regular, {
      color: _theme.color.palette.darkestGrey,
      marginTop: 4
    }),
    loadingIcon: {
      width: 200,
      height: 20,
      borderRadius: 4
    },
    loadingIconMenu: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: _theme.color.palette.lighterGrey
    },
    containerStyleV2: {
      flexDirection: "row",
      paddingVertical: 16,
      alignItems: "center",
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    titleStyleV2: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 16,
      marginRight: 16,
      fontSize: 16
    }),
    optionPlaceholderStyle: {
      marginVertical: 4
    },
    loadingIconV2: {
      borderRadius: 4,
      height: 12,
      width: 172
    }
  });
