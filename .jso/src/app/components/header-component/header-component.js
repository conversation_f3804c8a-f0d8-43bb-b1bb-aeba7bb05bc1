  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _native = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var HeaderComponent = function HeaderComponent(props) {
    var title = props.title,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "HeaderComponent" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "HeaderComponent" : _props$accessibilityL,
      _props$containerStyle = props.containerStyle,
      containerStyle = _props$containerStyle === undefined ? {} : _props$containerStyle,
      rightContent = props.rightContent;
    var navigation = (0, _native.useNavigation)();
    var marginTopView = {
      paddingTop: _reactNative2.Platform.OS === "android" ? _reactNative2.StatusBar.currentHeight + 22 : 0,
      marginBottom: _reactNative2.Platform.OS === "android" ? 22 : 0
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: [styles.container, containerStyle, marginTopView],
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.leftStyles,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.backIcon,
          onPress: function onPress() {
            return navigation.goBack();
          },
          testID: `${testID}__TouchableBack`,
          accessibilityLabel: `${accessibilityLabel}__TouchableBack`,
          children: (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {
            width: "24",
            height: "24"
          })
        })
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "subTitleBold",
        text: title,
        style: styles.titleStyles,
        numberOfLines: 1
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.rightContainer,
        children: rightContent
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    backIcon: {
      height: 24,
      width: 24
    },
    container: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      flexDirection: "row",
      justifyContent: "center",
      width: "100%"
    },
    leftStyles: {
      paddingHorizontal: 20,
      width: "15%"
    },
    rightContainer: {
      width: "15%"
    },
    titleStyles: {
      color: _theme.color.palette.almostBlackGrey,
      flex: 1,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      justifyContent: "center",
      textAlign: "center"
    }
  });
  var _default = exports.default = HeaderComponent;
