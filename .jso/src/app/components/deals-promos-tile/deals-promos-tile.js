  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _staffPerkTileV = _$$_REQUIRE(_dependencyMap[3]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[6]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[7]);
  var _react = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _theme = _$$_REQUIRE(_dependencyMap[10]);
  var _dealsPromosFilterBar = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var COMPONENT_NAME = "DealsPromosTile";
  var DealsPromosTile = function DealsPromosTile(props) {
    var _item$type, _item$categories;
    var dataLength = props.dataLength,
      index = props.index,
      item = props.item,
      navigation = props.navigation,
      onLayout = props.onLayout;
    var startDateValue = item != null && item.campaignStartDate ? (0, _moment.default)(item == null ? undefined : item.campaignStartDate).format(_dateTime.DateFormats.DayMonthYear) : "";
    var showLimitedOffer = (item == null || (_item$type = item.type) == null ? undefined : _item$type.toLowerCase()) === "seasonal";
    var showNewlyAdded = item == null || (_item$categories = item.categories) == null || _item$categories.some == null ? undefined : _item$categories.some(function (val) {
      return val === _dealsPromosFilterBar.DealsPromosCategory.NewlyAdded;
    });
    var containerStyle = (0, _react.useMemo)(function () {
      var result = [_staffPerkTileV.styles.containerStyle];
      if (index === 0) {
        result.push(_staffPerkTileV.styles.firstItemContainerStyle);
      }
      if (index === dataLength - 1) {
        result.push(_staffPerkTileV.styles.lastItemContainerStyle);
      }
      return result;
    }, [dataLength, index]);
    var handleOpenDetail = function handleOpenDetail() {
      // TODO: Implement deals/promos detail
      console.log("Detail navigation is not yet implemented.");
    };
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      accessibilityLabel: `${COMPONENT_NAME}_Container`,
      onLayout: onLayout,
      onPress: handleOpenDetail,
      style: containerStyle,
      testID: `${COMPONENT_NAME}_Container`,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        accessibilityLabel: `${COMPONENT_NAME}_Image`,
        source: {
          uri: item == null ? undefined : item.imageUrl
        },
        style: _staffPerkTileV.styles.imageStyle,
        testID: `${COMPONENT_NAME}_Image`
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _staffPerkTileV.styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          accessibilityLabel: `${COMPONENT_NAME}_TenantName`,
          style: _staffPerkTileV.styles.tenantNameTextStyle,
          testID: `${COMPONENT_NAME}_TenantName`,
          text: (item == null ? undefined : item.tenantName) || (item == null ? undefined : item.merchantName),
          numberOfLines: 1
        }), showLimitedOffer && (0, _jsxRuntime.jsxs)(_reactNative.View, {
          accessibilityLabel: `${COMPONENT_NAME}_Label_LimitedOffer`,
          style: _staffPerkTileV.styles.perkLabelContainerStyle,
          testID: `${COMPONENT_NAME}_Label_LimitedOffer`,
          children: [(0, _jsxRuntime.jsx)(_icons.StaffPerkClockIcon, {
            color: _theme.color.palette.lightOrange,
            height: 12,
            width: 12
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _staffPerkTileV.styles.perkLabelTextStyle,
            tx: "dealsPromosListing.item.limitedOffer"
          })]
        }), showNewlyAdded && (0, _jsxRuntime.jsxs)(_reactNative.View, {
          accessibilityLabel: `${COMPONENT_NAME}_Label_NewlyAdded`,
          style: _staffPerkTileV.styles.perkLabelContainerStyle,
          testID: `${COMPONENT_NAME}_Label_NewlyAdded`,
          children: [(0, _jsxRuntime.jsx)(_icons.StarPerk, {
            color: _theme.color.palette.lightOrange,
            height: 12,
            width: 12
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _staffPerkTileV.styles.perkLabelTextStyle,
            tx: "dealsPromosListing.item.newlyAdded"
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          accessibilityLabel: `${COMPONENT_NAME}_Title`,
          numberOfLines: 3,
          style: _staffPerkTileV.styles.titleTextStyle,
          testID: `${COMPONENT_NAME}_Title`,
          text: item == null ? undefined : item.title
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          accessibilityLabel: `${COMPONENT_NAME}_StartDate`,
          numberOfLines: 3,
          style: _staffPerkTileV.styles.startDateTextStyle,
          testID: `${COMPONENT_NAME}_StartDate`,
          tx: startDateValue ? "dealsPromosListing.item.fromDateMsg" : "",
          txOptions: {
            date: startDateValue
          }
        })]
      })]
    });
  };
  var _default = exports.default = DealsPromosTile;
