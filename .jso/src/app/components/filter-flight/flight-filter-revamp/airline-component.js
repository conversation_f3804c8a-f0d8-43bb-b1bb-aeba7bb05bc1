  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _flightFilterRevampStyles = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _searchComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _airlineRadioButton = _$$_REQUIRE(_dependencyMap[7]);
  var _flightFilterRevamp = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _translate = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AirlineComponent = function AirlineComponent(props) {
    var airlineList = props.airlineList,
      toogleCollapse = props.toogleCollapse,
      checkedAirlineState = props.checkedAirlineState,
      setCheckedAirlineState = props.setCheckedAirlineState,
      collapsedTypeSelect = props.collapsedTypeSelect;
    var flatListAirlineRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      airlineKeySearch = _useState2[0],
      setAirlineKeySearch = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      listAirlineSearch = _useState4[0],
      setAirlineListSearch = _useState4[1];
    var userTyping = (0, _react.useRef)(false);
    var previousListData = (0, _react.useRef)([]);
    var getAirlineItemView = function getAirlineItemView(index) {
      return {
        borderBottomWidth: index === airlineList.length - 1 ? 0 : 1
      };
    };
    var listAirlineData = (0, _react.useMemo)(function () {
      var _airlineKeySearch$tri;
      if ((airlineKeySearch == null || (_airlineKeySearch$tri = airlineKeySearch.trim()) == null ? undefined : _airlineKeySearch$tri.length) < 2) {
        previousListData.current = airlineList;
        return airlineList;
      } else {
        if (userTyping.current) {
          return previousListData.current;
        } else {
          previousListData.current = listAirlineSearch;
          return listAirlineSearch;
        }
      }
    }, [listAirlineSearch, airlineList, airlineKeySearch, userTyping.current, previousListData.current]);
    (0, _react.useEffect)(function () {
      if (collapsedTypeSelect === _flightFilterRevamp.COLLAPSE_VALUE.airline) {
        var airlineCheckedIndex = listAirlineData.findIndex(function (airlineElement) {
          return (airlineElement == null ? undefined : airlineElement.value) === checkedAirlineState;
        });
        if (airlineCheckedIndex !== -1) {
          setTimeout(function () {
            var _flatListAirlineRef$c;
            flatListAirlineRef == null || (_flatListAirlineRef$c = flatListAirlineRef.current) == null || _flatListAirlineRef$c.scrollToIndex({
              animated: true,
              index: airlineCheckedIndex
            });
          }, 500);
        }
      }
    }, [collapsedTypeSelect, listAirlineData]);
    (0, _react.useEffect)(function () {
      if (collapsedTypeSelect !== _flightFilterRevamp.COLLAPSE_VALUE.airline) {
        _reactNative.Keyboard.dismiss();
        setAirlineKeySearch("");
      }
    }, [collapsedTypeSelect]);
    var onSearchAirline = function onSearchAirline(keySearch) {
      var _keySearch$trim;
      if ((keySearch == null || (_keySearch$trim = keySearch.trim()) == null ? undefined : _keySearch$trim.length) >= 2) {
        userTyping.current = false;
        var _listAirlineSearch = airlineList == null ? undefined : airlineList.filter(function (item) {
          var _item$name;
          var itemName = item == null || (_item$name = item.name) == null ? undefined : _item$name.toLowerCase();
          return itemName == null ? undefined : itemName.includes(keySearch == null ? undefined : keySearch.toLowerCase());
        });
        previousListData.current = _listAirlineSearch;
        setAirlineListSearch(_listAirlineSearch);
      }
    };
    var onDebounceKeySearch = (0, _react.useCallback)((0, _lodash.debounce)(onSearchAirline, 2000), []);
    var renderAirlineItem = function renderAirlineItem(item, index) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [_flightFilterRevampStyles.styles.airlineItemView, getAirlineItemView(index)],
        children: (0, _jsxRuntime.jsx)(_airlineRadioButton.AirlineRadioButton, {
          text: item == null ? undefined : item.name,
          onPress: function onPress() {
            toogleCollapse("");
            setAirlineListSearch([]);
            setAirlineKeySearch("");
            setCheckedAirlineState(item.value);
          },
          imageUrl: item.image,
          outerContainer: item.value === checkedAirlineState ? _flightFilterRevampStyles.styles.outerRadioRingSelected : _flightFilterRevampStyles.styles.outerRadioRing,
          innerContainer: (item == null ? undefined : item.value) === checkedAirlineState ? _flightFilterRevampStyles.styles.innerRadioCircle : {}
        })
      });
    };
    var renderEmptyComponent = function renderEmptyComponent() {
      return (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "flyLocationFilter.noResults",
        style: _flightFilterRevampStyles.styles.noResultTextStyles
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _flightFilterRevampStyles.styles.airlineCollapsibleStyles,
      children: [(0, _jsxRuntime.jsx)(_searchComponent.default, {
        value: airlineKeySearch,
        placeHolderValue: (0, _translate.translate)("flyLocationFilter.airlineplaceHolder"),
        onChangeText: function onChangeText(text) {
          userTyping.current = true;
          setAirlineKeySearch(text);
          onDebounceKeySearch(text);
        },
        onSearchClear: function onSearchClear() {
          setAirlineListSearch([]);
          setAirlineKeySearch("");
        },
        onSubmitEditing: function onSubmitEditing() {
          return onSearchAirline(airlineKeySearch);
        },
        isShowClearAll: !(0, _lodash.isEmpty)(airlineKeySearch)
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _flightFilterRevampStyles.styles.spaceViewStyles
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        ref: flatListAirlineRef,
        data: listAirlineData,
        renderItem: function renderItem(_ref) {
          var item = _ref.item,
            index = _ref.index;
          return renderAirlineItem(item, index);
        },
        showsVerticalScrollIndicator: false,
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        },
        initialScrollIndex: 0,
        onScrollToIndexFailed: function onScrollToIndexFailed() {
          return null;
        },
        initialNumToRender: airlineList.length,
        ListEmptyComponent: renderEmptyComponent
      })]
    });
  };
  var _default = exports.default = AirlineComponent;
