  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeCollapsible = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text2 = _$$_REQUIRE(_dependencyMap[8]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CollapsibleComponent = function CollapsibleComponent(_ref) {
    var titleTx = _ref.titleTx,
      titleText = _ref.titleText,
      subTitle = _ref.subTitle,
      content = _ref.content,
      toogleCollapse = _ref.toogleCollapse,
      collapsed = _ref.collapsed,
      collapsibleHeaderStyles = _ref.collapsibleHeaderStyles;
    var animatedController = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var arrowAngle = animatedController.interpolate({
      inputRange: [0, 1],
      outputRange: ["0rad", `${Math.PI}rad`]
    });
    (0, _react.useEffect)(function () {
      if (collapsed) {
        _reactNative2.Animated.timing(animatedController, {
          duration: 300,
          toValue: 0,
          easing: _reactNative2.Easing.bezier(0.4, 0.0, 0.2, 1),
          useNativeDriver: false
        }).start();
      } else {
        _reactNative2.Animated.timing(animatedController, {
          duration: 300,
          toValue: 1,
          easing: _reactNative2.Easing.bezier(0.4, 0.0, 0.2, 1),
          useNativeDriver: false
        }).start();
      }
    }, [collapsed]);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: toogleCollapse,
        activeOpacity: 0.5,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [styles.collapsibleHeaderContainer, collapsibleHeaderStyles],
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: titleTx,
            text: titleText,
            style: styles.titleStyles
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.rightHeaderStyles,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: subTitle,
              style: styles.subTitleStyles,
              numberOfLines: 1
            }), (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
              style: {
                transform: [{
                  rotateZ: arrowAngle
                }]
              },
              children: (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
                color: _theme.color.palette.lightPurple
              })
            })]
          })]
        })
      }), (0, _jsxRuntime.jsx)(_reactNativeCollapsible.default, {
        collapsed: collapsed,
        children: content
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    collapsibleHeaderContainer: {
      alignItems: "center",
      flexDirection: "row",
      height: 56,
      paddingHorizontal: 24
    },
    rightHeaderStyles: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginLeft: 17
    },
    subTitleStyles: Object.assign({}, _text2.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey,
      flex: 1,
      fontSize: _responsive.default.getFontSize(14),
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: _responsive.default.getFontSize(18),
      marginRight: 8,
      textAlign: "right"
    }),
    titleStyles: Object.assign({}, _text2.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      })
    })
  });
  var _default = exports.default = CollapsibleComponent;
