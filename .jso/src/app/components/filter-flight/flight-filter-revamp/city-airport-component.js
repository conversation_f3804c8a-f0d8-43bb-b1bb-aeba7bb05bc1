  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _flightFilterRevampStyles = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _searchComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _airlineRadioButton = _$$_REQUIRE(_dependencyMap[7]);
  var _flightFilterRevamp = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CityAirportComponent = function CityAirportComponent(props) {
    var cityAirportList = props.cityAirportList,
      collapsedTypeSelect = props.collapsedTypeSelect,
      toogleCollapse = props.toogleCollapse,
      checkedCityAirportState = props.checkedCityAirportState,
      setCheckedCityAirportState = props.setCheckedCityAirportState;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      airportKeySearch = _useState2[0],
      setAirportKeySearch = _useState2[1];
    var _useState3 = (0, _react.useState)([]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      listCityAirportSearch = _useState4[0],
      setListCityAirportSearch = _useState4[1];
    var flatListCityAirportRef = (0, _react.useRef)(null);
    var userTyping = (0, _react.useRef)(false);
    var previousListData = (0, _react.useRef)([]);
    var listCityAirportData = (0, _react.useMemo)(function () {
      var _airportKeySearch$tri;
      if ((airportKeySearch == null || (_airportKeySearch$tri = airportKeySearch.trim()) == null ? undefined : _airportKeySearch$tri.length) < 2) {
        previousListData.current = cityAirportList;
        return cityAirportList;
      } else {
        if (userTyping.current) {
          return previousListData.current;
        } else {
          previousListData.current = listCityAirportSearch;
          return listCityAirportSearch;
        }
      }
    }, [listCityAirportSearch, cityAirportList, airportKeySearch, userTyping.current, previousListData.current]);
    (0, _react.useEffect)(function () {
      if (collapsedTypeSelect === _flightFilterRevamp.COLLAPSE_VALUE.cityAirport) {
        var cityAirportCheckedIndex = listCityAirportData.findIndex(function (cityAirportElement) {
          return (cityAirportElement == null ? undefined : cityAirportElement.value) === checkedCityAirportState;
        });
        if (cityAirportCheckedIndex !== -1) {
          setTimeout(function () {
            var _flatListCityAirportR;
            flatListCityAirportRef == null || (_flatListCityAirportR = flatListCityAirportRef.current) == null || _flatListCityAirportR.scrollToIndex({
              animated: true,
              index: cityAirportCheckedIndex
            });
          }, 500);
        }
      }
    }, [collapsedTypeSelect, listCityAirportData]);
    (0, _react.useEffect)(function () {
      if (collapsedTypeSelect !== _flightFilterRevamp.COLLAPSE_VALUE.cityAirport) {
        setAirportKeySearch("");
        _reactNative.Keyboard.dismiss();
      }
    }, [collapsedTypeSelect]);
    var onSearchCityAirport = function onSearchCityAirport(keySearch) {
      var _keySearch$trim;
      if ((keySearch == null || (_keySearch$trim = keySearch.trim()) == null ? undefined : _keySearch$trim.length) >= 2) {
        userTyping.current = false;
        var listAirlineSearch = cityAirportList == null ? undefined : cityAirportList.filter(function (item) {
          var _item$name;
          var itemName = item == null || (_item$name = item.name) == null ? undefined : _item$name.toLowerCase();
          return itemName == null ? undefined : itemName.includes(keySearch == null ? undefined : keySearch.toLowerCase());
        });
        previousListData.current = listAirlineSearch;
        setListCityAirportSearch(listAirlineSearch);
      }
    };
    var onDebounceKeySearch = (0, _react.useCallback)((0, _lodash.debounce)(onSearchCityAirport, 2000), []);
    var getCityAirportItemView = function getCityAirportItemView(index) {
      return {
        borderBottomWidth: index === cityAirportList.length - 1 ? 0 : 1
      };
    };
    var renderCityAirPortItem = function renderCityAirPortItem(item, index) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [_flightFilterRevampStyles.styles.cityAirportItemView, getCityAirportItemView(index)],
        children: (0, _jsxRuntime.jsx)(_airlineRadioButton.AirlineRadioButton, {
          text: (item == null ? undefined : item.name) || "",
          onPress: function onPress() {
            setAirportKeySearch("");
            setListCityAirportSearch([]);
            toogleCollapse("");
            setCheckedCityAirportState(item.value);
          },
          imageUrl: item.image,
          outerContainer: (item == null ? undefined : item.value) === checkedCityAirportState ? _flightFilterRevampStyles.styles.outerRadioRingSelected : _flightFilterRevampStyles.styles.outerRadioRing,
          innerContainer: (item == null ? undefined : item.value) === checkedCityAirportState ? _flightFilterRevampStyles.styles.innerRadioCircle : {}
        })
      });
    };
    var renderEmptyComponent = function renderEmptyComponent() {
      return (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "flyLocationFilter.noResults",
        style: _flightFilterRevampStyles.styles.noResultTextStyles
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _flightFilterRevampStyles.styles.cityAirportCollapsibleStyles,
      children: [(0, _jsxRuntime.jsx)(_searchComponent.default, {
        value: airportKeySearch,
        placeHolderValue: (0, _i18n.translate)("flyLocationFilter.cityAirportplaceHolder"),
        onChangeText: function onChangeText(text) {
          userTyping.current = true;
          setAirportKeySearch(text);
          onDebounceKeySearch(text);
        },
        onSearchClear: function onSearchClear() {
          setListCityAirportSearch([]);
          setAirportKeySearch("");
        },
        isShowClearAll: !(0, _lodash.isEmpty)(airportKeySearch),
        onSubmitEditing: function onSubmitEditing() {
          return onSearchCityAirport(airportKeySearch);
        }
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _flightFilterRevampStyles.styles.spaceViewStyles
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        ref: flatListCityAirportRef,
        data: listCityAirportData,
        renderItem: function renderItem(_ref) {
          var item = _ref.item,
            index = _ref.index;
          return renderCityAirPortItem(item, index);
        },
        showsVerticalScrollIndicator: false,
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        },
        initialScrollIndex: 0,
        onScrollToIndexFailed: function onScrollToIndexFailed() {
          return null;
        },
        initialNumToRender: cityAirportList.length,
        ListEmptyComponent: renderEmptyComponent
      })]
    });
  };
  var _default = exports.default = CityAirportComponent;
