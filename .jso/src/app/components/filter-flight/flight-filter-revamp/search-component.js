  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.SearchComponent = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _textField = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var closeIconStyles = {
    color: _theme.color.palette.darkGrey999
  };
  var SearchComponent = exports.SearchComponent = function SearchComponent(props) {
    var value = props.value,
      trim = props.trim,
      isDisabled = props.isDisabled,
      _onFocus = props.onFocus,
      _onBlur = props.onBlur,
      _onChangeText = props.onChangeText,
      onSearchClear = props.onSearchClear,
      placeHolderValue = props.placeHolderValue,
      _props$isShowClearAll = props.isShowClearAll,
      isShowClearAll = _props$isShowClearAll === undefined ? false : _props$isShowClearAll,
      onSubmitEditing = props.onSubmitEditing;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      focused = _useState2[0],
      setFocused = _useState2[1];
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: Object.assign({}, styles.inputShadowStyles, focused ? styles.inputShadowFocusedStyles : {}),
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: Object.assign({}, styles.inputGroupStyle, focused ? styles.inputGroupFocusedStyle : {}),
        children: [(0, _jsxRuntime.jsx)(_icons.SearchIconInActive, {
          width: "18",
          height: "18"
        }), (0, _jsxRuntime.jsx)(_textField.TextField, Object.assign({
          value: value,
          style: styles.inputFieldStyle,
          inputStyle: styles.inputControlStyle,
          placeholderTextColor: _theme.color.palette.midGrey,
          secureTextEntry: false,
          preset: "noMargin",
          editable: !isDisabled,
          returnKeyType: "search",
          autoCorrect: false,
          maxLength: 50,
          autoCapitalize: "none",
          allowFontScaling: false
        }, props, {
          onBlur: function onBlur(e) {
            setFocused(false);
            _onBlur && _onBlur(e);
          },
          onFocus: function onFocus(e) {
            setFocused(true);
            _onFocus && _onFocus(e);
          },
          onChangeText: function onChangeText(e) {
            var val = trim && e ? e.trim() : e;
            _onChangeText && _onChangeText(val);
          },
          onSubmitEditing: onSubmitEditing,
          placeholder: focused ? "" : placeHolderValue || null
        })), isShowClearAll && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.searchIconContainer,
          onPress: onSearchClear,
          children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
            fill: "currentColor",
            style: closeIconStyles
          })
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    inputControlStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      height: 40,
      marginHorizontal: 8,
      minHeight: 0,
      paddingBottom: 0
    }),
    inputFieldStyle: {
      flex: 1
    },
    inputGroupFocusedStyle: {
      borderColor: _theme.color.palette.lightPurple
    },
    inputGroupStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.greyCCCCCC,
      borderRadius: 8,
      borderWidth: 1,
      flexDirection: "row",
      height: 44,
      justifyContent: "center",
      paddingHorizontal: 12
    },
    inputShadowFocusedStyles: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 8,
      zIndex: -1
    },
    inputShadowStyles: {
      marginHorizontal: 20,
      padding: 4,
      zIndex: -1
    },
    searchIconContainer: {
      alignItems: "center",
      height: 44,
      justifyContent: "center"
    }
  });
  var _default = exports.default = SearchComponent;
