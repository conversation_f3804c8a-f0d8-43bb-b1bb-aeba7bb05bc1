  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.COLLAPSE_VALUE = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _flightFilterRevampStyles = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[11]);
  var _button = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _theme = _$$_REQUIRE(_dependencyMap[14]);
  var _collapsibleComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _checkbox = _$$_REQUIRE(_dependencyMap[16]);
  var _lodash = _$$_REQUIRE(_dependencyMap[17]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _airlineComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _cityAirportComponent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[23]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[24]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[25]);
  var _utils = _$$_REQUIRE(_dependencyMap[26]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[27]);
  var _adobe = _$$_REQUIRE(_dependencyMap[28]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[29]);
  var _flightApi = _$$_REQUIRE(_dependencyMap[30]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[31]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var activeGradient = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var COLLAPSE_VALUE = exports.COLLAPSE_VALUE = {
    terminal: "TERMINAL",
    airline: "AIRLINE",
    cityAirport: "CITY_AIRPORT"
  };
  var FlightFilterRevamp = function FlightFilterRevamp(props) {
    var dispatch = (0, _reactRedux.useDispatch)();
    var direction = props.direction,
      visible = props.visible,
      onClosedSheet = props.onClosedSheet,
      handleApplyFilter = props.handleApplyFilter,
      navigationType = props.navigationType,
      testID = props.testID,
      accessibilityLabel = props.accessibilityLabel;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var _useState3 = (0, _react.useState)(direction),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      directionState = _useState4[0],
      setDirectionState = _useState4[1];
    var _useState5 = (0, _react.useState)(""),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      collapsedTypeSelect = _useState6[0],
      setCollapsedSelect = _useState6[1];
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var getFlightFilterOptionError = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.getFlightFilterOptionError);
    var flightFilterOptions = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightFilterOptions);
    var flightFilterOptionSelected = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightFilterOptionSelected);
    var _useState7 = (0, _react.useState)([]),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      checkedTerminalState = _useState8[0],
      setCheckedTerminalState = _useState8[1];
    var _useState9 = (0, _react.useState)("all"),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      checkedAirlineState = _useState0[0],
      setCheckedAirlineState = _useState0[1];
    var _useState1 = (0, _react.useState)("all"),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      checkedCityAirportState = _useState10[0],
      setCheckedCityAirportState = _useState10[1];
    var _useState11 = (0, _react.useState)(1),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      opacity1 = _useState12[0],
      setOpacity1 = _useState12[1];
    var _useState13 = (0, _react.useState)(1),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      opacity2 = _useState14[0],
      setOpacity2 = _useState14[1];
    var airlineList = (0, _react.useMemo)(function () {
      return (flightFilterOptions == null ? undefined : flightFilterOptions.airline) || [];
    }, [flightFilterOptions]);
    var cityAirportList = (0, _react.useMemo)(function () {
      return (flightFilterOptions == null ? undefined : flightFilterOptions.cityAirport) || [];
    }, [flightFilterOptions]);
    var terminalList = (0, _react.useMemo)(function () {
      return (flightFilterOptions == null ? undefined : flightFilterOptions.terminal) || [];
    }, [flightFilterOptions]);
    (0, _react.useEffect)(function () {
      if (visible) {
        var checkInternet = /*#__PURE__*/function () {
          var _ref = (0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
              isConnected = _yield$NetInfo$fetch.isConnected;
            if (!isConnected) {
              setNoConnection(true);
            }
          });
          return function checkInternet() {
            return _ref.apply(this, arguments);
          };
        }();
        checkInternet();
      }
    }, [visible]);
    (0, _react.useEffect)(function () {
      if (visible && navigationType === _flightProps.NavigationType.FlightsLanding) {
        var terminalState = [];
        terminalList.forEach(function (terminal) {
          if (!(0, _lodash.isEmpty)(terminal == null ? undefined : terminal.value)) {
            terminalState.push(terminal == null ? undefined : terminal.value);
          }
        });
        setCheckedTerminalState(terminalState);
        setDirectionState(direction);
        setCheckedAirlineState("all");
        setCheckedCityAirportState("all");
      }
      if (visible && navigationType === _flightProps.NavigationType.FlightsResult) {
        setDirectionState(direction);
        if ((0, _lodash.isEmpty)(flightFilterOptionSelected.terminal) && flightFilterOptionSelected.airline === "all" && flightFilterOptionSelected.cityAirport === "all") {
          var _terminalState = [];
          terminalList.forEach(function (terminal) {
            if (!(0, _lodash.isEmpty)(terminal == null ? undefined : terminal.value)) {
              _terminalState.push(terminal == null ? undefined : terminal.value);
            }
          });
          setCheckedTerminalState(_terminalState);
          setCheckedAirlineState("all");
          setCheckedCityAirportState("all");
        } else {
          setCheckedTerminalState(flightFilterOptionSelected.terminal);
          setCheckedAirlineState(flightFilterOptionSelected.airline);
          setCheckedCityAirportState(flightFilterOptionSelected.cityAirport);
        }
      }
    }, [visible, navigationType, terminalList]);
    var terminalSubTitle = (0, _react.useMemo)(function () {
      if ((checkedTerminalState == null ? undefined : checkedTerminalState.length) === terminalList.length - 1) {
        return "All";
      } else {
        var listTerminalCheckedName = [];
        checkedTerminalState.forEach(function (checkedTerminalElement) {
          terminalList.forEach(function (terminalElement) {
            if ((terminalElement == null ? undefined : terminalElement.value) === checkedTerminalElement) {
              listTerminalCheckedName.push(terminalElement == null ? undefined : terminalElement.name);
            }
          });
        });
        return listTerminalCheckedName.join(", ");
      }
    }, [checkedTerminalState]);
    var airlineSubTitle = (0, _react.useMemo)(function () {
      var airlineChecked = airlineList == null ? undefined : airlineList.find(function (airlineElement) {
        return (airlineElement == null ? undefined : airlineElement.value) === checkedAirlineState;
      });
      return (airlineChecked == null ? undefined : airlineChecked.name) || "All";
    }, [checkedAirlineState]);
    var cityAirportSubTitle = (0, _react.useMemo)(function () {
      var cityAirportChecked = cityAirportList == null ? undefined : cityAirportList.find(function (cityAirportElement) {
        return (cityAirportElement == null ? undefined : cityAirportElement.value) === checkedCityAirportState;
      });
      return (cityAirportChecked == null ? undefined : cityAirportChecked.name) || "All";
    }, [checkedCityAirportState]);
    var _toogleCollapse = function toogleCollapse(filterType) {
      if (collapsedTypeSelect === filterType) {
        setCollapsedSelect("");
      } else {
        setCollapsedSelect(filterType);
      }
    };
    var onDirectionTogglePress = function onDirectionTogglePress(tab) {
      if (directionState !== tab) {
        setCollapsedSelect("");
        setDirectionState(tab);
      }
    };
    var getIconStyles = function getIconStyles(tab) {
      if (directionState === tab) {
        return _flightFilterRevampStyles.styles.iconActiveStyles;
      } else {
        return _flightFilterRevampStyles.styles.iconInActiveStyles;
      }
    };
    var getSwitchActiveButton = function getSwitchActiveButton(tab) {
      if (directionState === tab) {
        return _flightFilterRevampStyles.styles.switchActiveButton;
      } else {
        return {};
      }
    };
    var getDirectionTextStyles = function getDirectionTextStyles(tab) {
      if (directionState === tab) {
        return _flightFilterRevampStyles.styles.directionTextActiveStyles;
      } else {
        return _flightFilterRevampStyles.styles.directionTextInActiveStyles;
      }
    };
    var getTerminalItemView = function getTerminalItemView(index) {
      return {
        borderBottomWidth: index === terminalList.length - 1 ? 0 : 1
      };
    };
    var handleOnCheckedAll = function handleOnCheckedAll(value) {
      var terminalState = [];
      if (value) {
        terminalList.forEach(function (terminal) {
          if (!(0, _lodash.isEmpty)(terminal == null ? undefined : terminal.value)) {
            terminalState.push(terminal == null ? undefined : terminal.value);
          }
        });
        _toogleCollapse("");
      }
      setCheckedTerminalState(terminalState);
    };
    var handleOnCheckboxChange = function handleOnCheckboxChange(terminalItem) {
      var existTerminalIndex = checkedTerminalState == null ? undefined : checkedTerminalState.findIndex(function (terminalElement) {
        return terminalElement === (terminalItem == null ? undefined : terminalItem.value);
      });
      if (existTerminalIndex === -1) {
        var newCheckedTerminalState = [].concat((0, _toConsumableArray2.default)(checkedTerminalState), [terminalItem == null ? undefined : terminalItem.value]).sort();
        (newCheckedTerminalState == null ? undefined : newCheckedTerminalState.length) === terminalList.length - 1 && _toogleCollapse("");
        setCheckedTerminalState((0, _toConsumableArray2.default)(newCheckedTerminalState));
      } else {
        var _newCheckedTerminalState = checkedTerminalState.filter(function (item) {
          return item !== (terminalItem == null ? undefined : terminalItem.value);
        }).sort();
        setCheckedTerminalState((0, _toConsumableArray2.default)(_newCheckedTerminalState));
      }
    };
    var getCheckboxValue = function getCheckboxValue(item, index) {
      if (index === 0) {
        return (checkedTerminalState == null ? undefined : checkedTerminalState.length) === terminalList.length - 1;
      } else {
        return checkedTerminalState.includes(item == null ? undefined : item.value);
      }
    };
    var renderTerminalContent = function renderTerminalContent() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _flightFilterRevampStyles.styles.terminalCollapsibleStyles,
        children: terminalList.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: [_flightFilterRevampStyles.styles.terminalItemView, getTerminalItemView(index)],
            children: (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
              value: getCheckboxValue(item, index),
              onToggle: function onToggle(value) {
                if (index === 0) {
                  handleOnCheckedAll(value);
                  return;
                }
                handleOnCheckboxChange(item);
              },
              text: item == null ? undefined : item.name,
              textStyle: _flightFilterRevampStyles.styles.terminalTextItemStyles,
              outlineStyle: {
                borderColor: _theme.color.palette.darkGrey
              }
            })
          }, index.toString());
        })
      });
    };
    var onApplyFilter = function onApplyFilter() {
      var contextData = {
        buttonName: "Apply",
        direction: (0, _utils.simpleCondition)({
          condition: directionState === _flightProps.FlightDirection.arrival,
          ifValue: "Arrival",
          elseValue: "Departure"
        }),
        terminal: (0, _utils.simpleCondition)({
          condition: (0, _lodash.isEmpty)(terminalSubTitle),
          ifValue: "None",
          elseValue: terminalSubTitle
        }),
        airline: airlineSubTitle,
        cityAirport: cityAirportSubTitle
      };
      (0, _screenHelper.trackActionNewFormat)(_adobe.AdobeTagName.CAppFlightLandingFilter, contextData);
      _toogleCollapse("");
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      var filterOption = {
        direction: directionState,
        terminal: checkedTerminalState,
        airline: checkedAirlineState,
        cityAirport: checkedCityAirportState
      };
      handleApplyFilter(filterOption);
    };
    var handleOnClearFilter = function handleOnClearFilter() {
      var contextData = {
        buttonName: "Clear all",
        direction: (0, _utils.simpleCondition)({
          condition: directionState === _flightProps.FlightDirection.arrival,
          ifValue: "Arrival",
          elseValue: "Departure"
        }),
        terminal: (0, _utils.simpleCondition)({
          condition: (0, _lodash.isEmpty)(terminalSubTitle),
          ifValue: "None",
          elseValue: terminalSubTitle
        }),
        airline: airlineSubTitle,
        cityAirport: cityAirportSubTitle
      };
      (0, _screenHelper.trackActionNewFormat)(_adobe.AdobeTagName.CAppFlightLandingFilter, contextData);
      if (!(0, _lodash.isEmpty)(collapsedTypeSelect)) {
        _toogleCollapse("");
      }
      if ((checkedTerminalState == null ? undefined : checkedTerminalState.length) !== terminalList.length - 1) {
        var terminalState = [];
        terminalList.forEach(function (terminal) {
          if (!(0, _lodash.isEmpty)(terminal == null ? undefined : terminal.value)) {
            terminalState.push(terminal == null ? undefined : terminal.value);
          }
        });
        setCheckedTerminalState(terminalState);
      }
      setDirectionState(direction);
      setCheckedAirlineState("all");
      setCheckedCityAirportState("all");
    };
    var handleOncloseBottomSheet = function handleOncloseBottomSheet() {
      setCollapsedSelect("");
      onClosedSheet();
    };
    var onReloadData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        setNoConnection(!isConnected);
        if (isConnected) {
          (0, _flightApi.getFlightFilterOptions)();
        }
      });
      return function onReloadData() {
        return _ref2.apply(this, arguments);
      };
    }();
    var renderContent = function renderContent() {
      if (isNoConnection) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _flightFilterRevampStyles.styles.noInternetViewStyles,
          children: (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
            reload: true,
            header: false,
            headerBackgroundColor: "transparent",
            hideScreenHeader: false,
            visible: true,
            testID: `${testID}__ErrorOverlayNoConnection`,
            accessibilityLabel: `${accessibilityLabel}__ErrorOverlayNoConnection`,
            onReload: onReloadData,
            noInternetOverlayStyle: _flightFilterRevampStyles.styles.overlayStyle
          })
        });
      }
      if (getFlightFilterOptionError) {
        return (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          errorData: errorData,
          onPress: onReloadData,
          style: _flightFilterRevampStyles.styles.errorCloudComponentStyle,
          titleStyle: _flightFilterRevampStyles.styles.titleTextOfErrorCloudStyles,
          contentTextStyle: _flightFilterRevampStyles.styles.contentTextOfErrorCloudStyles,
          buttonStyle: _flightFilterRevampStyles.styles.buttonReloadOfErrorCloudStyles,
          testID: `${testID}__ErrorCloudComponent`,
          accessibilityLabel: `${accessibilityLabel}__ErrorCloudComponent`
        });
      }
      if ((0, _lodash.isEmpty)(flightFilterOptions)) return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _flightFilterRevampStyles.styles.noInternetViewStyles
      });
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _flightFilterRevampStyles.styles.switchFlightContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
            onPress: function onPress() {
              return onDirectionTogglePress(_flightProps.FlightDirection.arrival);
            },
            onPressIn: function onPressIn() {
              return setOpacity1(0.2);
            },
            onPressOut: function onPressOut() {
              return setOpacity1(1);
            },
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: [_flightFilterRevampStyles.styles.switchButton, {
                opacity: opacity1
              }, _flightFilterRevampStyles.styles.switchButtonLeft, getSwitchActiveButton(_flightProps.FlightDirection.arrival)],
              children: [(0, _jsxRuntime.jsx)(_icons.FlightArrivalIcon, {
                height: 20,
                width: 20,
                fill: "currentColor",
                style: getIconStyles(_flightProps.FlightDirection.arrival)
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "flyLocationFilter.arrivalToggle",
                style: [_flightFilterRevampStyles.styles.directionTextStyles, getDirectionTextStyles(_flightProps.FlightDirection.arrival)]
              })]
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
            onPress: function onPress() {
              return onDirectionTogglePress(_flightProps.FlightDirection.departure);
            },
            onPressIn: function onPressIn() {
              return setOpacity2(0.2);
            },
            onPressOut: function onPressOut() {
              return setOpacity2(1);
            },
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: [_flightFilterRevampStyles.styles.switchButton, {
                opacity: opacity2
              }, _flightFilterRevampStyles.styles.switchButtonRight, getSwitchActiveButton(_flightProps.FlightDirection.departure)],
              children: [(0, _jsxRuntime.jsx)(_icons.FlightDepartureIcon, {
                height: 20,
                width: 20,
                fill: "currentColor",
                style: getIconStyles(_flightProps.FlightDirection.departure)
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "flyLocationFilter.departureToggle",
                style: [_flightFilterRevampStyles.styles.directionTextStyles, getDirectionTextStyles(_flightProps.FlightDirection.departure)]
              })]
            })
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_collapsibleComponent.default, {
            titleTx: "flyLocationFilter.terminalTitle",
            subTitle: terminalSubTitle,
            content: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              children: renderTerminalContent()
            }),
            toogleCollapse: function toogleCollapse() {
              return _toogleCollapse(COLLAPSE_VALUE.terminal);
            },
            collapsed: collapsedTypeSelect === "" ? true : collapsedTypeSelect !== COLLAPSE_VALUE.terminal,
            collapsibleHeaderStyles: _flightFilterRevampStyles.styles.collapsibleHeaderStyles
          }), (0, _jsxRuntime.jsx)(_collapsibleComponent.default, {
            titleTx: "flyLocationFilter.airlineTitle",
            subTitle: airlineSubTitle,
            content: (0, _jsxRuntime.jsx)(_airlineComponent.default, {
              airlineList: airlineList,
              collapsedTypeSelect: collapsedTypeSelect,
              toogleCollapse: _toogleCollapse,
              checkedAirlineState: checkedAirlineState,
              setCheckedAirlineState: setCheckedAirlineState
            }),
            toogleCollapse: function toogleCollapse() {
              return _toogleCollapse(COLLAPSE_VALUE.airline);
            },
            collapsed: collapsedTypeSelect === "" ? true : collapsedTypeSelect !== COLLAPSE_VALUE.airline,
            collapsibleHeaderStyles: collapsedTypeSelect !== COLLAPSE_VALUE.airline && _flightFilterRevampStyles.styles.collapsibleHeaderStyles
          }), (0, _jsxRuntime.jsx)(_collapsibleComponent.default, {
            titleText: `${directionState === _flightProps.FlightDirection.arrival ? "From" : "To"} City/Airport`,
            subTitle: cityAirportSubTitle,
            content: (0, _jsxRuntime.jsx)(_cityAirportComponent.default, {
              cityAirportList: cityAirportList,
              collapsedTypeSelect: collapsedTypeSelect,
              toogleCollapse: _toogleCollapse,
              checkedCityAirportState: checkedCityAirportState,
              setCheckedCityAirportState: setCheckedCityAirportState
            }),
            toogleCollapse: function toogleCollapse() {
              return _toogleCollapse(COLLAPSE_VALUE.cityAirport);
            },
            collapsed: collapsedTypeSelect === "" ? true : collapsedTypeSelect !== COLLAPSE_VALUE.cityAirport,
            collapsibleHeaderStyles: collapsedTypeSelect !== COLLAPSE_VALUE.cityAirport && _flightFilterRevampStyles.styles.collapsibleHeaderStyles
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _flightFilterRevampStyles.styles.bottomFilterSheet,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _flightFilterRevampStyles.styles.bottomSheetContainer,
            children: [(0, _jsxRuntime.jsx)(_button.Button, {
              tx: "flyLocationFilter.clearAll",
              typePreset: "secondary",
              onPress: handleOnClearFilter,
              statePreset: "default",
              backgroundPreset: "light",
              textPreset: "buttonLarge",
              textStyle: _flightFilterRevampStyles.styles.clearFilterButtonStyle,
              style: _flightFilterRevampStyles.styles.clearButtonStyle,
              testID: `TouchableClearFilterLocations`,
              accessibilityLabel: `TouchableClearFilterLocations`
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _flightFilterRevampStyles.styles.parentContainer,
              children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                style: _flightFilterRevampStyles.styles.buttonLinearStyles,
                start: {
                  x: 0,
                  y: 1
                },
                end: {
                  x: 1,
                  y: 0
                },
                colors: activeGradient,
                children: (0, _jsxRuntime.jsx)(_button.Button, {
                  tx: "flyLocationFilter.apply",
                  sizePreset: "large",
                  textPreset: "buttonLarge",
                  typePreset: "secondary",
                  statePreset: "default",
                  backgroundPreset: "light",
                  onPress: onApplyFilter,
                  textStyle: _flightFilterRevampStyles.styles.applyTextStyles
                })
              })
            })]
          })
        })]
      });
    };
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: visible,
      onClosedSheet: handleOncloseBottomSheet,
      containerStyle: _flightFilterRevampStyles.styles.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: handleOncloseBottomSheet,
      animationInTiming: 200,
      animationOutTiming: 200,
      children: (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _flightFilterRevampStyles.styles.container,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _flightFilterRevampStyles.styles.headerFilterSheet,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              tx: "airportLanding.filter",
              style: _flightFilterRevampStyles.styles.titleFilterSheet
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: handleOncloseBottomSheet,
              style: _flightFilterRevampStyles.styles.btnCloseModalStyles,
              children: (0, _jsxRuntime.jsx)(_icons.CrossBlue, {
                width: 24,
                height: 24
              })
            })]
          }), renderContent()]
        })
      })
    });
  };
  var _default = exports.default = FlightFilterRevamp;
