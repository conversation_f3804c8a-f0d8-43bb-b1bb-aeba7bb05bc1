  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.AirlineRadioButton = AirlineRadioButton;
  exports.textStyles = exports.textContainerStyle = exports.radioOptionsInnerContainerStyle = exports.outerRadioRingStyle = exports.innerRadioCircleStyle = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _text2 = _$$_REQUIRE(_dependencyMap[7]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var outerRadioRingStyle = exports.outerRadioRingStyle = {
    height: 24,
    width: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: _theme.color.palette.lightPurple,
    alignItems: "center",
    justifyContent: "center"
  };
  var innerRadioCircleStyle = exports.innerRadioCircleStyle = {
    height: 12,
    width: 12,
    borderRadius: 6,
    backgroundColor: _theme.color.palette.lightPurple
  };
  var textStyles = exports.textStyles = Object.assign({}, _text2.presets.bodyTextRegular, {
    color: _theme.color.palette.almostBlackGrey
  });
  var textContainerStyle = exports.textContainerStyle = {
    marginLeft: 12,
    flex: 1
  };
  var radioOptionsInnerContainerStyle = exports.radioOptionsInnerContainerStyle = {
    flexDirection: "row",
    alignItems: "center"
  };
  var logoStyles = {
    height: 32,
    width: 32
  };
  var logoContainer = {
    height: 32,
    width: 32,
    borderRadius: 8,
    overflow: "hidden",
    marginLeft: 12
  };
  function AirlineRadioButton(props) {
    var outerCircle = props.outerContainer ? props.outerContainer : outerRadioRingStyle;
    var textContainer = props.textContainer ? props.textContainer : textContainerStyle;
    var text = props.text,
      tx = props.tx,
      imageUrl = props.imageUrl,
      onPress = props.onPress,
      disabled = props.disabled,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "AirlineRadioButton" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "AirlineRadioButton" : _props$accessibilityL;
    return (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
      style: Object.assign({}, radioOptionsInnerContainerStyle, props == null ? undefined : props.customStyle),
      onPress: onPress,
      disabled: disabled,
      testID: `${testID}__Pressable`,
      accessibilityLabel: `${accessibilityLabel}__Pressable`,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: outerCircle,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: props.innerContainer ? props.innerContainer : innerRadioCircleStyle
        })
      }), !(0, _lodash.isEmpty)(imageUrl) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: logoContainer,
        children: (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: imageUrl
          },
          resizeMode: "cover",
          style: logoStyles
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: textContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          tx: tx,
          text: text || tx,
          style: textStyles,
          numberOfLines: 1
        })
      })]
    });
  }
