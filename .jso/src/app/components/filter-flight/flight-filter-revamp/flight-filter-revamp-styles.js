  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var heightScreen = _reactNative.Dimensions.get("screen").height;
  var modalSheetHeight = heightScreen * 0.9 - (_reactNative.Platform.OS === "android" ? _reactNative.StatusBar.currentHeight : 0);
  var headerSheetHeight = 64;
  var toggleViewHeight = 52;
  var collapsibleHeaderHeight = 56;
  var bottomSheetHeight = 97;
  var contentCollapsibleHeight = modalSheetHeight - 156 - headerSheetHeight - collapsibleHeaderHeight - bottomSheetHeight;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    airlineCollapsibleStyles: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1,
      height: contentCollapsibleHeight
    },
    airlineItemView: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      height: 64,
      justifyContent: "center",
      marginHorizontal: 24
    },
    applyTextStyles: {
      color: _theme.color.palette.almostWhiteGrey
    },
    bottomFilterSheet: {
      backgroundColor: _theme.color.palette.whiteGrey,
      paddingHorizontal: 24
    },
    bottomSheetContainer: {
      flexDirection: "row",
      marginBottom: 37,
      marginTop: 16
    },
    bottomSheetStyle: {
      justifyContent: "flex-end",
      margin: 0
    },
    btnCloseModalStyles: {
      position: "absolute",
      right: 16
    },
    buttonLinearStyles: {
      borderRadius: 60,
      width: "100%"
    },
    buttonReloadOfErrorCloudStyles: {
      marginBottom: 63,
      marginTop: 24,
      width: "auto"
    },
    cityAirportCollapsibleStyles: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1,
      height: contentCollapsibleHeight
    },
    cityAirportItemView: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      height: 56,
      justifyContent: "center",
      marginHorizontal: 24
    },
    clearButtonStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 60,
      height: 44,
      marginRight: 12
    },
    clearFilterButtonStyle: {
      color: _theme.color.palette.gradientColor1Start,
      fontSize: 16,
      fontStyle: "normal",
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 24,
      paddingHorizontal: 16,
      textAlign: "center"
    },
    collapsibleHeaderStyles: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1
    },
    container: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      maxHeight: modalSheetHeight,
      width: "100%"
    },
    contentTextOfErrorCloudStyles: {
      marginHorizontal: 62,
      paddingHorizontal: 0,
      width: "auto"
    },
    directionTextActiveStyles: {
      color: _theme.color.palette.lightPurple
    },
    directionTextInActiveStyles: {
      color: _theme.color.palette.greyCCCCCC
    },
    directionTextStyles: Object.assign({}, _text.presets.bodyTextBold, {
      marginLeft: 4
    }),
    errorCloudComponentStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      height: "auto"
    },
    headerFilterSheet: {
      alignItems: "center",
      flexDirection: "row",
      height: 64,
      justifyContent: "center",
      width: "100%"
    },
    iconActiveStyles: {
      color: _theme.color.palette.lightPurple
    },
    iconInActiveStyles: {
      color: _theme.color.palette.greyCCCCCC
    },
    innerRadioCircle: {
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 6,
      height: 12,
      width: 12
    },
    loadingButtonStyles: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightGrey,
      borderRadius: 60,
      height: 44,
      justifyContent: "center",
      width: "100%"
    },
    lottieView: {
      height: 24
    },
    noInternetViewStyles: {
      height: _reactNative.Dimensions.get("window").height * 0.6
    },
    noResultTextStyles: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      marginHorizontal: 24
    }),
    outerRadioRing: {
      alignItems: "center",
      borderColor: _theme.color.palette.darkGrey,
      borderRadius: 12,
      borderWidth: 2,
      height: 24,
      justifyContent: "center",
      width: 24
    },
    outerRadioRingSelected: {
      alignItems: "center",
      borderColor: _theme.color.palette.lightPurple,
      borderRadius: 12,
      borderWidth: 2,
      height: 24,
      justifyContent: "center",
      width: 24
    },
    overlayStyle: {
      height: "100%",
      paddingBottom: headerSheetHeight,
      width: "100%"
    },
    parentContainer: {
      flex: 1
    },
    spaceViewStyles: {
      height: 16,
      width: "100%"
    },
    switchActiveButton: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        }
      },
      android: {
        elevation: 3
      }
    })),
    switchButton: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center"
    },
    switchButtonLeft: {
      margin: 4,
      marginRight: 0,
      borderRadius: 63
    },
    switchButtonRight: {
      margin: 4,
      marginLeft: 0,
      borderRadius: 63
    },
    switchFlightContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderRadius: 52,
      flexDirection: "row",
      height: 44,
      marginHorizontal: 48.5
    },
    terminalCollapsibleStyles: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1
    },
    terminalItemView: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      marginHorizontal: 24,
      paddingVertical: 16
    },
    terminalTextItemStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: "400"
    }),
    titleFilterSheet: Object.assign({
      color: _theme.color.palette.almostBlackGrey
    }, _text.presets.subTitleBold, {
      flexGrow: 2,
      textAlign: "center"
    }),
    titleTextOfErrorCloudStyles: {
      marginTop: 20
    }
  });
