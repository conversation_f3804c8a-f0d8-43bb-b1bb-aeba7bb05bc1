  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.InstagramTrending = InstagramTrending;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _hashtag = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _instagramTrending = _$$_REQUIRE(_dependencyMap[10]);
  var _instagram = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var containerStyle = {
    width: width - 15,
    alignSelf: "center"
  };
  var contentContainerStyle = {
    paddingVertical: 40
  };
  var imageStyle = {
    height: 75,
    width: 75,
    borderRadius: 16,
    marginRight: 12
  };
  var hashTagStyle = {
    marginLeft: 8
  };
  var titleStyle = {
    left: 24,
    lineHeight: 22,
    width: "90%",
    color: _theme.color.palette.almostBlackGrey
  };
  var imagesFlatListStyle = {
    marginHorizontal: 24,
    paddingRight: 48,
    marginTop: 16
  };
  var hashtagFlatListStyle = {
    marginHorizontal: 24,
    paddingRight: 48
  };
  var instagramIconStyle = {
    marginLeft: 28
  };
  var hashtagContainerStyle = {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12
  };
  var hashtagItemStyle = {
    flexDirection: "row",
    alignItems: "center"
  };
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonImageStyle = {
    flexDirection: "row",
    paddingLeft: 24,
    marginTop: 16
  };
  var skeletonHashtagStyle = {
    flexDirection: "row"
  };
  var skeletonLayoutImage = [{
    height: 75,
    width: 75,
    borderRadius: 16,
    marginRight: 12
  }, {
    height: 75,
    width: 75,
    borderRadius: 16,
    marginRight: 12
  }, {
    height: 75,
    width: 75,
    borderRadius: 16,
    marginRight: 12
  }, {
    height: 75,
    width: 75,
    borderRadius: 16,
    marginRight: 12
  }];
  var skeletonLayoutHashtag = [{
    width: 132,
    height: 30,
    borderRadius: 8,
    marginLeft: 10.63
  }, {
    width: 164,
    height: 30,
    borderRadius: 8,
    marginLeft: 8
  }, {
    width: 8,
    height: 30,
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    marginLeft: 8
  }];
  var renderImageView = function renderImageView(_ref, onImageClick) {
    var item = _ref.item;
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: function onPress() {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineSocial, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineSocial, "1"));
        onImageClick(item);
      },
      children: (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: item.imageUrl
        },
        style: imageStyle
      })
    }, item.id);
  };
  var renderHashtagView = function renderHashtagView(_ref2, onHashtagClick) {
    var item = _ref2.item,
      index = _ref2.index;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: hashtagItemStyle,
      children: [index === 0 && (0, _jsxRuntime.jsx)(_instagram.default, {}), (0, _jsxRuntime.jsx)(_hashtag.Hashtag, {
        text: item.text,
        style: hashTagStyle,
        onClickHashTag: function onClickHashTag() {
          return onHashtagClick(item);
        }
      })]
    }, item.id);
  };
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: skeletonImageStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[0]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[2]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[3]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: hashtagContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_instagram.default, {
          style: instagramIconStyle
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: skeletonHashtagStyle,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: skeletonLayoutHashtag[0]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: skeletonLayoutHashtag[1]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: skeletonLayoutHashtag[2]
          })]
        })]
      })]
    });
  };
  var defaultView = function defaultView(props) {
    var _ref3, _ref4;
    var imageUrls = props.imageUrls,
      hashtags = props.hashtags,
      onHashtagClick = props.onHashtagClick,
      onImageClick = props.onImageClick;
    var sortedImageUrls = (_ref3 = (0, _toConsumableArray2.default)(imageUrls)) == null ? undefined : _ref3.sort(function (a, b) {
      return a.id - b.id;
    });
    var sortedHashTags = (_ref4 = (0, _toConsumableArray2.default)(hashtags)) == null ? undefined : _ref4.sort(function (a, b) {
      return a.id - b.id;
    });
    var isScrollEnabled = sortedImageUrls.length === 4;
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        data: sortedImageUrls,
        contentContainerStyle: imagesFlatListStyle,
        renderItem: function renderItem(item) {
          return renderImageView(item, onImageClick);
        },
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        keyExtractor: function keyExtractor(item) {
          return item.id.toString();
        },
        scrollEnabled: !isScrollEnabled
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: hashtagContainerStyle,
        children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          contentContainerStyle: hashtagFlatListStyle,
          data: sortedHashTags,
          renderItem: function renderItem(item) {
            return renderHashtagView(item, onHashtagClick);
          },
          horizontal: true,
          showsHorizontalScrollIndicator: false,
          keyExtractor: function keyExtractor(item) {
            return item.id.toString();
          }
        })
      })]
    });
  };
  function InstagramTrending(props) {
    var isLoading = _instagramTrending.InstagramTrendingType.loading === props.type;
    var backgroundColor = {
      backgroundColor: _theme.color.palette.whiteGrey
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: Object.assign({}, containerStyle, backgroundColor),
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: contentContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          text: props.title,
          numberOfLines: 2,
          style: titleStyle
        }), isLoading ? loadingView() : defaultView(props)]
      })
    });
  }
