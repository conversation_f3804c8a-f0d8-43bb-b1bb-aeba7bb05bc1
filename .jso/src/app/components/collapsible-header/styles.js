  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.FILTER_BORDER_RADIUS = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var FILTER_BORDER_RADIUS = exports.FILTER_BORDER_RADIUS = 16;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1
    },
    headerImageContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      overflow: 'hidden'
    },
    backgroundImage: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      width: '100%',
      resizeMode: 'cover'
    },
    headerBar: Object.assign({
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      zIndex: 1
    }, _theme.shadow.filterHeaderShadow, {
      shadowColor: _reactNative.Platform.select({
        android: _theme.color.palette.almostBlackGrey,
        ios: "rgba(18, 18, 18, 0.08)"
      })
    }),
    title: Object.assign({}, _text.newPresets.subTitleBold),
    filterContainer: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: -FILTER_BORDER_RADIUS,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      overflow: "hidden"
    },
    backBtn: {
      width: 28
    },
    placeholder: {
      width: 28
    }
  });
