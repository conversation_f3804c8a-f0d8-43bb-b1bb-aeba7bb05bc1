  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _exportNames = {};
  Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function get() {
      return _CollapsibleHeader.default;
    }
  });
  var _CollapsibleHeader = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  Object.keys(_CollapsibleHeader).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
    if (key in exports && exports[key] === _CollapsibleHeader[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _CollapsibleHeader[key];
      }
    });
  });
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
