  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _styles = _$$_REQUIRE(_dependencyMap[6]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _adobe = _$$_REQUIRE(_dependencyMap[10]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[11]);
  var _lodash = _$$_REQUIRE(_dependencyMap[12]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[13]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _utils = _$$_REQUIRE(_dependencyMap[16]);
  var _envParams = _$$_REQUIRE(_dependencyMap[17]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[18]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _flyDetailSubscription = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _subscriptions = _$$_REQUIRE(_dependencyMap[21]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[22]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[23]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[24]);
  var _flyHelper = _$$_REQUIRE(_dependencyMap[25]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[26]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BASE_STYLE = {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginLeft: 4
  };
  var greenBackgroundStyle = Object.assign({}, BASE_STYLE, {
    backgroundColor: _theme.color.palette.lightestGreen
  });
  var greenStatusText = Object.assign({}, _text.presets.caption2Bold, {
    paddingBottom: 1,
    color: _theme.color.palette.basegreen
  });
  var redBackgroundStyle = Object.assign({}, BASE_STYLE, {
    backgroundColor: _theme.color.palette.lightestRed
  });
  var redStatusText = Object.assign({}, _text.presets.caption2Bold, greenStatusText, {
    color: _theme.color.palette.baseRed
  });
  var blackBackgroundStyle = Object.assign({}, BASE_STYLE, {
    backgroundColor: _theme.color.palette.lighterGrey
  });
  var blackStatusText = Object.assign({}, _text.presets.caption2Bold, greenStatusText, {
    color: _theme.color.palette.almostBlackGrey
  });
  var handleTimeLeft = function handleTimeLeft(item, isRetimedOrDelayed) {
    var _ref = item || "",
      displayTimestamp = _ref.displayTimestamp;
    var currentTimestamp = (0, _dateTime.getCurrentTimeSingapore)();
    // Change to always use "displayTimestamp" instead of. Comment to revert code to use "time" if need.
    var diffTime = (0, _moment.default)(displayTimestamp).diff(currentTimestamp, "minutes");
    return (0, _flyHelper.mappingTimeWithFlightText)(item == null ? undefined : item.direction, diffTime, item);
  };
  var SavedFlightCardUpcomingEvent = function SavedFlightCardUpcomingEvent(_ref2) {
    var _myTravelFlightsPaylo2;
    var navigation = _ref2.navigation,
      item = _ref2.item,
      isSingle = _ref2.isSingle,
      index = _ref2.index,
      isFirstOfList = _ref2.isFirstOfList;
    var isRetimedOrDelayed = (item == null ? undefined : item.upcomingStatusMapping) && ((item == null ? undefined : item.upcomingStatusMapping.includes("Re-timed")) || (item == null ? undefined : item.upcomingStatusMapping.includes("Delayed")));
    var isDEP = (item == null ? undefined : item.direction) === "DEP";
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      updatedStatus = _useState2[0],
      setUpdatedStatus = _useState2[1];
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_EXPLORE_SWIMLANE),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance,
      errorData = _useTickerbandMaintan.errorData;
    var errorSection = errorData != null && errorData.length ? errorData.find(function (el) {
      return el.code === _errorOverlay.ERROR_HANDLING_CODE.ERROR_SECTION_LEVEL;
    }) : null;
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var isDisplayMaintenance = (0, _react.useMemo)(function () {
      var _myTravelFlightsPaylo;
      return !(0, _lodash.isEmpty)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) && (myTravelFlightsPayload == null || (_myTravelFlightsPaylo = myTravelFlightsPayload.getMyTravelFlightDetails) == null ? undefined : _myTravelFlightsPaylo.length) > 0 && isShowMaintenance;
    }, [myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails, myTravelFlightsPayload == null || (_myTravelFlightsPaylo2 = myTravelFlightsPayload.getMyTravelFlightDetails) == null ? undefined : _myTravelFlightsPaylo2.length, isShowMaintenance]);
    var onPress = function onPress() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeBookingTiles, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeBookingTiles, `${item == null ? undefined : item.flightNumber} to ${item == null ? undefined : item.destinationPlace}|Flight`));
      navigation.navigate("flightDetails", {
        payload: {
          item: Object.assign({}, item, {
            isSaved: true,
            flightDate: item == null ? undefined : item.scheduledDate,
            flightTime: item == null ? undefined : item.scheduledTime,
            flightUniqueId: item != null && item.flightUniqueId ? item == null ? undefined : item.flightUniqueId : `${item == null ? undefined : item.flightNumber}_${item == null ? undefined : item.scheduledDate}`
          })
        },
        direction: (item == null ? undefined : item.departingCode) === "SIN" ? _flightProps.FlightDirection.departure : _flightProps.FlightDirection.arrival || (item == null ? undefined : item.flightDirection),
        isFromUpcomingEvent: true
      });
    };
    var handleDateTimeText = function handleDateTimeText() {
      var scheduleDateToDisplay = (0, _moment.default)(item == null ? undefined : item.scheduledDate).format(_dateTime.DateFormats.DateMonth);
      var scheduleTimeToDisplay = (0, _moment.default)((item == null ? undefined : item.scheduledDate) + " " + (item == null ? undefined : item.scheduledTime)).format(_dateTime.TimeFormats["24Hours"]);
      var reTimeFlag = false;
      var numberDaysDiff = 0;
      var newTime = "";
      if (item != null && item.displayTimestamp) {
        var _item$displayTimestam, _item$displayTimestam2;
        var mainTime = (_item$displayTimestam = item.displayTimestamp) == null ? undefined : _item$displayTimestam.split(" ")[1];
        var mainDate = (_item$displayTimestam2 = item.displayTimestamp) == null ? undefined : _item$displayTimestam2.split(" ")[0];
        if (item.scheduledDate !== mainDate || item.scheduledTime !== mainTime) {
          reTimeFlag = true;
          numberDaysDiff = (0, _moment.default)(mainDate).diff((0, _moment.default)(item.scheduledDate), "days");
          newTime = (0, _moment.default)(item == null ? undefined : item.displayTimestamp).format(_dateTime.TimeFormats["24Hours"]);
        }
      }
      if (reTimeFlag) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.wrapHandleDateTimeText,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: `${scheduleDateToDisplay}, `,
            preset: "caption2Regular",
            style: _styles.styles.dateTimeStyle,
            numberOfLines: 1
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: `${scheduleTimeToDisplay}`,
              preset: "caption2Regular",
              style: Object.assign({}, _styles.styles.dateTimeStyle),
              numberOfLines: 1
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.styles.lineThroughStyle
            })]
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: `${newTime}`,
            preset: "caption2Regular",
            style: Object.assign({}, _styles.styles.dateTimeStyleWithMargin),
            numberOfLines: 1
          }), !!numberDaysDiff && (0, _jsxRuntime.jsx)(_text.Text, {
            text: numberDaysDiff > 0 ? `(+${numberDaysDiff})` : `(${numberDaysDiff})`,
            preset: "caption2Regular",
            style: _styles.styles.diffNumberTimeStyle,
            numberOfLines: 1
          })]
        });
      }
      return (0, _jsxRuntime.jsx)(_text.Text, {
        text: `${scheduleDateToDisplay}, ${scheduleTimeToDisplay}`,
        preset: "caption2Regular",
        style: _styles.styles.dateTimeStyle,
        numberOfLines: 1
      });
    };
    var calculateDiffTime = function calculateDiffTime(time) {
      var hours = time.split(":")[0];
      var minutes = time.split(":")[1];
      var currentTime = (0, _momentTimezone.default)().tz("Asia/Singapore");
      var checkgateTimeSg = (0, _momentTimezone.default)().tz("Asia/Singapore");
      checkgateTimeSg.hours(hours);
      checkgateTimeSg.minutes(minutes);
      var diff = (0, _momentTimezone.default)(checkgateTimeSg).diff(currentTime, "milliseconds");
      return diff;
    };
    var flightStatusTag = function flightStatusTag() {
      var _item$statusColor;
      var statusTag = item == null ? undefined : item.upcomingStatusMapping;
      if (!statusTag) return null;
      var beltStatusMapping = item == null ? undefined : item.beltStatusMapping;
      var statusColor = item == null || (_item$statusColor = item.statusColor) == null ? undefined : _item$statusColor.toLowerCase();
      var status = statusTag == null ? undefined : statusTag.toLowerCase();
      if (!(0, _lodash.isEmpty)(beltStatusMapping)) {
        if (["grey", "black"].includes(statusColor)) {
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: blackBackgroundStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Bold",
              style: Object.assign({}, blackStatusText, {
                color: statusColor
              }),
              children: beltStatusMapping
            })
          });
        }
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: greenBackgroundStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            style: Object.assign({}, greenStatusText, {
              color: statusColor
            }),
            children: beltStatusMapping
          })
        });
      }
      switch (true) {
        case /gate open/gim.test(status):
        case /boarding/gim.test(status):
        case /landed/gim.test(status):
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: greenBackgroundStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Bold",
              style: Object.assign({}, greenStatusText, {
                color: statusColor
              }),
              children: statusTag
            })
          });
        case /gate closed/gim.test(status):
        case /last call/gim.test(status):
        case /gate closing/gim.test(status):
        case /cancelled/gim.test(status):
        case /re-timed/gim.test(status):
        case /delayed/gim.test(status):
        case /diverted/gim.test(status):
        case /new gate/gim.test(status):
        case /go to info counter/gim.test(status):
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: redBackgroundStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Bold",
              style: Object.assign({}, redStatusText, {
                color: statusColor
              }),
              children: statusTag
            })
          });
        default:
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: blackBackgroundStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Bold",
              style: Object.assign({}, blackStatusText, {
                color: statusColor
              }),
              children: updatedStatus ? updatedStatus : statusTag
            })
          });
      }
    };
    var contentTimeLeft = function contentTimeLeft() {
      var _item$upcomingStatusM;
      var statusNeedIgnore = ["cancelled", "go to info counter", "diverted"];
      if (statusNeedIgnore != null && statusNeedIgnore.includes(item == null || (_item$upcomingStatusM = item.upcomingStatusMapping) == null ? undefined : _item$upcomingStatusM.toLowerCase())) {
        return null;
      }
      var timeLeft = handleTimeLeft(item, isRetimedOrDelayed);
      return !(0, _lodash.isEmpty)(timeLeft) && (0, _jsxRuntime.jsx)(_text.Text, {
        text: timeLeft,
        preset: "caption2Bold",
        numberOfLines: 1
      });
    };
    (0, _react.useEffect)(function () {
      var upcomingStatusMapping = item.upcomingStatusMapping;
      var isStatusCheckGate = upcomingStatusMapping == null ? undefined : upcomingStatusMapping.includes("Check Gate");
      if (isStatusCheckGate) {
        //Check Gate at: length = 13
        var checkgateTime = upcomingStatusMapping == null ? undefined : upcomingStatusMapping.substring(13).trim();
        var diffTime = calculateDiffTime(checkgateTime);
        if (diffTime > 0) {
          setTimeout(function () {
            setUpdatedStatus("On Schedule");
          }, diffTime);
        } else {
          setUpdatedStatus("On Schedule");
        }
      } else {
        setUpdatedStatus("");
      }
    }, []);
    var MaintanenceCard = (0, _react.useMemo)(function () {
      var _env;
      if (!isFirstOfList) {
        return null;
      }
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [(0, _utils.handleCondition)(isSingle, _styles.styles.containerFullWidth, _styles.styles.container), _styles.styles.cardErrorContainer],
        children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _styles.styles.cardErrorImageContainer,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 0
          },
          colors: ["#F3F0F7", "#FCFCFC"],
          children: (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: `${(_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL}${errorSection == null ? undefined : errorSection.icon}`
            },
            resizeMode: "contain",
            style: _styles.styles.cardErrorImage
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.cardErrorTextContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "incomingCardError.title",
            style: _styles.styles.incomingCardErrorTitle
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "incomingCardError.description",
            style: _styles.styles.incomingCardErrorDescription
          })]
        })]
      });
    }, [errorSection == null ? undefined : errorSection.icon, isSingle, index]);
    if (isDisplayMaintenance) {
      return MaintanenceCard;
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: onPress,
      style: [(0, _utils.handleCondition)(isSingle, _styles.styles.containerFullWidth, _styles.styles.container), {
        flexDirection: "row"
      }],
      activeOpacity: 0.5,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.Image, {
        source: (0, _utils.handleCondition)(isDEP, _$$_REQUIRE(_dependencyMap[27]), _$$_REQUIRE(_dependencyMap[28])),
        style: _styles.styles.imageFlightStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.wrapFlightInfor,
        children: [handleDateTimeText(), (0, _jsxRuntime.jsx)(_text.Text, {
          text: (0, _utils.handleCondition)(isDEP, item == null ? undefined : item.destinationPlace, item == null ? undefined : item.departingPlace),
          preset: "subTitleBold",
          style: _styles.styles.destinationPlaceStyle,
          numberOfLines: 1
        }), contentTimeLeft(), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.wrapFlightNumberAndStatusStyle,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            style: _styles.styles.logoStyle,
            source: {
              uri: item == null ? undefined : item.logo
            }
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: item == null ? undefined : item.flightNumber,
            style: _styles.styles.flightNumberStyle,
            preset: "caption2Regular"
          }), flightStatusTag()]
        })]
      }), (0, _jsxRuntime.jsx)(_flyDetailSubscription.default, {
        direction: item == null ? undefined : item.flightDirection,
        flightNumber: item == null ? undefined : item.flightNumber,
        scheduledDate: item == null ? undefined : item.scheduledDate,
        screen: _subscriptions.FlySubscriptionScreenEnum.upComingSaveFlight
      })]
    });
  };
  var _default = exports.default = SavedFlightCardUpcomingEvent;
