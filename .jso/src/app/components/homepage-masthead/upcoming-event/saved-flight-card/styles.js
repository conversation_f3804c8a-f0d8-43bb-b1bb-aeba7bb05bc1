  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var WIDTH_SINGLE_ITEM = _reactNative.Dimensions.get("screen").width - 48;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      height: 114,
      marginRight: 16,
      // overflow: "hidden",
      paddingRight: 10,
      width: _reactNative.Dimensions.get("screen").width * 0.75
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 5,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 1
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    containerFullWidth: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      height: 114,
      marginRight: 16,
      overflow: "hidden",
      paddingRight: 10,
      width: WIDTH_SINGLE_ITEM
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 5,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 1
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    content: {
      backgroundColor: _theme.color.palette.whiteGrey,
      flexDirection: "row"
    },
    dateTimeStyle: {
      color: _theme.color.palette.almostBlackGrey
    },
    dateTimeStyleWithMargin: {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 2
    },
    destinationPlaceStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 2
    },
    diffNumberTimeStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 2
    },
    flightNumberStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 4
    },
    imageFlightStyle: {
      height: 114,
      marginRight: 12,
      width: 62
    },
    lineThroughStyle: {
      backgroundColor: _theme.color.palette.baseRed,
      height: 2,
      position: "absolute",
      top: 8,
      width: "100%"
    },
    logoStyle: {
      borderRadius: 4,
      height: 24,
      overflow: "hidden",
      width: 24
    },
    wrapFlightInfor: {
      flex: 1,
      marginBottom: 8,
      marginRight: 8,
      marginTop: 12
    },
    wrapFlightNumberAndStatusStyle: {
      alignItems: "center",
      flexDirection: "row",
      marginTop: 8
    },
    wrapHandleDateTimeText: {
      alignItems: "center",
      flexDirection: 'row',
      marginBottom: 2
    },
    cardErrorImage: {
      width: 64,
      height: 54
    },
    cardErrorTextContainer: {
      flex: 1,
      marginLeft: 16
    },
    cardErrorImageContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: 16,
      borderRadius: 16
    },
    cardErrorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 16
    },
    incomingCardErrorTitle: {
      fontFamily: "Lato-Bold",
      fontSize: 16,
      lineHeight: 20,
      color: _theme.color.palette.almostBlackGrey
    },
    incomingCardErrorDescription: {
      marginTop: 2,
      fontFamily: "Lato-Regular",
      fontSize: 16,
      lineHeight: 20,
      color: _theme.color.palette.darkestGrey
    }
  });
