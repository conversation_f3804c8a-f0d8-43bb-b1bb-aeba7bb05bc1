  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _styles = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var UpComingEventsViewMoreButtons = function UpComingEventsViewMoreButtons(_ref) {
    var handleGlobalNavigate = _ref.handleGlobalNavigate;
    var onPressViewAllSavedFlights = function onPressViewAllSavedFlights() {
      handleGlobalNavigate(_constants.NavigationConstants.saveFlightsScreen);
    };
    var onPressViewAllBookings = function onPressViewAllBookings() {
      handleGlobalNavigate(_constants.NavigationConstants.bookingsOrdersScreen);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.wrapper,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _styles.styles.buttonWrapper,
        onPress: onPressViewAllSavedFlights,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: "All Saved Flights",
          preset: "caption2Bold"
        }), (0, _jsxRuntime.jsx)(_icons.ArrowRight, {
          width: 16,
          style: _styles.styles.buttonRightIcon
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.divider
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _styles.styles.buttonWrapper,
        onPress: onPressViewAllBookings,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: "All Bookings",
          preset: "caption2Bold"
        }), (0, _jsxRuntime.jsx)(_icons.ArrowRight, {
          width: 16,
          style: _styles.styles.buttonRightIcon
        })]
      })]
    });
  };
  var _default = exports.default = UpComingEventsViewMoreButtons;
