  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    wrapper: Object.assign({
      paddingTop: 14,
      paddingLeft: 12,
      paddingRight: 12,
      paddingBottom: 14,
      borderRadius: 16,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    }, _theme.shadow.primaryShadow),
    divider: {
      height: 1,
      width: "100%",
      marginTop: 12,
      marginBottom: 12,
      backgroundColor: _theme.color.palette.lighterGrey
    },
    buttonWrapper: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between"
    },
    buttonRightIcon: {
      width: 20,
      height: 20,
      marginLeft: 10
    }
  });
