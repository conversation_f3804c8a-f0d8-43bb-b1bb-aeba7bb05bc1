  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _color = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var widthScreen = _reactNative.Dimensions.get("window").width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    btnViewStyles: {
      alignItems: "center",
      flexDirection: "row"
    },
    buttonCardTextStyles: Object.assign({}, _text.presets.caption1Bold, {
      color: _color.color.palette.lightPurple
    }),
    contentFlatlistStyles: {
      alignItems: "center",
      paddingHorizontal: 24
    },
    flatlistStyles: {
      bottom: -28,
      height: 128,
      opacity: 1,
      position: "absolute",
      width: "100%"
    },
    imageStyles: {
      borderRadius: 12,
      height: 80,
      width: 80
    },
    itemUpcomingEventStyles: Object.assign({
      alignItems: "center",
      backgroundColor: _color.color.palette.whiteGrey,
      borderRadius: 16,
      flexDirection: "row",
      height: 114,
      marginBottom: 0,
      marginRight: 16,
      paddingLeft: 12,
      paddingRight: 4,
      width: widthScreen * 0.75
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 5,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 1
        },
        backgroundColor: _color.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _color.color.palette.whiteGrey
      }
    })),
    leftContent: {
      backgroundColor: _color.color.transparent,
      marginVertical: 8
    },
    moreItemContainer: Object.assign({
      alignItems: "center",
      backgroundColor: _color.color.palette.whiteGrey,
      borderRadius: 16,
      elevation: 5,
      height: 96,
      justifyContent: "center",
      marginRight: -8
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 16,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 1
        },
        backgroundColor: _color.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _color.color.palette.whiteGrey
      }
    }), {
      width: 96
    }),
    rightItemView: {
      flex: 1,
      height: "auto",
      justifyContent: "space-between",
      marginLeft: 16
    },
    rightTextTimeStyles: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _color.color.palette.darkestGrey
    }),
    rightTitleItemStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _color.color.palette.almostBlackGrey
    }),
    seeMoreStyles: Object.assign({}, _text.presets.caption2Bold),
    wrapIconImage: {
      borderRadius: 12,
      overflow: "hidden"
    }
  });
