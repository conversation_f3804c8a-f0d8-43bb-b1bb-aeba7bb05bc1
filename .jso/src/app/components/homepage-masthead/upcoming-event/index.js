  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.WIDTH_SINGLE_ITEM = exports.UpComingEventComponent = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _styles = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _native = _$$_REQUIRE(_dependencyMap[9]);
  var _adobe = _$$_REQUIRE(_dependencyMap[10]);
  var _lodash = _$$_REQUIRE(_dependencyMap[11]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _savedFlightCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _viewMoreButtons = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var WIDTH_SINGLE_ITEM = exports.WIDTH_SINGLE_ITEM = width - 48;
  var UpComingEventComponent = exports.UpComingEventComponent = function UpComingEventComponent(props) {
    var data = props.data;
    var dataLength = (0, _lodash.size)(data);
    var navigation = (0, _native.useNavigation)();
    var handleGlobalNavigate = function handleGlobalNavigate() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      navigation.navigate.apply(navigation, (0, _toConsumableArray2.default)(args));
    };
    var firstSavedFlightIndex = (0, _react.useMemo)(function () {
      return data.findIndex(function (i) {
        return (i == null ? undefined : i.upcomingEventType) === _constants.UpComingEventType.savedFlights;
      });
    }, [data]);
    var getMarginTop = function getMarginTop() {
      return {
        marginTop: 4
      };
    };
    var getDateDataV2 = function getDateDataV2(item) {
      return (item == null ? undefined : item.type) === "event" ? item == null ? undefined : item.upcomingStartScheduledTxt : item == null ? undefined : item.upcomingEndScheduledTxt;
    };
    var getPlaypassEventItemTitle = function getPlaypassEventItemTitle(item) {
      var _item$playpassEventCa;
      var itemKeyString = "titleV2";
      return item == null || (_item$playpassEventCa = item.playpassEventCard) == null ? undefined : _item$playpassEventCa[itemKeyString];
    };
    var getPlaypassEventItemImage = function getPlaypassEventItemImage(item) {
      var _item$playpassEventCa2;
      var itemKeyString = "imageV2";
      return item == null || (_item$playpassEventCa2 = item.playpassEventCard) == null ? undefined : _item$playpassEventCa2[itemKeyString];
    };
    var renderUpcomingEventItem = function renderUpcomingEventItem(_ref) {
      var item = _ref.item,
        index = _ref.index;
      switch (item == null ? undefined : item.upcomingEventType) {
        case _constants.UpComingEventType.playpassBookings:
          return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: [_styles.styles.itemUpcomingEventStyles, {
              marginLeft: 0
            }, dataLength === 1 && index === 0 && {
              width: WIDTH_SINGLE_ITEM
            }],
            onPress: function onPress() {
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeBookingTiles, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeBookingTiles, `${getPlaypassEventItemTitle(item)}|Event`));
              handleGlobalNavigate(_constants.NavigationConstants.playPassBookingDetail, {
                bookingKey: item == null ? undefined : item.bookingKey
              });
            },
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.styles.wrapIconImage,
              children: (0, _jsxRuntime.jsx)(_baseImage.default, {
                style: _styles.styles.imageStyles,
                source: {
                  uri: getPlaypassEventItemImage(item)
                }
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.styles.rightItemView,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.styles.rightTitleItemStyles,
                numberOfLines: 2,
                children: getPlaypassEventItemTitle(item)
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: [_styles.styles.rightTextTimeStyles, getMarginTop()],
                children: getDateDataV2(item)
              })]
            })]
          });
        case _constants.UpComingEventType.savedFlights:
          return (0, _jsxRuntime.jsx)(_savedFlightCard.default, {
            item: item,
            index: index,
            isFirstOfList: index === firstSavedFlightIndex,
            navigation: navigation,
            isSingle: dataLength === 1 && index === 0
          });
        default:
          return (0, _jsxRuntime.jsx)(_viewMoreButtons.default, {
            handleGlobalNavigate: handleGlobalNavigate
          });
      }
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
      data: data,
      renderItem: renderUpcomingEventItem,
      horizontal: true,
      contentContainerStyle: _styles.styles.contentFlatlistStyles,
      showsHorizontalScrollIndicator: false,
      style: _styles.styles.flatlistStyles,
      keyExtractor: function keyExtractor(_, index) {
        return index.toString();
      }
    });
  };
