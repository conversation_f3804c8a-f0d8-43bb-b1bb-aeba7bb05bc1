  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.whiteGreyLoadingColors = exports.styles = exports.loadingElementsLayout = exports.lightGreyLoadingColors = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var heightScreen = _reactNative.Dimensions.get("window").height;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomContainerStyle: {
      // justifyContent: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      marginLeft: 24,
      marginRight: 16
    },
    bottomSkeletonStyle: {
      bottom: 20,
      position: "absolute",
      width: "100%"
    },
    butterflyContainerStyle: Object.assign({
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 99,
      height: 36,
      justifyContent: "center",
      shadowColor: _theme.color.palette.almostBlackGrey,
      width: 36
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 10,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 6
        }
      },
      android: {
        elevation: 3
      }
    })),
    containerStyle: {
      borderBottomLeftRadius: 40,
      height: heightScreen * 0.35,
      justifyContent: "space-between"
    },
    eCardButtonContainer: Object.assign({
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestGrey,
      borderRadius: 80,
      height: 32,
      justifyContent: "center",
      opacity: 0.95,
      shadowColor: _theme.color.palette.almostBlackGrey
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 10,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 6
        }
      },
      android: {
        elevation: 3
      }
    }), {
      flexDirection: "row",
      paddingHorizontal: 12
    }),
    eCardButtonLoading: Object.assign({
      borderRadius: 80,
      height: 32,
      opacity: 0.95,
      shadowColor: _theme.color.palette.almostBlackGrey,
      width: 132
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 10,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 6
        }
      },
      android: {
        elevation: 3
      }
    })),
    eCardDefaultIcon: {
      color: _theme.color.palette.lightPurple,
      marginRight: 5,
      marginTop: 1
    },
    eCardText: Object.assign({}, _text.presets.textLink, {
      fontSize: _responsive.default.getFontSize(14),
      lineHeight: _responsive.default.getFontSize(16)
    }),
    imageStyle: {
      borderBottomLeftRadius: 40
    },
    imageTierHeaderStyle: {
      height: 16,
      marginRight: 5,
      marginTop: 1,
      width: 16
    },
    imageTierStyle: {
      height: 20,
      width: 20
    },
    loadingContainerStyle: {
      height: 296,
      marginBottom: 24,
      width: "100%"
    },
    payContainerStyle: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 52,
      paddingLeft: 24,
      paddingRight: 16
    },
    pointTextLoading: {
      borderRadius: 4,
      height: 16,
      width: 46
    },
    pointsContainerStyle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      marginTop: 3
    },
    pointsTextStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      fontWeight: "400",
      marginLeft: 5
    }),
    reloadIconStyles: {
      marginLeft: 5
    },
    rewardECardLoading: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.lightPurple,
      fontSize: 14,
      lineHeight: 16,
      marginRight: 7
    }),
    shimmerStyle: {
      borderBottomLeftRadius: 40,
      height: 296,
      marginBottom: 24,
      width: "100%"
    },
    upcomingEventErrContainer: {
      bottom: -16,
      height: 114,
      opacity: 1,
      paddingHorizontal: 24,
      position: "absolute",
      width: "100%"
    },
    viewInfoContainer: {
      flexShrink: 1,
      paddingRight: 12
    },
    viewRewardContainer: {
      justifyContent: "flex-end"
    },
    viewStyle: {
      alignItems: "center",
      flexDirection: "row"
    },
    walletContainerStyle: {
      alignItems: "center",
      flexDirection: "row",
      marginLeft: 14,
      marginTop: 3
    },
    walletTextLoading: {
      borderRadius: 4,
      height: 16,
      marginLeft: 5,
      width: 46
    }
  });
  var lightGreyLoadingColors = exports.lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var whiteGreyLoadingColors = exports.whiteGreyLoadingColors = [_theme.color.palette.whiteGrey, _theme.color.background, _theme.color.palette.whiteGrey];
  var loadingElementsLayout = exports.loadingElementsLayout = [{
    width: 132,
    height: 13,
    borderRadius: 4,
    marginLeft: 24
  }, {
    width: 153,
    height: 13,
    borderRadius: 4,
    marginLeft: 24,
    marginTop: 13
  }];
