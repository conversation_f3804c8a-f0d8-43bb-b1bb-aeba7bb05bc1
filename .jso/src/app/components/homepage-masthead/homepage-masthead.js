  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.HomepageMasthead = HomepageMasthead;
  exports.mappingTierCode = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _profileRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _sectionsData = _$$_REQUIRE(_dependencyMap[12]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _search = _$$_REQUIRE(_dependencyMap[14]);
  var _text = _$$_REQUIRE(_dependencyMap[15]);
  var _color = _$$_REQUIRE(_dependencyMap[16]);
  var _constants = _$$_REQUIRE(_dependencyMap[17]);
  var _utils2 = _$$_REQUIRE(_dependencyMap[18]);
  var _homepageMasthead = _$$_REQUIRE(_dependencyMap[19]);
  var _exploreRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _upcomingEvent = _$$_REQUIRE(_dependencyMap[21]);
  var _upcomingEventProps = _$$_REQUIRE(_dependencyMap[22]);
  var _error = _$$_REQUIRE(_dependencyMap[23]);
  var _i18n = _$$_REQUIRE(_dependencyMap[24]);
  var _native = _$$_REQUIRE(_dependencyMap[25]);
  var _getConfigurationPermission = _$$_REQUIRE(_dependencyMap[26]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[27]);
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[28]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[31]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[32]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var NON_LOGGED_IN = "Non-logged-in";
  var heightScreen = _reactNative2.Dimensions.get("window").height;
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _homepageMasthead.styles.loadingContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _homepageMasthead.lightGreyLoadingColors,
          shimmerStyle: _homepageMasthead.styles.shimmerStyle
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _homepageMasthead.styles.bottomSkeletonStyle,
          children: _homepageMasthead.loadingElementsLayout.map(function (item, index) {
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {
              children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _homepageMasthead.whiteGreyLoadingColors,
                shimmerStyle: item
              })
            }, index);
          })
        })]
      })
    });
  };
  var transformRewardPoint = function transformRewardPoint(rewardsData) {
    var points = (0, _lodash.get)(rewardsData, "reward.point");
    if (points) {
      var pointSubstr = points.substring(0, 8);
      if (points.length > 10) {
        return `${pointSubstr}...`;
      }
      return points;
    }
    return "";
  };
  var mappingTierCode = exports.mappingTierCode = function mappingTierCode(tierCode, arrTier) {
    if (tierCode === _changiRewardsMemberCard.Tier.StaffGold) {
      return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
        return tierIcon.tier === _changiRewardsMemberCard.Tier.Gold;
      });
    } else if (tierCode === _changiRewardsMemberCard.Tier.StaffMember) {
      return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
        return tierIcon.tier === _changiRewardsMemberCard.Tier.Member;
      });
    } else if (tierCode === _changiRewardsMemberCard.Tier.StaffPlatinum) {
      return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
        return tierIcon.tier === _changiRewardsMemberCard.Tier.Platinum;
      });
    } else if (tierCode === _changiRewardsMemberCard.Tier.StaffMonarch) {
      return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
        return tierIcon.tier === _changiRewardsMemberCard.Tier.Monarch;
      });
    }
    return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
      return tierIcon.tier === tierCode;
    });
  };
  var transformTierIcon = function transformTierIcon(rewardsData, isLoggedIn, tierIconsAEM) {
    var _rewardsData$reward;
    var tier = isLoggedIn ? rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null || (_rewardsData$reward = _rewardsData$reward.currentTierInfo) == null ? undefined : _rewardsData$reward.replace(" ", "") : NON_LOGGED_IN;
    var tierAEM = mappingTierCode(tier, tierIconsAEM);
    return (0, _utils.mappingUrlAem)(tierAEM == null ? undefined : tierAEM.icon);
  };
  var getThemeHomePage = function getThemeHomePage(isLoggedIn, rewardsData, mastHeadsAEM) {
    var _rewardsData$reward2;
    var tier = isLoggedIn ? rewardsData == null || (_rewardsData$reward2 = rewardsData.reward) == null || (_rewardsData$reward2 = _rewardsData$reward2.currentTierInfo) == null ? undefined : _rewardsData$reward2.replace(" ", "") : NON_LOGGED_IN;
    var imageHomepageAEM = mappingTierCode(tier, mastHeadsAEM);
    return {
      srcImageHomepage: (0, _utils.mappingUrlAem)(imageHomepageAEM == null ? undefined : imageHomepageAEM.image),
      isDarkTheme: (imageHomepageAEM == null ? undefined : imageHomepageAEM.background) === "Dark"
    };
  };
  var getThemeColor = function getThemeColor(isDarkTheme, errorAEM, profileCardNoMissing) {
    if (isDarkTheme || errorAEM || profileCardNoMissing) {
      return _color.color.palette.whiteGrey;
    }
    return _color.color.palette.almostBlackGrey;
  };
  var getSourceImage = function getSourceImage(errorAEM, rewardsError, srcImageHomepage, profileCardNoMissing) {
    if (profileCardNoMissing) {
      return _sectionsData.sectionsPayloadForExplore.homePageMastHead.default.loggedIn.imageUrl;
    }
    if (errorAEM || rewardsError) {
      return _sectionsData.sectionsPayloadForExplore.homePageMastHead.default.loggedIn.imageUrl;
    }
    return srcImageHomepage;
  };
  var getTitleHomePage = function getTitleHomePage(isLoggedIn, name, welcomeText, profileLandingError) {
    if (profileLandingError) {
      return "";
    }
    if (isLoggedIn) {
      return `Hi ${name || ""}`;
    }
    return (0, _i18n.translate)(welcomeText);
  };
  var getMastheadContainer = function getMastheadContainer(isShowUpcomingEventView) {
    return {
      height: heightScreen * 0.35 + (isShowUpcomingEventView ? 45 : 0),
      backgroundColor: _color.color.palette.lightestGrey,
      marginBottom: isShowUpcomingEventView ? 14 : 0
    };
  };
  var marginBottomValue = function marginBottomValue(isShowUpcomingEventView) {
    return isShowUpcomingEventView ? 64 : 16;
  };
  var pointTextComponent = function pointTextComponent(rewardsError, rewardPoints, themeColor, _errorAEM, profileError) {
    var marginLeft = 0;
    var marginRight = 0;
    var errorState = rewardsError || profileError;
    if (errorState) {
      marginLeft = 5;
      marginRight = 7;
    }
    return (0, _jsxRuntime.jsx)(_text.Text, {
      tx: errorState && "homepageMasthead.reload",
      text: !errorState && `${rewardPoints} pts`,
      preset: "subTitleBold",
      style: Object.assign({}, _homepageMasthead.styles.pointsTextStyle, {
        color: themeColor,
        marginLeft: marginLeft,
        marginRight: marginRight
      }),
      numberOfLines: 1
    });
  };
  var walletTextComponent = function walletTextComponent(_ref, themeColor, profileError) {
    var rewardsError = _ref.rewardsError,
      rewardsFetching = _ref.rewardsFetching,
      rewardsData = _ref.rewardsData,
      profileFetching = _ref.profileFetching;
    var errorState = rewardsError || profileError;
    if ((0, _utils2.ifAllTrue)([rewardsFetching, (0, _lodash.isEmpty)(rewardsData)]) || profileFetching) {
      return (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _homepageMasthead.lightGreyLoadingColors,
        shimmerStyle: _homepageMasthead.styles.walletTextLoading
      });
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        tx: (0, _utils.handleCondition)(errorState, "homepageMasthead.reload", ""),
        text: (0, _utils.handleCondition)(!errorState, (0, _i18n.translate)("homepageMasthead.wallet"), ""),
        preset: "subTitleBold",
        style: Object.assign({}, _homepageMasthead.styles.pointsTextStyle, {
          color: themeColor,
          marginRight: (0, _utils.handleCondition)(errorState, 7, 0)
        }),
        numberOfLines: 1
      }), (0, _utils.handleCondition)(errorState, (0, _jsxRuntime.jsx)(_icons.ReloadIcon, {
        color: themeColor,
        width: 14,
        height: 14
      }), null)]
    });
  };
  var _Dimensions$get = _reactNative2.Dimensions.get("screen"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var overLayLoading = {
    width: width,
    height: height,
    position: "absolute"
  };
  function HomepageMasthead(props) {
    var sourceSystem = props.sourceSystem,
      onChangiPayPressed = props.onChangiPayPressed,
      onSearchBarPressed = props.onSearchBarPressed,
      onChangiRewardsIconPressed = props.onChangiRewardsIconPressed,
      onChangiRewardsPointsPressed = props.onChangiRewardsPointsPressed,
      onReLoadUpComingEvent = props.onReLoadUpComingEvent,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "HomepageMasthead" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "HomepageMasthead" : _props$accessibilityL;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var userProfile = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var profileLandingError = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileLandingError);
    var profileCardNoMissing = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileCardNoMissing);
    var profileFetching = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileFetching);
    var profileError = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileError);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var rewardsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsError);
    var rewardsFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsFetching);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var tierIconsAEM = (0, _lodash.get)(dataCommonAEM, "data.pageLanding.explore.tierIcons");
    var mastHeadsAEM = (0, _lodash.get)(dataCommonAEM, "data.pageLanding.explore.mastHeads");
    var isLoadingAem = dataCommonAEM == null ? undefined : dataCommonAEM.loading;
    var errorAEM = dataCommonAEM == null ? undefined : dataCommonAEM.error;
    var firstName = (0, _lodash.get)(userProfile, "firstName");
    var logInText = "homepageMasthead.logInText";
    var welcomeText = "homepageMasthead.welcomeText";
    var getUpComingLoading = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getUpComingLoading);
    var getUpcomingEventsSuccess = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getUpcomingEventsSuccess);
    var upComingEventData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.upComingEventsData);
    var getUpComingEventError = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getUpComingEventError);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var rewardPoints = (0, _react.useMemo)(function () {
      return transformRewardPoint(rewardsData);
    }, [rewardsData]);
    var tierIcon = (0, _react.useMemo)(function () {
      return transformTierIcon(rewardsData, isLoggedIn, tierIconsAEM);
    }, [rewardsData, tierIconsAEM, isLoggedIn]);
    var errorState = rewardsError || profileError;
    var _useGetConfigurationP = (0, _getConfigurationPermission.useGetConfigurationPermissionHelper)(),
      loadingGetConfig = _useGetConfigurationP.loadingGetConfig,
      getConfigApp = _useGetConfigurationP.getConfigApp,
      notifyDisableChangiRewards = _useGetConfigurationP.notifyDisableChangiRewards;
    var _useMemo = (0, _react.useMemo)(function () {
        return getThemeHomePage(isLoggedIn, rewardsData, mastHeadsAEM);
      }, [rewardsData, mastHeadsAEM, isLoggedIn]),
      srcImageHomepage = _useMemo.srcImageHomepage,
      isDarkTheme = _useMemo.isDarkTheme;
    var themeColor = getThemeColor(isDarkTheme, errorAEM, profileCardNoMissing);
    var loadingType = (0, _react.useMemo)(function () {
      return (0, _utils2.ifAllTrue)([!getUpComingLoading, !(myTravelFlightsPayload != null && myTravelFlightsPayload.loading)]);
    }, [getUpComingLoading, myTravelFlightsPayload]);
    var isShowUpcomingEventView = (0, _react.useMemo)(function () {
      if ((0, _utils2.ifAllTrue)([isLoggedIn, !(0, _lodash.isEmpty)(upComingEventData) || getUpComingEventError])) return true;
      return false;
    }, [isLoggedIn, upComingEventData, getUpComingEventError, getUpcomingEventsSuccess, profileError]);
    var handleRequestReward = function handleRequestReward(callbackfunc) {
      if (isLoggedIn && !rewardsFetching && errorState) {
        if (profileError || profileCardNoMissing) {
          dispatch(_profileRedux.default.profilePayloadReset());
          dispatch(_profileRedux.default.profileRequest());
        }
        dispatch(_forYouRedux.default.rewardsRequest((userProfile == null ? undefined : userProfile.cardNo) || ""));
        return;
      }
      callbackfunc == null || callbackfunc();
    };
    var handleLogin = function handleLogin() {
      //@ts-ignore
      navigation.navigate(_constants.NavigationConstants.authScreen, {
        sourceSystem: sourceSystem
      });
    };
    var handleOpenChangiRewards = function handleOpenChangiRewards() {
      getConfigApp({
        configKey: _constants.AppConfigPermissionTypes.changiappCREnabled,
        callbackSuccess: function callbackSuccess() {
          return handleRequestReward(onChangiRewardsPointsPressed(isLoggedIn));
        },
        callbackFailure: function callbackFailure() {
          return notifyDisableChangiRewards();
        }
      });
    };
    return isLoadingAem ? loadingView() : (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: getMastheadContainer(isShowUpcomingEventView),
        children: [(0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
          source: {
            uri: getSourceImage(errorAEM, rewardsError, srcImageHomepage, profileCardNoMissing)
          },
          imageStyle: _homepageMasthead.styles.containerStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _homepageMasthead.styles.payContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {}), (0, _jsxRuntime.jsx)(_search.Search, {
              onPressed: onSearchBarPressed
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: [_homepageMasthead.styles.bottomContainerStyle, {
              marginBottom: marginBottomValue(isShowUpcomingEventView)
            }],
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _homepageMasthead.styles.viewInfoContainer,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                text: getTitleHomePage(isLoggedIn, firstName, welcomeText, profileLandingError),
                preset: "h3",
                numberOfLines: 1,
                style: {
                  color: themeColor
                }
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _homepageMasthead.styles.viewStyle,
                children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, Object.assign({
                  disabled: rewardsFetching,
                  style: _homepageMasthead.styles.pointsContainerStyle,
                  onPress: (0, _utils.handleCondition)(isLoggedIn, handleOpenChangiRewards, handleLogin)
                }, (0, _utils2.accessibility)({
                  testID: `${testID}__TouchableChangiRewardPoint`,
                  accessibilityLabel: `${accessibilityLabel}__TouchableChangiRewardPoint`,
                  OS: _reactNative2.Platform.OS
                }), {
                  children: (0, _utils.handleCondition)((0, _utils2.ifAllTrue)([rewardsFetching, (0, _lodash.isEmpty)(rewardsData)]) || profileFetching, (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                    duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                    shimmerColors: _homepageMasthead.lightGreyLoadingColors,
                    shimmerStyle: _homepageMasthead.styles.pointTextLoading
                  }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                    children: [(0, _utils.simpleCondition)({
                      condition: (0, _utils.handleCondition)(isLoggedIn && errorState, true, false),
                      ifValue: (0, _jsxRuntime.jsx)(_icons.Butterfly, {
                        width: 20,
                        height: 20,
                        color: themeColor
                      }),
                      elseValue: (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {})
                    }), (0, _utils.handleCondition)(!isLoggedIn, (0, _jsxRuntime.jsx)(_text.Text, {
                      tx: logInText,
                      preset: "subTitleBold",
                      style: Object.assign({}, _homepageMasthead.styles.pointsTextStyle, {
                        color: themeColor,
                        marginLeft: 0
                      }),
                      numberOfLines: 1
                    }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                      children: [pointTextComponent(rewardsError, rewardPoints, themeColor, errorAEM, profileError), (0, _utils.handleCondition)(errorState, (0, _jsxRuntime.jsx)(_icons.ReloadIcon, {
                        color: themeColor,
                        width: 14,
                        height: 14
                      }), null)]
                    }))]
                  }))
                })), (0, _utils.handleCondition)(isLoggedIn, (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, Object.assign({
                  style: _homepageMasthead.styles.walletContainerStyle,
                  onPress: function onPress() {
                    if (rewardsFetching) {
                      return;
                    }
                    handleRequestReward(onChangiPayPressed);
                  }
                }, (0, _utils2.accessibility)({
                  testID: `${testID}__TouchableChangiWallet`,
                  accessibilityLabel: `${accessibilityLabel}__TouchableChangiWallet`,
                  OS: _reactNative2.Platform.OS
                }), {
                  children: [(0, _jsxRuntime.jsx)(_icons.Wallet, {
                    color: themeColor
                  }), walletTextComponent({
                    rewardsError: rewardsError,
                    rewardsFetching: rewardsFetching,
                    rewardsData: rewardsData,
                    profileFetching: profileFetching
                  }, themeColor, profileError)]
                })), null)]
              })]
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _homepageMasthead.styles.viewRewardContainer,
              children: (0, _utils.handleCondition)((0, _utils2.ifAllTrue)([rewardsFetching, (0, _lodash.isEmpty)(rewardsData)]) || profileFetching, (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _homepageMasthead.lightGreyLoadingColors,
                shimmerStyle: _homepageMasthead.styles.eCardButtonLoading
              }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, Object.assign({
                style: _homepageMasthead.styles.eCardButtonContainer,
                onPress: function onPress() {
                  if (rewardsFetching) {
                    return;
                  }
                  handleRequestReward(function () {
                    return onChangiRewardsIconPressed(isLoggedIn);
                  });
                }
              }, (0, _utils2.accessibility)({
                testID: `${testID}__TouchableChangiRewardIcon`,
                accessibilityLabel: `${accessibilityLabel}__TouchableChangiRewardIcon`,
                OS: _reactNative2.Platform.OS
              }), {
                children: (0, _utils.handleCondition)(isLoggedIn && errorState, (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    tx: (0, _utils.handleCondition)(errorState, "homepageMasthead.reload", ""),
                    text: (0, _utils.handleCondition)(!errorState, `${rewardPoints} pts`, null),
                    preset: "subTitleBold",
                    style: Object.assign({}, _homepageMasthead.styles.rewardECardLoading),
                    numberOfLines: 1
                  }), (0, _jsxRuntime.jsx)(_icons.ReloadIcon, {
                    color: _color.color.palette.lightPurple,
                    width: 14,
                    height: 14
                  })]
                }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _utils.handleCondition)(errorAEM, (0, _jsxRuntime.jsx)(_icons.Butterfly, {
                    width: 18,
                    height: 18,
                    style: _homepageMasthead.styles.eCardDefaultIcon
                  }), (0, _jsxRuntime.jsx)(_baseImage.default, {
                    source: {
                      uri: tierIcon
                    },
                    style: _homepageMasthead.styles.imageTierHeaderStyle,
                    resizeMode: "contain"
                  })), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _homepageMasthead.styles.eCardText,
                    tx: "homepageMasthead.rewardsCard"
                  })]
                }))
              })))
            })]
          })]
        }), (0, _utils.handleCondition)(getUpComingEventError !== null && loadingType && isLoggedIn, (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _homepageMasthead.styles.upcomingEventErrContainer,
          children: (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
            type: _error.ErrorComponentType.standard,
            onPressed: onReLoadUpComingEvent,
            style: {
              margin: 0
            }
          })
        }), isShowUpcomingEventView && (0, _jsxRuntime.jsx)(_upcomingEvent.UpComingEventComponent, {
          data: upComingEventData,
          type: _upcomingEventProps.UpComingEventState.default
        }))]
      }), (0, _utils.handleCondition)(loadingGetConfig, (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: overLayLoading,
        children: (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
          visible: loadingGetConfig
        })
      }), null)]
    });
  }
