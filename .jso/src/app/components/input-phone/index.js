  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _inputPhone = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_inputPhone).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _inputPhone[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _inputPhone[key];
      }
    });
  });
  var _inputPhone2 = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_inputPhone2).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _inputPhone2[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _inputPhone2[key];
      }
    });
  });
