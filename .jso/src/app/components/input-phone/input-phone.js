  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.InputPhone = undefined;
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNativePickerSelect = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _textField = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _envParams = _$$_REQUIRE(_dependencyMap[10]);
  var _countries = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var _excluded = ["isInvalid", "isDisabled", "countryOptions", "phoneNumber", "countryCode", "maxLength", "onPhoneChange", "onCountryCodeChange", "highlightOnFocused", "onBlurInput"];
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT = "InputPhone";
  var InputPhone = exports.InputPhone = function InputPhone(props) {
    var _env;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      focused = _useState2[0],
      setFocused = _useState2[1];
    var isInvalid = props.isInvalid,
      isDisabled = props.isDisabled,
      _props$countryOptions = props.countryOptions,
      countryOptions = _props$countryOptions === undefined ? [] : _props$countryOptions,
      phoneNumber = props.phoneNumber,
      countryCode = props.countryCode,
      maxLength = props.maxLength,
      onPhoneChange = props.onPhoneChange,
      onCountryCodeChange = props.onCountryCodeChange,
      highlightOnFocused = props.highlightOnFocused,
      onBlurInput = props.onBlurInput,
      inputProps = (0, _objectWithoutProperties2.default)(props, _excluded);
    var pickerSelectRef = (0, _react.useRef)(null);
    var items = (0, _react.useMemo)(function () {
      return Array.isArray(countryOptions) ? countryOptions.map(function (option, index) {
        return Object.assign({}, option, {
          key: index,
          label: `${option.label} +${option.value}`
        });
      }) : [];
    }, [countryOptions]);
    var _useState3 = (0, _react.useState)(-1),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      selectingIndex = _useState4[0],
      setSelectingIndex = _useState4[1];
    var selectedCountryCode = (0, _react.useMemo)(function () {
      var _listCountries$find;
      return _countries.default == null || (_listCountries$find = _countries.default.find(function (item) {
        return item.code === countryCode;
      })) == null || (_listCountries$find = _listCountries$find.value) == null ? undefined : _listCountries$find.toLowerCase();
    }, [countryCode]);
    var pickerValue = (0, _react.useMemo)(function () {
      return selectingIndex in countryOptions ? countryOptions[selectingIndex].value : countryCode;
    }, [selectingIndex, countryOptions, countryCode]);

    // Format display: 00000000 -> 0000 0000
    var maskedPhoneNumber = (0, _react.useMemo)(function () {
      return phoneNumber && phoneNumber.length > 4 ? phoneNumber.substring(0, 4) + " " + phoneNumber.substring(4, phoneNumber.length) : phoneNumber;
    }, [phoneNumber]);
    var onValueChange = function onValueChange(value, index) {
      if (_reactNative2.Platform.OS === "android" && value !== countryCode) {
        onCountryCodeChange == null || onCountryCodeChange(value);
      }
      if (_reactNative2.Platform.OS === "ios" && selectingIndex !== index) {
        setSelectingIndex(index);
      }
    };
    var onDonePress = function onDonePress() {
      if (selectingIndex in countryOptions) {
        var option = countryOptions[selectingIndex];
        countryCode !== option.value && (onCountryCodeChange == null ? undefined : onCountryCodeChange(option.value));
        setSelectingIndex(-1);
      }
    };
    var onClose = function onClose() {
      setSelectingIndex(-1);
    };
    var renderInputAccessoryView = function renderInputAccessoryView() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.inputAccessoryView,
        testID: `${COMPONENT}__SelectPickerInputAccessoryView`,
        accessibilityLabel: `${COMPONENT}__SelectInputAccessoryView`,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            pickerSelectRef.current.togglePicker(true);
            onDonePress();
          },
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: "Done",
            style: styles.textDoneStyles
          })
        })
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: Object.assign({}, styles.fieldContainerStyle, isInvalid ? styles.fieldContainerErrorStyle : {}, isDisabled ? styles.fieldContainerDisabledStyle : {}, highlightOnFocused && focused ? Object.assign({}, styles.focusedInput, inputProps.highlightOnFocusedStyle) : {}),
      children: [(0, _jsxRuntime.jsx)(_reactNativePickerSelect.default, {
        ref: pickerSelectRef,
        items: items,
        placeholder: {},
        value: pickerValue,
        disabled: isDisabled || (countryOptions == null ? undefined : countryOptions.length) <= 1,
        fixAndroidTouchableBug: true,
        onValueChange: onValueChange,
        onDonePress: onDonePress,
        onClose: onClose,
        InputAccessoryView: renderInputAccessoryView,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.flagContainerStyle,
          children: [!!selectedCountryCode && (0, _jsxRuntime.jsx)(_baseImage.default, {
            style: styles.flagImageStyle,
            source: {
              uri: (_env = (0, _envParams.env)()) == null ? undefined : _env.FLAG_COUNTRY.replace("{code}", selectedCountryCode)
            }
          }), (countryOptions == null ? undefined : countryOptions.length) > 1 && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.pickerIconStyle,
            children: inputProps.iconDownArrow || (0, _jsxRuntime.jsx)(_icons.DownArrow, {
              width: 24,
              height: 24
            })
          })]
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.fieldRightContainerStyle,
        children: [!inputProps.noShowCode && (0, _jsxRuntime.jsx)(_text.Text, {
          style: Object.assign({}, styles.countryCodeTextStyle, isDisabled ? styles.countryCodeTextDisabledStyle : {}),
          text: countryCode ? "+" + countryCode : ""
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.inputGroupStyle,
          children: (0, _jsxRuntime.jsx)(_textField.TextField, Object.assign({
            style: styles.inputContainer,
            inputStyle: Object.assign({}, styles.inputStyle, isDisabled ? styles.inputDisabledStyle : {}),
            keyboardType: "number-pad"
          }, inputProps, {
            maxLength: maxLength >= 4 ? maxLength + 1 : maxLength,
            value: maskedPhoneNumber,
            editable: !isDisabled,
            onChangeText: function onChangeText(value) {
              var val = value.replace(/(\D)/g, "");
              // Remove all non-numberic
              onPhoneChange == null || onPhoneChange(maxLength ? val.slice(0, maxLength) : val);
            },
            onFocus: function onFocus() {
              setFocused(true);
            },
            onBlur: function onBlur() {
              onBlurInput();
              setFocused(false);
            }
          }))
        })]
      })]
    });
  };
  var _default = exports.default = InputPhone;
