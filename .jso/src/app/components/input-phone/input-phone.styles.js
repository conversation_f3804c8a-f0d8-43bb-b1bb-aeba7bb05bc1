  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.textDoneStyles = exports.pickerIconStyle = exports.inputStyle = exports.inputGroupStyle = exports.inputDisabledStyle = exports.inputContainer = exports.inputAccessoryView = exports.focusedInput = exports.flagImageStyle = exports.flagContainerStyle = exports.fieldRightContainerStyle = exports.fieldContainerStyle = exports.fieldContainerErrorStyle = exports.fieldContainerDisabledStyle = exports.countryCodeTextStyle = exports.countryCodeTextDisabledStyle = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var fieldContainerStyle = exports.fieldContainerStyle = {
    flexDirection: "row",
    alignContent: "center",
    overflow: "hidden",
    height: 44,
    borderWidth: 1,
    borderRadius: 12,
    borderColor: _theme.color.palette.lightGrey
  };
  var fieldContainerErrorStyle = exports.fieldContainerErrorStyle = {
    borderColor: _theme.color.palette.baseRed
  };
  var fieldContainerDisabledStyle = exports.fieldContainerDisabledStyle = {
    borderColor: _theme.color.palette.lighterGrey
  };
  var flagContainerStyle = exports.flagContainerStyle = {
    minWidth: 50,
    maxWidth: 72,
    height: 44,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingLeft: 10,
    paddingRight: 8
  };
  var flagImageStyle = exports.flagImageStyle = {
    width: 22,
    height: 16,
    borderRadius: 2
  };
  var pickerIconStyle = exports.pickerIconStyle = {
    flexGrow: 1,
    flexDirection: "row",
    justifyContent: "flex-end",
    marginLeft: 5
  };
  var fieldRightContainerStyle = exports.fieldRightContainerStyle = {
    flexGrow: 1,
    flexDirection: "row",
    alignItems: "center",
    borderLeftColor: _theme.color.palette.lightGrey,
    borderLeftWidth: 2,
    height: 44
  };
  var countryCodeTextStyle = exports.countryCodeTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    marginLeft: 8,
    color: _theme.color.palette.almostBlackGrey
  });
  var countryCodeTextDisabledStyle = exports.countryCodeTextDisabledStyle = {
    color: _theme.color.palette.midGrey
  };
  var inputGroupStyle = exports.inputGroupStyle = {
    flexGrow: 1
  };
  var inputContainer = exports.inputContainer = {
    flexGrow: 1,
    paddingLeft: _reactNative.Platform.OS === "ios" ? 4 : 0
  };
  var inputStyle = exports.inputStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    marginTop: _reactNative.Platform.OS === "ios" ? -2 : 2,
    backgroundColor: "transparent",
    textAlignVertical: "center",
    color: _theme.color.palette.almostBlackGrey
  });
  var inputDisabledStyle = exports.inputDisabledStyle = {
    color: _theme.color.palette.midGrey
  };
  var focusedInput = exports.focusedInput = {
    borderColor: _theme.color.palette.lightPurple
  };
  var inputAccessoryView = exports.inputAccessoryView = {
    alignItems: "flex-end",
    backgroundColor: _theme.color.palette.greyf8f8f8,
    borderTopColor: _theme.color.palette.greydedede,
    borderTopWidth: 1,
    height: 45,
    justifyContent: "center",
    paddingHorizontal: 10,
    width: "100%"
  };
  var textDoneStyles = exports.textDoneStyles = {
    color: _theme.color.palette.lightBlue,
    fontSize: 17,
    fontWeight: "600",
    paddingRight: 11,
    paddingTop: 1
  };
