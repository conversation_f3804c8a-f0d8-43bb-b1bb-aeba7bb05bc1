  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var windowWidth = _reactNative.Dimensions.get("window").width;
  var bgHeight = windowWidth * 0.6;
  var figmaBaseWidth = 375;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    flex1: {
      flex: 1,
      backgroundColor: _theme.color.palette.yellowLight
    },
    bottomLoadingView: {
      flex: 1,
      alignItems: "center",
      paddingHorizontal: 24
    },
    modalStyles: {
      flex: 1,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    backgroundImageStyle: {
      width: "100%",
      height: windowWidth * 2.8373333333333335
    },
    headerContainer: {
      width: "100%",
      height: bgHeight
    },
    headerContainerStyle: {
      height: windowWidth * 0.6266666666666667,
      width: "100%",
      alignItems: "center"
    },
    cardImageStyles: {
      width: windowWidth * 0.****************,
      height: windowWidth * 0.****************,
      borderRadius: 12
    },
    contentContainerStyles: {
      width: "100%",
      paddingTop: 16,
      paddingHorizontal: 20
    },
    contentViewStyles: {
      alignItems: "center"
    },
    cardShadowStyles: {
      position: "absolute",
      bottom: -20.5
    },
    cardShadowImageStyles: {
      width: 245,
      height: 41
    },
    titleTextStyles: {
      marginTop: 38,
      fontFamily: _theme.typography.regular,
      fontSize: 28,
      lineHeight: 36,
      textAlign: "left",
      color: _theme.color.palette.whiteGrey
    },
    captionTextStyles: {
      marginTop: 8,
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.whiteGrey,
      textAlign: "center"
    },
    buttonLinearStyles: {
      borderRadius: 60,
      marginTop: 16
    },
    viewAllPrivilegesAndDealTextStyles: {
      fontFamily: _theme.typography.bold,
      fontSize: 16,
      lineHeight: 20,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      marginHorizontal: 20
    },
    viewTermsAndConditionsCtaTextStyles: {
      fontFamily: _theme.typography.bold,
      fontSize: 16,
      lineHeight: 20,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      marginHorizontal: 20,
      color: _theme.color.palette.whiteGrey
    },
    viewTermsAndConditionsButton: {
      marginTop: 24
    },
    touristPerksCardStyles: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      padding: 24,
      width: windowWidth - 48,
      marginTop: 24
    },
    touristPerksCardTitleContainer: {
      flexDirection: "row",
      alignItems: "center"
    },
    touristPerksTextStyles: {
      fontFamily: _theme.typography.bold,
      fontSize: 18,
      textTransform: "uppercase",
      lineHeight: 22,
      paddingRight: 24
    },
    touristPerksCardCaptionStyles: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 12
    },
    touristIconStyles: {
      width: 24,
      height: 24,
      marginRight: 4
    },
    closeButtonStyles: {
      opacity: 0.6,
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      position: "absolute",
      top: 20,
      left: windowWidth - 40,
      right: 0,
      bottom: 0,
      width: 24,
      height: 24,
      zIndex: 999,
      borderRadius: 12
    },
    textLoading1: {
      height: 16,
      width: 204,
      borderRadius: 8,
      marginTop: 40
    },
    textLoading2: {
      height: 12,
      width: 120,
      borderRadius: 8,
      marginTop: 8
    },
    cardLoadingContainer: {
      marginTop: 38
    },
    cardItemLoadingContainer: Object.assign({
      width: "100%",
      borderRadius: 12,
      flexDirection: "row",
      marginTop: 12,
      marginHorizontal: 24,
      padding: 20,
      backgroundColor: _theme.color.palette.whiteGrey
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        }
      },
      android: {
        elevation: 3
      }
    })),
    imageCardLoading: {
      width: 80,
      height: 80
    },
    rightCardLoading: {
      flex: 1,
      marginLeft: 12,
      justifyContent: "center"
    },
    rightCard1Loading: {
      height: 12,
      borderRadius: 4
    },
    rightCard2Loading: {
      height: 12,
      width: 74,
      borderRadius: 4,
      marginTop: 12
    },
    loadingContainer: {
      flex: 1,
      backgroundColor: _theme.color.palette.lightestGrey
    },
    cardsContainer: {
      marginTop: 28,
      width: "100%"
    },
    errorStyles: {
      height: "80%",
      justifyContent: "flex-start",
      paddingVertical: 30,
      width: "100%"
    },
    headerOpacityStyles: {
      position: "absolute",
      width: "100%",
      height: windowWidth * 0.6266666666666667
    },
    jpcSparklesContainer: {
      height: windowWidth * 0.6266666666666667,
      width: "100%",
      position: "absolute",
      bottom: -21.5
    },
    cardImageViewStyles: {
      borderRadius: 12,
      overflow: "hidden"
    }
  });
