  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _JewelPrivilegeDetailController;
  var JewelPrivilegeDetailController = exports.default = /*#__PURE__*/(0, _createClass2.default)(function JewelPrivilegeDetailController() {
    (0, _classCallCheck2.default)(this, JewelPrivilegeDetailController);
  });
  _JewelPrivilegeDetailController = JewelPrivilegeDetailController;
  JewelPrivilegeDetailController.setAppRef = function (ref) {
    _JewelPrivilegeDetailController.appRef = ref;
  };
  JewelPrivilegeDetailController.setModalRef = function (ref) {
    _JewelPrivilegeDetailController.modalRef = ref;
  };
  JewelPrivilegeDetailController.showModal = function (nav) {
    var _JewelPrivilegeDetail;
    (_JewelPrivilegeDetail = _JewelPrivilegeDetailController.modalRef) == null || (_JewelPrivilegeDetail = _JewelPrivilegeDetail.current) == null || _JewelPrivilegeDetail.show(nav);
  };
  JewelPrivilegeDetailController.hideModal = function () {
    var _JewelPrivilegeDetail2;
    (_JewelPrivilegeDetail2 = _JewelPrivilegeDetailController.modalRef) == null || (_JewelPrivilegeDetail2 = _JewelPrivilegeDetail2.current) == null || _JewelPrivilegeDetail2.hide();
  };
