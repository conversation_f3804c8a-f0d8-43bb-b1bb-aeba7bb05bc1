  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _maskedView = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var TextGradient = function TextGradient(_ref) {
    var text = _ref.text,
      colors = _ref.colors,
      _ref$style = _ref.style,
      style = _ref$style === undefined ? {} : _ref$style,
      _ref$start = _ref.start,
      start = _ref$start === undefined ? {
        x: 0,
        y: 0
      } : _ref$start,
      _ref$end = _ref.end,
      end = _ref$end === undefined ? {
        x: 1,
        y: 1
      } : _ref$end,
      _ref$locations = _ref.locations,
      locations = _ref$locations === undefined ? [0, 1] : _ref$locations,
      numberOfLines = _ref.numberOfLines;
    return (0, _jsxRuntime.jsx)(_maskedView.default, {
      maskElement: (0, _jsxRuntime.jsx)(_reactNative.Text, {
        style: [style, {
          backgroundColor: "transparent"
        }],
        numberOfLines: 2,
        children: text
      }),
      children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        colors: colors,
        start: start,
        end: end,
        locations: locations,
        children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
          style: [style, {
            opacity: 0
          }],
          numberOfLines: numberOfLines,
          children: text
        })
      })
    });
  };
  var _default = exports.default = TextGradient;
