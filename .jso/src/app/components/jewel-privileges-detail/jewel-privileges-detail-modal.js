  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[10]);
  var _jewelPrivilegesDetailControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _backgrounds = _$$_REQUIRE(_dependencyMap[14]);
  var _privilegesRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[18]);
  var _text = _$$_REQUIRE(_dependencyMap[19]);
  var _button = _$$_REQUIRE(_dependencyMap[20]);
  var _textGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _itemCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _icons = _$$_REQUIRE(_dependencyMap[23]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _constants = _$$_REQUIRE(_dependencyMap[25]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[26]);
  var _jewelPrivilegesDetailStyles = _$$_REQUIRE(_dependencyMap[27]);
  var _lodash = _$$_REQUIRE(_dependencyMap[28]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[29]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[30]);
  var _errorOverlayVariant = _$$_REQUIRE(_dependencyMap[31]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[32]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _errorOverlayVariant2 = _$$_REQUIRE(_dependencyMap[34]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _JPC_sparkles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[36]));
  var _adobe = _$$_REQUIRE(_dependencyMap[37]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[38]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[39]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var windowWidth = _reactNative2.Dimensions.get("window").width;
  var figmaBaseWidth = 375;
  var heightBgImage = windowWidth * 2.8373333333333335;
  var heightHeader = windowWidth * 0.6266666666666667;
  var heightImageHeader = windowWidth * 0.****************;
  var activeGradient = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var closeIconStyles = {
    color: _theme.color.palette.darkestGrey
  };
  var cardArrLoading = Array(2).fill(null);
  var TRANSITION_TIMING = 400;
  var lighterGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var _worklet_8592924079645_init_data = {
    code: "function jewelPrivilegesDetailModalTsx1(){const{interpolate,animationProgress,heightHeader,heightImageHeader,Extrapolation}=this.__closure;return{transform:[{translateY:interpolate(animationProgress.value,[0,1],[0,heightHeader-heightImageHeader],Extrapolation.CLAMP)}],opacity:interpolate(animationProgress.value,[0,0.5,1],[0,0.3,1],Extrapolation.CLAMP),zIndex:1};}"
  };
  var _worklet_8795999407311_init_data = {
    code: "function jewelPrivilegesDetailModalTsx2(){const{interpolate,animationProgress,Extrapolation}=this.__closure;return{opacity:interpolate(animationProgress.value,[0,1,2],[1,0.6,0],Extrapolation.CLAMP)};}"
  };
  var _worklet_871361928310_init_data = {
    code: "function jewelPrivilegesDetailModalTsx3(){const{interpolate,animationProgress,Extrapolation}=this.__closure;return{opacity:interpolate(animationProgress.value,[0,1],[0,1],Extrapolation.CLAMP),zIndex:0};}"
  };
  var _worklet_12667552368819_init_data = {
    code: "function jewelPrivilegesDetailModalTsx4(){const{interpolate,animationProgress,heightHeader,heightImageHeader,Extrapolation}=this.__closure;return{zIndex:interpolate(animationProgress.value,[0,(heightHeader-heightImageHeader)*3/4,heightHeader-heightImageHeader],[0,-2,-2],Extrapolation.CLAMP),opacity:interpolate(animationProgress.value,[0,0.5,1],[0.4,0.4,1],Extrapolation.CLAMP)};}"
  };
  var JewelPrivilegesDetailModal = function JewelPrivilegesDetailModal(_ref) {
    var _navigationRef$curren;
    var navigationRef = _ref.navigationRef;
    var modalRef = (0, _react.useRef)(null);
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("jewelPrivilegesPlayPass"),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl;
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var ehr21 = errorData == null ? undefined : errorData.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR2.1";
    });
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setConnected = _useState2[1];
    var _useState3 = (0, _react.useState)(0),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      heightBottomView = _useState4[0],
      setHeightBottomView = _useState4[1];
    var _useState5 = (0, _react.useState)(),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      newNavigation = _useState6[0],
      setNewNavigation = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      modalVisible = _useState8[0],
      setModalVisible = _useState8[1];
    var lottieViewRef = (0, _react.useRef)(null);
    var animationProgress = (0, _reactNativeReanimated.useSharedValue)(0);
    var isModalOpenRef = (0, _react.useRef)(false);
    var getPrivilegesDetailLoading = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.getPrivilegesDetailLoading);
    var privilegesDetailData = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.privilegesDetailData);
    var getPrivilegesDetailError = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.getPrivilegesDetailError);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("customNavigation", newNavigation),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var navigationRoute = navigationRef == null || (_navigationRef$curren = navigationRef.current) == null || _navigationRef$curren.getCurrentRoute == null ? undefined : _navigationRef$curren.getCurrentRoute();

    // useEffect(() => {
    //   if (!isEmpty(privilegesDetailData)) {
    //     lottieViewRef.current?.play()
    //     animationProgress.value = withSequence(
    //       withTiming(1, {duration: 1200}),
    //       withTiming(2, {duration: 200})
    //     )
    //   }
    // }, [privilegesDetailData])

    var animatedCardStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var jewelPrivilegesDetailModalTsx1 = function jewelPrivilegesDetailModalTsx1() {
        return {
          transform: [{
            translateY: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [0, 1], [0, heightHeader - heightImageHeader], _reactNativeReanimated.Extrapolation.CLAMP)
          }],
          opacity: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [0, 0.5, 1], [0, 0.3, 1], _reactNativeReanimated.Extrapolation.CLAMP),
          zIndex: 1
        };
      };
      jewelPrivilegesDetailModalTsx1.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animationProgress: animationProgress,
        heightHeader: heightHeader,
        heightImageHeader: heightImageHeader,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      jewelPrivilegesDetailModalTsx1.__workletHash = 8592924079645;
      jewelPrivilegesDetailModalTsx1.__initData = _worklet_8592924079645_init_data;
      return jewelPrivilegesDetailModalTsx1;
    }(), [heightHeader, heightImageHeader]);
    var headerOpacityStyles = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var jewelPrivilegesDetailModalTsx2 = function jewelPrivilegesDetailModalTsx2() {
        return {
          opacity: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [0, 1, 2], [1, 0.6, 0], _reactNativeReanimated.Extrapolation.CLAMP)
        };
      };
      jewelPrivilegesDetailModalTsx2.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animationProgress: animationProgress,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      jewelPrivilegesDetailModalTsx2.__workletHash = 8795999407311;
      jewelPrivilegesDetailModalTsx2.__initData = _worklet_8795999407311_init_data;
      return jewelPrivilegesDetailModalTsx2;
    }());
    var animatedShadowStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var jewelPrivilegesDetailModalTsx3 = function jewelPrivilegesDetailModalTsx3() {
        return {
          opacity: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [0, 1], [0, 1], _reactNativeReanimated.Extrapolation.CLAMP),
          zIndex: 0
        };
      };
      jewelPrivilegesDetailModalTsx3.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animationProgress: animationProgress,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      jewelPrivilegesDetailModalTsx3.__workletHash = 871361928310;
      jewelPrivilegesDetailModalTsx3.__initData = _worklet_871361928310_init_data;
      return jewelPrivilegesDetailModalTsx3;
    }());
    var sparkleAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var jewelPrivilegesDetailModalTsx4 = function jewelPrivilegesDetailModalTsx4() {
        return {
          zIndex: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [0, (heightHeader - heightImageHeader) * 3 / 4, heightHeader - heightImageHeader], [0, -2, -2], _reactNativeReanimated.Extrapolation.CLAMP),
          opacity: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [0, 0.5, 1], [0.4, 0.4, 1], _reactNativeReanimated.Extrapolation.CLAMP)
        };
      };
      jewelPrivilegesDetailModalTsx4.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animationProgress: animationProgress,
        heightHeader: heightHeader,
        heightImageHeader: heightImageHeader,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      jewelPrivilegesDetailModalTsx4.__workletHash = 12667552368819;
      jewelPrivilegesDetailModalTsx4.__initData = _worklet_12667552368819_init_data;
      return jewelPrivilegesDetailModalTsx4;
    }());
    (0, _react.useEffect)(function () {
      _jewelPrivilegesDetailControler.default.setModalRef(modalRef);
    }, []);
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var modalStyle = {
      marginHorizontal: 0,
      marginBottom: 0,
      marginTop: _reactNative2.Platform.select({
        ios: 60,
        android: (0, _utils.handleCondition)(inset == null ? undefined : inset.top, (inset == null ? undefined : inset.top) + 5, 25)
      }),
      overflow: "hidden",
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10
    };
    var marginBottomValue = (0, _react.useMemo)(function () {
      if (heightBottomView + heightHeader >= heightBgImage) return heightBottomView + heightHeader - heightBgImage + 50;
      return 0;
    }, [heightBottomView]);
    (0, _react.useImperativeHandle)(modalRef, function () {
      return {
        show: function show(nav) {
          setNewNavigation(nav);
          setModalVisible(true);
          isModalOpenRef.current = true;
          dispatch(_privilegesRedux.default.setJewelPrivilegesDetailModalOpenning(true));
        },
        hide: function hide() {
          setModalVisible(false);
          isModalOpenRef.current = false;
          dispatch(_privilegesRedux.default.setJewelPrivilegesDetailModalOpenning(false));
        }
      };
    }, []);
    var closeScreen = function closeScreen() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppJewelPrivilege, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppJewelPrivilege, "Close"));
      dispatch(_privilegesRedux.default.setJewelPrivilegesDetailModalOpenning(false));
      _jewelPrivilegesDetailControler.default.hideModal();
    };
    var checkConnection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch.isConnected;
        setConnected(isConnectedNetInfo);
        return isConnectedNetInfo;
      });
      return function checkConnection() {
        return _ref2.apply(this, arguments);
      };
    }();
    var onReLoad = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var isConnectedNetInfo = yield checkConnection();
        if (isConnectedNetInfo) {
          dispatch(_privilegesRedux.default.getPrivilegesDetailRequest());
        }
      });
      return function onReLoad() {
        return _ref3.apply(this, arguments);
      };
    }();
    var viewAllPrivilegesOnpress = function viewAllPrivilegesOnpress() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppJewelPrivilege, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppJewelPrivilege, (privilegesDetailData == null ? undefined : privilegesDetailData.viewAllPrivilegesAndDealCtaText) || ""));
      var _privilegesDetailData = privilegesDetailData == null ? undefined : privilegesDetailData.viewAllPrivilegesAndDealCtaNavigation,
        type = _privilegesDetailData.type,
        value = _privilegesDetailData.value;
      if (!type || !value) return;
      closeScreen();
      handleNavigation(type, value, {});
    };
    var viewTermsAndConditionOnpress = function viewTermsAndConditionOnpress() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppJewelPrivilege, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppJewelPrivilege, (privilegesDetailData == null ? undefined : privilegesDetailData.viewTermsAndConditionsCtaText) || ""));
      var _privilegesDetailData2 = privilegesDetailData == null ? undefined : privilegesDetailData.viewTermsAndConditionsCtaNavigation,
        type = _privilegesDetailData2.type,
        value = _privilegesDetailData2.value;
      if (!type || !value) return;
      closeScreen();
      handleNavigation(type, value, {});
    };
    var itemCardOnpress = function itemCardOnpress(cardItem) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppJewelPrivilege, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppJewelPrivilege, `Playpass | ${cardItem == null ? undefined : cardItem.title}`));
      closeScreen();
      getPlayPassUrl(_constants.StateCode.PPEVENT, cardItem == null ? undefined : cardItem.playPassPackageCode, {
        entryPoint: "Explore Changi",
        eventName: cardItem == null ? undefined : cardItem.title
      });
    };
    var onLayoutBottom = function onLayoutBottom(event) {
      var height = event.nativeEvent.layout.height;
      setHeightBottomView(height);
    };
    var onModalHide = function onModalHide() {
      animationProgress.value = 0;
      dispatch(_privilegesRedux.default.resetPrivilegesDetailData());
    };
    var onModalShow = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var isConnectedNetInfo = yield checkConnection();
        if (isConnectedNetInfo) {
          dispatch(_privilegesRedux.default.getPrivilegesDetailRequest());
        }
      });
      return function onModalShow() {
        return _ref4.apply(this, arguments);
      };
    }();
    var onReloadPrivilegesDetail = function onReloadPrivilegesDetail() {
      var _get2 = (0, _lodash.get)(ehr21, "navigationFirst", {}),
        type = _get2.type,
        value = _get2.value;
      if (type) {
        handleNavigation(type, value);
        return;
      }
      onReLoad();
    };
    (0, _react.useEffect)(function () {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(!!modalVisible);
    }, [modalVisible]);
    (0, _react.useEffect)(function () {
      return function () {
        (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
      };
    }, []);
    var renderModalContent = function renderModalContent() {
      var _privilegesDetailData3;
      if (!isConnected) {
        return (0, _jsxRuntime.jsx)(_errorOverlayVariant2.ErrorOverlayVariant3, {
          reload: true,
          onReload: onReLoad
        });
      }
      if (getPrivilegesDetailLoading) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _jewelPrivilegesDetailStyles.styles.loadingContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: [_jewelPrivilegesDetailStyles.styles.headerContainerStyle, {
              justifyContent: "flex-end"
            }],
            children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              navigationRoute: navigationRoute,
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: _jewelPrivilegesDetailStyles.styles.cardImageStyles
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _jewelPrivilegesDetailStyles.styles.bottomLoadingView,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              navigationRoute: navigationRoute,
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: _jewelPrivilegesDetailStyles.styles.textLoading1
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              navigationRoute: navigationRoute,
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: lighterGreyLoadingColors,
              shimmerStyle: _jewelPrivilegesDetailStyles.styles.textLoading2
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _jewelPrivilegesDetailStyles.styles.cardLoadingContainer,
              children: cardArrLoading == null ? undefined : cardArrLoading.map(function (index) {
                return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _jewelPrivilegesDetailStyles.styles.cardItemLoadingContainer,
                  children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                    navigationRoute: navigationRoute,
                    duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                    shimmerColors: lighterGreyLoadingColors,
                    shimmerStyle: _jewelPrivilegesDetailStyles.styles.imageCardLoading
                  }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                    style: _jewelPrivilegesDetailStyles.styles.rightCardLoading,
                    children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                      navigationRoute: navigationRoute,
                      duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                      shimmerColors: lighterGreyLoadingColors,
                      shimmerStyle: _jewelPrivilegesDetailStyles.styles.rightCard1Loading
                    }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                      navigationRoute: navigationRoute,
                      duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                      shimmerColors: lighterGreyLoadingColors,
                      shimmerStyle: _jewelPrivilegesDetailStyles.styles.rightCard2Loading
                    })]
                  })]
                }, index);
              })
            })]
          })]
        });
      }
      if (getPrivilegesDetailError) {
        return (0, _jsxRuntime.jsx)(_errorOverlayVariant.ErrorOverlayVariant2, {
          title: ehr21 == null ? undefined : ehr21.header,
          message: ehr21 == null ? undefined : ehr21.subHeader,
          reloadText: ehr21 == null ? undefined : ehr21.buttonLabel,
          reload: true,
          onReload: onReloadPrivilegesDetail,
          icon: ehr21 == null ? undefined : ehr21.icon
        });
      }
      return (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        style: _jewelPrivilegesDetailStyles.styles.flex1,
        showsVerticalScrollIndicator: false,
        bounces: false,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
          barStyle: "light-content"
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [_jewelPrivilegesDetailStyles.styles.backgroundImageStyle, {
            marginBottom: marginBottomValue
          }],
          children: (0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
            source: _backgrounds.BottomBG,
            imageStyle: [_jewelPrivilegesDetailStyles.styles.backgroundImageStyle, {
              marginBottom: marginBottomValue
            }],
            children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
              style: headerOpacityStyles,
              children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
                source: _backgrounds.TopGradient,
                style: _jewelPrivilegesDetailStyles.styles.headerOpacityStyles
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: [_jewelPrivilegesDetailStyles.styles.headerContainerStyle],
              children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
                style: [_jewelPrivilegesDetailStyles.styles.jpcSparklesContainer, sparkleAnimatedStyle],
                children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
                  source: _JPC_sparkles.default,
                  style: {
                    flex: 1
                  },
                  ref: lottieViewRef,
                  loop: false,
                  duration: 750
                })
              }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
                style: [_jewelPrivilegesDetailStyles.styles.cardImageViewStyles, animatedCardStyle],
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  activeOpacity: 1,
                  children: (privilegesDetailData == null ? undefined : privilegesDetailData.cardImage) && (0, _jsxRuntime.jsx)(_baseImage.default, {
                    source: {
                      uri: (0, _screenHelper.getUriImage)(privilegesDetailData == null ? undefined : privilegesDetailData.cardImage)
                    },
                    style: _jewelPrivilegesDetailStyles.styles.cardImageStyles,
                    onLoad: function onLoad() {
                      var _lottieViewRef$curren;
                      (_lottieViewRef$curren = lottieViewRef.current) == null || _lottieViewRef$curren.play();
                      animationProgress.value = (0, _reactNativeReanimated.withSequence)((0, _reactNativeReanimated.withTiming)(1, {
                        duration: 1200
                      }), (0, _reactNativeReanimated.withTiming)(2, {
                        duration: 200
                      }));
                    }
                  })
                })
              }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
                style: [_jewelPrivilegesDetailStyles.styles.cardShadowStyles, animatedShadowStyle],
                children: (0, _jsxRuntime.jsx)(_baseImage.default, {
                  source: _backgrounds.Shadow,
                  style: _jewelPrivilegesDetailStyles.styles.cardShadowImageStyles
                })
              })]
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: _jewelPrivilegesDetailStyles.styles.contentContainerStyles,
              activeOpacity: 1,
              children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _jewelPrivilegesDetailStyles.styles.contentViewStyles,
                onLayout: onLayoutBottom,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  text: privilegesDetailData == null ? undefined : privilegesDetailData.title,
                  style: _jewelPrivilegesDetailStyles.styles.titleTextStyles,
                  numberOfLines: 2
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  text: privilegesDetailData == null ? undefined : privilegesDetailData.caption,
                  style: _jewelPrivilegesDetailStyles.styles.captionTextStyles,
                  numberOfLines: 4,
                  preset: "caption1Italic"
                }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                  style: _jewelPrivilegesDetailStyles.styles.buttonLinearStyles,
                  start: {
                    x: 0,
                    y: 1
                  },
                  end: {
                    x: 1,
                    y: 0
                  },
                  colors: activeGradient,
                  children: (0, _jsxRuntime.jsx)(_button.Button, {
                    text: privilegesDetailData == null ? undefined : privilegesDetailData.viewAllPrivilegesAndDealCtaText,
                    sizePreset: "large",
                    textPreset: "buttonLarge",
                    typePreset: "secondary",
                    statePreset: "default",
                    backgroundPreset: "light",
                    onPress: viewAllPrivilegesOnpress,
                    textStyle: _jewelPrivilegesDetailStyles.styles.viewAllPrivilegesAndDealTextStyles
                  })
                }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  style: _jewelPrivilegesDetailStyles.styles.viewTermsAndConditionsButton,
                  onPress: viewTermsAndConditionOnpress,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    text: privilegesDetailData == null ? undefined : privilegesDetailData.viewTermsAndConditionsCtaText,
                    style: _jewelPrivilegesDetailStyles.styles.viewTermsAndConditionsCtaTextStyles,
                    numberOfLines: 2
                  })
                }), !(0, _lodash.isEmpty)(privilegesDetailData == null ? undefined : privilegesDetailData.cards) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: _jewelPrivilegesDetailStyles.styles.cardsContainer,
                  children: privilegesDetailData == null || (_privilegesDetailData3 = privilegesDetailData.cards) == null ? undefined : _privilegesDetailData3.map(function (cardItem, index) {
                    return (0, _jsxRuntime.jsx)(_itemCard.default, {
                      cardItem: cardItem,
                      index: index,
                      itemCardOnpress: itemCardOnpress
                    });
                  })
                }), !(0, _utils.ifAllTrue)([(0, _lodash.isEmpty)(privilegesDetailData == null ? undefined : privilegesDetailData.touristPerksCardIcon), (0, _lodash.isEmpty)(privilegesDetailData == null ? undefined : privilegesDetailData.touristPerksCardText), (0, _lodash.isEmpty)(privilegesDetailData == null ? undefined : privilegesDetailData.touristPerksCardText)]) && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _jewelPrivilegesDetailStyles.styles.touristPerksCardStyles,
                  children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
                    style: _jewelPrivilegesDetailStyles.styles.touristPerksCardTitleContainer,
                    children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
                      source: {
                        uri: (0, _screenHelper.getUriImage)(privilegesDetailData == null ? undefined : privilegesDetailData.touristPerksCardIcon)
                      },
                      resizeMode: "cover",
                      style: _jewelPrivilegesDetailStyles.styles.touristIconStyles
                    }), (0, _jsxRuntime.jsx)(_textGradient.default, {
                      style: _jewelPrivilegesDetailStyles.styles.touristPerksTextStyles,
                      locations: [0, 1],
                      colors: [_theme.color.palette.blueLightest, _theme.color.palette.yellowLightest],
                      start: {
                        x: 0,
                        y: 0
                      },
                      end: {
                        x: 1,
                        y: 0
                      },
                      text: privilegesDetailData == null ? undefined : privilegesDetailData.touristPerksCardText,
                      numberOfLines: 2
                    })]
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    text: privilegesDetailData == null ? undefined : privilegesDetailData.touristPerksCardCaption,
                    preset: "caption1Italic",
                    style: _jewelPrivilegesDetailStyles.styles.touristPerksCardCaptionStyles
                  })]
                })]
              })
            })]
          })
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNativeModal.default, {
      isVisible: modalVisible,
      onModalWillHide: function onModalWillHide() {
        _jewelPrivilegesDetailControler.default.hideModal();
      },
      onModalShow: onModalShow,
      onModalHide: onModalHide,
      style: modalStyle,
      backdropColor: _theme.color.palette.black,
      backdropOpacity: 0.5,
      swipeDirection: "down",
      animationInTiming: TRANSITION_TIMING,
      animationOutTiming: TRANSITION_TIMING,
      backdropTransitionInTiming: TRANSITION_TIMING,
      backdropTransitionOutTiming: TRANSITION_TIMING,
      onSwipeComplete: closeScreen,
      propagateSwipe: true,
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: _jewelPrivilegesDetailStyles.styles.closeButtonStyles,
        onPress: closeScreen,
        children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
          width: 24,
          height: 24,
          fill: "currentColor",
          style: closeIconStyles
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _jewelPrivilegesDetailStyles.styles.modalStyles,
        children: renderModalContent()
      })]
    });
  };
  var _default = exports.default = (0, _react.forwardRef)(JewelPrivilegesDetailModal);
