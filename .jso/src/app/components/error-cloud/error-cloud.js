  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorCloudComponent = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _get3 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _button = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _errorCode = _$$_REQUIRE(_dependencyMap[11]);
  var _envParams = _$$_REQUIRE(_dependencyMap[12]);
  var _i18n = _$$_REQUIRE(_dependencyMap[13]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ErrorCloudComponent = exports.ErrorCloudComponent = function ErrorCloudComponent(_ref) {
    var title = _ref.title,
      content = _ref.content,
      onPress = _ref.onPress,
      style = _ref.style,
      titleStyle = _ref.titleStyle,
      contentTextStyle = _ref.contentTextStyle,
      buttonText = _ref.buttonText,
      buttonStyle = _ref.buttonStyle,
      isLink = _ref.isLink,
      linkTx = _ref.linkTx,
      errorData = _ref.errorData,
      onLinkPress = _ref.onLinkPress,
      testID = _ref.testID,
      skipStatusbar = _ref.skipStatusbar;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      iconUri = _useState2[0],
      setIconUri = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isLoadingIcon = _useState4[0],
      setLoadingIcon = _useState4[1];
    var _useState5 = (0, _react.useState)({
        width: 0,
        height: 0,
        resizeMode: "contain"
      }),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      iconStyle = _useState6[0],
      setIconStyle = _useState6[1];
    var errorSection = errorData != null && errorData.length ? errorData.find(function (el) {
      return el.code === _errorCode.ERROR_HANDLING_CODE.ERROR_SECTION_LEVEL;
    }) : null;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("ERROR_CLOUD"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var handlePressButton = function handlePressButton() {
      var _get2 = (0, _get3.default)(errorSection, "navigationFirst", {}),
        type = _get2.type,
        value = _get2.value;
      if (type) {
        handleNavigation(type, value);
        return;
      }
      onPress == null || onPress();
    };
    (0, _react.useEffect)(function () {
      var isMounted = true;
      if (!(errorSection != null && errorSection.icon) && (errorSection == null ? undefined : errorSection.icon) === "") {
        if (isMounted) {
          setLoadingIcon(true);
        }
      } else if (errorSection != null && errorSection.icon) {
        var _env, _env2;
        setIconUri(`${(_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL}${errorSection == null ? undefined : errorSection.icon}`);
        _reactNative.Image.getSize(`${(_env2 = (0, _envParams.env)()) == null ? undefined : _env2.AEM_URL}${errorSection == null ? undefined : errorSection.icon}`, function (width, height) {
          var maxWidth = 191;
          var newWidth = width;
          var newHeight = height;
          var rate = width / height;
          if (width > maxWidth) {
            newWidth = maxWidth;
            newHeight = maxWidth / rate;
          }
          if (isMounted) {
            setIconStyle({
              width: newWidth,
              height: newHeight,
              resizeMode: "contain"
            });
          }
        });
        if (isMounted) {
          setLoadingIcon(false);
        }
      } else {
        if (isMounted) {
          setLoadingIcon(false);
        }
      }
      return function () {
        isMounted = false;
      };
    }, [errorSection]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: [styles.containerStyle, style],
      children: [skipStatusbar ? null : (0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
        barStyle: "light-content"
      }), !isLoadingIcon && (errorSection != null && errorSection.icon && iconUri ? (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: iconUri
        },
        style: iconStyle
      }) : (0, _jsxRuntime.jsx)(_icons.ErrorCloud, {
        width: "191",
        height: "104"
      })), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h2",
        text: title || (errorSection == null ? undefined : errorSection.header) || (0, _i18n.translate)("errorOverlay.variantSection.title"),
        style: [styles.titleStyle, titleStyle]
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "caption1Regular",
        text: content || (errorSection == null ? undefined : errorSection.subHeader) || (0, _i18n.translate)("errorOverlay.variantSection.message"),
        style: [styles.textStyle, contentTextStyle]
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: [styles.buttonStyle, buttonStyle],
        start: {
          x: 0,
          y: 1
        },
        end: {
          x: 1,
          y: 0
        },
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "secondary",
          statePreset: "default",
          backgroundPreset: "light",
          text: buttonText || (errorSection == null ? undefined : errorSection.buttonLabel) || (0, _i18n.translate)("errorOverlay.variantSection.reload"),
          style: {
            borderColor: "transparent"
          },
          onPress: handlePressButton,
          testID: `${testID}__ButtonRetry`,
          accessibilityLabel: `${testID}__ButtonRetry`
        })
      }), isLink && (0, _jsxRuntime.jsx)(_button.Button, {
        typePreset: "textLink",
        statePreset: "default",
        sizePreset: "small",
        tx: linkTx,
        textPreset: "textLink",
        style: styles.linkStyle,
        onPress: onLinkPress,
        testID: `${testID}__ButtonPressLink`,
        accessibilityLabel: `${testID}__ButtonPressLink`
      })]
    });
  };
