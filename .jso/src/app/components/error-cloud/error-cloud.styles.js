  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.titleStyle = exports.textStyle = exports.linkStyle = exports.containerStyle = exports.buttonStyle = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var containerStyle = exports.containerStyle = {
    alignItems: "center",
    justifyContent: "center",
    // height: "100%",
    minHeight: 400,
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var titleStyle = exports.titleStyle = {
    marginTop: 50
  };
  var textStyle = exports.textStyle = {
    marginTop: 12,
    paddingHorizontal: 62,
    textAlign: "center",
    color: _theme.color.palette.darkestGrey,
    width: 440
  };
  var buttonStyle = exports.buttonStyle = {
    borderRadius: 60,
    paddingHorizontal: 24,
    marginTop: 34,
    width: "85%"
  };
  var linkStyle = exports.linkStyle = {
    marginTop: 25
  };
