  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerTransportCard: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1
    },
    containerTypeThreeTransportCard: {
      flex: 1
    },
    containerTextTransportCard: {
      flex: 1,
      marginHorizontal: 8
    },
    textTransportCard: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left"
    },
    textDaskestGrey: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "left"
    }
  });
