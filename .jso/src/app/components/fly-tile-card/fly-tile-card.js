  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _flyTileCard = _$$_REQUIRE(_dependencyMap[5]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[6]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var FlyTileCard = function FlyTileCard(_ref) {
    var icon = _ref.icon,
      title = _ref.title,
      desc = _ref.desc,
      navigationTiles = _ref.navigationTiles,
      redirect = _ref.redirect,
      tileTag = _ref.tileTag,
      onSendTrackingData = _ref.onSendTrackingData;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FLIGHT_DETAIL"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var onPress = function onPress() {
      onSendTrackingData == null || onSendTrackingData(title);
      handleNavigation(navigationTiles == null ? undefined : navigationTiles.type, navigationTiles == null ? undefined : navigationTiles.value, redirect);
    };
    if (tileTag === _flightDetail.SectionTileTagNameEnum.WITH_SUB_CATEGORY) {
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _flyTileCard.styles.containerTypeThreeTransportCard,
        onPress: onPress,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: desc,
          preset: "caption2Regular",
          style: [_flyTileCard.styles.textDaskestGrey, {
            marginBottom: 8
          }]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _flyTileCard.styles.containerTransportCard,
          children: [icon, (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _flyTileCard.styles.containerTextTransportCard,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              text: title,
              preset: "caption1Bold",
              style: _flyTileCard.styles.textTransportCard
            })
          })]
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: _flyTileCard.styles.containerTransportCard,
      disabled: !navigationTiles && !(redirect != null && redirect.redirectTarget),
      onPress: onPress,
      children: [icon, (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _flyTileCard.styles.containerTextTransportCard,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          preset: !!desc && tileTag === _flightDetail.SectionTileTagNameEnum.LONG_DESIGN ? "bodyTextBold" : "caption1Bold",
          style: _flyTileCard.styles.textTransportCard
        }), !!desc && tileTag === _flightDetail.SectionTileTagNameEnum.LONG_DESIGN && (0, _jsxRuntime.jsx)(_text.Text, {
          text: desc,
          preset: "caption1Regular",
          style: [_flyTileCard.styles.textDaskestGrey, {
            marginTop: 4
          }]
        })]
      })]
    });
  };
  var _default = exports.default = FlyTileCard;
