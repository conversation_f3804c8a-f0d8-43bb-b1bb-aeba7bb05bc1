  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    backgroundTierStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 6,
      height: 12,
      justifyContent: "center",
      marginRight: 4,
      padding: 2,
      width: 12
    },
    bottomCardContainer: {
      alignItems: "center",
      flexDirection: "row",
      marginTop: 8
    },
    bottomContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      flex: 1,
      marginBottom: 17,
      marginLeft: 16,
      marginRight: 16,
      marginTop: 16
    },
    coreTextStyle: {
      color: _theme.color.palette.almostBlackGrey
    },
    dateTextStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 8
    },
    earnChangiRewardspointsImage: {
      alignItems: "center",
      bottom: 0,
      flexDirection: "row",
      height: 28,
      left: 0,
      paddingHorizontal: 8,
      position: "absolute",
      right: 0
    },
    earnChangiRewardspointsText: Object.assign({}, _text.presets.caption2Regular, {
      color: _theme.color.palette.whiteGrey
    }),
    eventCardContainer: {
      alignSelf: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      elevation: 5,
      flexDirection: "column",
      marginBottom: 5,
      marginTop: 11,
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        height: 0,
        width: 6
      },
      shadowOpacity: 0.16,
      shadowRadius: 20,
      width: width - 48
    },
    image: {
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: (width - 48) * 0.428,
      width: width - 48
    },
    imageContainer: {
      backgroundColor: _theme.color.transparent,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: "auto",
      overflow: "hidden"
    },
    imageTierStyle: {
      height: 8,
      width: 8
    },
    locationTextStyles: {
      flex: 1
    },
    perkContainer: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.baseBlue,
      borderRadius: 4,
      flexDirection: "row",
      paddingHorizontal: 8,
      paddingVertical: 5,
      zIndex: 1
    },
    perkTextStyles: Object.assign({}, _text.presets.XSmallBold, {
      color: _theme.color.palette.whiteGrey,
      marginLeft: 5
    })
  });
