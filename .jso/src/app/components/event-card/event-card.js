  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[8]);
  var _eventHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[10]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _icons = _$$_REQUIRE(_dependencyMap[12]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _backgrounds = _$$_REQUIRE(_dependencyMap[15]);
  var _eventCard = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var loadingElementsLayout = [{
    backgroundColor: _theme.color.palette.baseRed,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    height: 116
  }, {
    width: 223,
    height: 13,
    marginTop: 19,
    marginLeft: 14,
    borderRadius: 4
  }, {
    width: 151,
    height: 13,
    marginTop: 15,
    marginLeft: 13,
    borderRadius: 4
  }, {
    width: 80,
    height: 13,
    marginTop: 15,
    marginLeft: 13,
    borderRadius: 4,
    marginBottom: 19
  }];
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _eventCard.styles.imageContainer,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: _eventCard.styles.image
        })
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: loadingElementsLayout[1]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: loadingElementsLayout[2]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: loadingElementsLayout[3]
      })]
    });
  };
  var defaultView = function defaultView(props) {
    var type = props.type,
      categoryType = props.categoryType,
      title = props.title,
      location = props.location,
      eventStart = props.eventStart,
      eventEnd = props.eventEnd,
      imageUrl = props.imageUrl,
      ticketPrices = props.ticketPrices,
      onPressed = props.onPressed,
      tokenType = props.tokenType,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "EventCard" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "EventCard" : _props$accessibilityL,
      earnCrPointFlg = props.earnCrPointFlg;
    var isEvent = categoryType === _exploreItemType.ExploreItemCategoryEnum.event;
    var dateString = "";
    var smallDot = "";
    var ticketPrice = "";
    var useEventEligible = earnCrPointFlg === "yes";
    if (isEvent) {
      dateString = (0, _dateTime.eventCardDateFormatting)(eventStart, eventEnd);
      smallDot = (0, _constants.getDotUnicode)();
      ticketPrice = (0, _eventHelper.eventCardPrice)(ticketPrices);
    }
    var locationText = (0, _eventHelper.eventCardLocation)(location);
    var ticketPriceText = ticketPrice ? ` ${smallDot} ${ticketPrice}` : "";
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: onPressed,
      disabled: type === _exploreItemType.ExploreItemTypeEnum.loading,
      testID: `${testID}__TouchableItemEventCard`,
      accessibilityLabel: `${accessibilityLabel}__TouchableItemEventCard`,
      accessible: false,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _eventCard.styles.imageContainer,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          style: _eventCard.styles.image,
          source: {
            uri: imageUrl
          },
          resizeMode: "stretch"
        }), useEventEligible && (0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
          source: _backgrounds.CardRibbon,
          style: _eventCard.styles.earnChangiRewardspointsImage,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _eventCard.styles.backgroundTierStyle,
            children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
              source: _icons.ButterflyMember,
              style: _eventCard.styles.imageTierStyle,
              resizeMode: "cover"
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: "Earn Changi Rewards points",
            style: _eventCard.styles.earnChangiRewardspointsText
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _eventCard.styles.bottomContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          numberOfLines: 1,
          preset: "bodyTextBold",
          style: _eventCard.styles.coreTextStyle,
          accessible: true
        }), isEvent && (0, _jsxRuntime.jsx)(_text.Text, {
          text: `${dateString}${ticketPriceText}`,
          numberOfLines: 1,
          preset: "bodyTextRegular",
          style: _eventCard.styles.dateTextStyle
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _eventCard.styles.bottomCardContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _eventCard.styles.locationTextStyles,
            children: !!locationText && (0, _jsxRuntime.jsx)(_text.Text, {
              text: locationText,
              numberOfLines: 1,
              preset: "caption1Regular"
            })
          }), !(0, _isEmpty.default)(tokenType) && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _eventCard.styles.perkContainer,
            children: [(0, _jsxRuntime.jsx)(_icons.StarPerk, {
              color: _theme.color.palette.whiteGrey,
              height: 11,
              width: 11
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: "PERKS",
              style: _eventCard.styles.perkTextStyles
            })]
          })]
        })]
      })]
    });
  };
  var EventCard = function EventCard(props) {
    var isLoading = props.type === _exploreItemType.ExploreItemTypeEnum.loading;
    var eventCardContainerStyles = props.eventCardContainerStyles;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: [_eventCard.styles.eventCardContainer, Object.assign({}, eventCardContainerStyles)],
      children: isLoading ? loadingView() : defaultView(props)
    });
  };
  var _default = exports.default = _react.default.memo(EventCard);
