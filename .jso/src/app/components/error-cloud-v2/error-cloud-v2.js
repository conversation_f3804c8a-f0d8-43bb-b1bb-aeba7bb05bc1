  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorCloudComponentV2 = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text2 = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var ErrorCloudComponentV2 = exports.ErrorCloudComponentV2 = function ErrorCloudComponentV2(_ref) {
    var title = _ref.title,
      content = _ref.content,
      onPress = _ref.onPress,
      style = _ref.style,
      titleStyle = _ref.titleStyle,
      contentTextStyle = _ref.contentTextStyle,
      buttonText = _ref.buttonText,
      _ref$hideButton = _ref.hideButton,
      hideButton = _ref$hideButton === undefined ? false : _ref$hideButton;
    var handlePressButton = function handlePressButton() {
      onPress == null || onPress();
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: [styles.container, style],
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.iconContainer,
        children: (0, _jsxRuntime.jsx)(_icons.ErrorCloud, {
          width: "191",
          height: "104"
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.textContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h2",
          text: title || (0, _i18n.translate)("errorOverlay.variantSection.title"),
          style: [styles.title, titleStyle]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          text: content || (0, _i18n.translate)("errorOverlay.variantSection.message"),
          style: [styles.contentText, contentTextStyle]
        })]
      }), !hideButton && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: handlePressButton,
        style: styles.button,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: [_text2.presets.bodyTextBold, styles.buttonText],
          text: buttonText || (0, _i18n.translate)("errorOverlay.variantSection.reload")
        })
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      alignItems: "center",
      justifyContent: "center",
      minHeight: 400,
      backgroundColor: _theme.color.palette.lightestGrey,
      rowGap: 24
    },
    iconContainer: {
      width: 200,
      height: 200,
      alignItems: "center",
      justifyContent: "center"
    },
    textContainer: {
      alignItems: "center",
      rowGap: 16
    },
    title: {
      marginTop: 0
    },
    contentText: {
      textAlign: "center",
      paddingHorizontal: 24,
      color: _theme.color.palette.almostBlackGrey
    },
    button: {
      width: "100%",
      alignItems: "center"
    },
    buttonText: {
      color: _theme.color.palette.gradientColor1Start
    }
  });
