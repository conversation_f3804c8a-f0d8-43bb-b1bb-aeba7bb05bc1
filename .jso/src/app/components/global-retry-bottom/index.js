  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _theme = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _button = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var VIEW_WARNING_POSITION = 35;
  var AnimatedPressable = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.Pressable);
  var _worklet_10879868577201_init_data = {
    code: "function indexTsx1(finished){const{runOnJS,showDone}=this.__closure;if(finished){runOnJS(showDone)();}}"
  };
  var _worklet_14319035440850_init_data = {
    code: "function indexTsx2(finished){const{runOnJS,hideDone}=this.__closure;if(finished){runOnJS(hideDone)();}}"
  };
  var _worklet_394839106712_init_data = {
    code: "function indexTsx3(){const{interpolate,animationProgress,Extrapolation}=this.__closure;return{position:'absolute',backgroundColor:'rgba(0, 0, 0, 0.6)',opacity:interpolate(animationProgress.value,[0,1],[0,1],Extrapolation.CLAMP),top:0,right:0,left:0,bottom:0,justifyContent:'flex-end',zIndex:20};}"
  };
  var _worklet_617165496122_init_data = {
    code: "function indexTsx4(){const{interpolate,animationProgress,containerHeight,VIEW_WARNING_POSITION}=this.__closure;return{transform:[{translateY:interpolate(animationProgress.value,[1,2],[containerHeight.value+VIEW_WARNING_POSITION,0])}]};}"
  };
  var GlobalRetryBottom = function GlobalRetryBottom() {
    var loadingRef = (0, _react.useRef)(null);
    var retryFunction = (0, _react.useRef)(null);
    var animationProgress = (0, _reactNativeReanimated.useSharedValue)(0);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isVisible = _useState2[0],
      setVisible = _useState2[1];
    var _useState3 = (0, _react.useState)(""),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      retryButtonLabel = _useState4[0],
      setRetryButtonLabel = _useState4[1];
    var containerHeight = (0, _reactNativeReanimated.useSharedValue)(0);
    (0, _react.useEffect)(function () {
      _globalLoadingController.default.setGlobalTryBottomRef(loadingRef);
    }, []);
    var showDone = function showDone() {
      setVisible(true);
    };
    var hideDone = function hideDone() {
      setVisible(false);
      setRetryButtonLabel("");
    };
    var showModal = function showModal() {
      console.log(animationProgress.value);
      animationProgress.value = (0, _reactNativeReanimated.withTiming)(2, {
        duration: 500
      }, function () {
        var indexTsx1 = function indexTsx1(finished) {
          if (finished) {
            (0, _reactNativeReanimated.runOnJS)(showDone)();
          }
        };
        indexTsx1.__closure = {
          runOnJS: _reactNativeReanimated.runOnJS,
          showDone: showDone
        };
        indexTsx1.__workletHash = 10879868577201;
        indexTsx1.__initData = _worklet_10879868577201_init_data;
        return indexTsx1;
      }());
    };
    var hideModal = function hideModal() {
      animationProgress.value = (0, _reactNativeReanimated.withTiming)(0, {
        duration: 500
      }, function () {
        var indexTsx2 = function indexTsx2(finished) {
          if (finished) {
            (0, _reactNativeReanimated.runOnJS)(hideDone)();
          }
        };
        indexTsx2.__closure = {
          runOnJS: _reactNativeReanimated.runOnJS,
          hideDone: hideDone
        };
        indexTsx2.__workletHash = 14319035440850;
        indexTsx2.__initData = _worklet_14319035440850_init_data;
        return indexTsx2;
      }());
    };
    (0, _react.useImperativeHandle)(loadingRef, function () {
      return {
        open: showModal,
        close: hideModal,
        setRetryFunction: function setRetryFunction(current) {
          retryFunction.current = current;
        },
        setRetryLabel: function setRetryLabel(label) {
          setRetryButtonLabel(label);
        }
      };
    }, []);
    var animatedContainerStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx3 = function indexTsx3() {
        return {
          position: 'absolute',
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          opacity: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [0, 1], [0, 1], _reactNativeReanimated.Extrapolation.CLAMP),
          top: 0,
          right: 0,
          left: 0,
          bottom: 0,
          justifyContent: 'flex-end',
          zIndex: 20
        };
      };
      indexTsx3.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animationProgress: animationProgress,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      indexTsx3.__workletHash = 394839106712;
      indexTsx3.__initData = _worklet_394839106712_init_data;
      return indexTsx3;
    }(), []);
    var animatedBottomSheetStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var indexTsx4 = function indexTsx4() {
        return {
          transform: [{
            translateY: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [1, 2], [containerHeight.value + VIEW_WARNING_POSITION, 0])
          }]
        };
      };
      indexTsx4.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animationProgress: animationProgress,
        containerHeight: containerHeight,
        VIEW_WARNING_POSITION: VIEW_WARNING_POSITION
      };
      indexTsx4.__workletHash = 617165496122;
      indexTsx4.__initData = _worklet_617165496122_init_data;
      return indexTsx4;
    }(), []);
    var onRetry = function onRetry() {
      hideModal();
      if (retryFunction.current) {
        var _retryFunction$curren;
        retryFunction.current.apply(retryFunction, (0, _toConsumableArray2.default)((retryFunction == null || (_retryFunction$curren = retryFunction.current) == null ? undefined : _retryFunction$curren.arguments) || []));
      }
    };
    return (0, _jsxRuntime.jsx)(AnimatedPressable, {
      onPress: hideModal,
      style: [animatedContainerStyle, {
        pointerEvents: isVisible ? 'auto' : 'none'
      }],
      children: (0, _jsxRuntime.jsxs)(AnimatedPressable, {
        style: [styles.containerStyle, animatedBottomSheetStyle],
        onLayout: function onLayout(e) {
          return containerHeight.value = e.nativeEvent.layout.height;
        },
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.iconStyle,
          children: (0, _jsxRuntime.jsx)(_icons.InfoRed, {})
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.dismissIconContainer,
          onPress: hideModal,
          testID: `RetryBottom__TouchableClose`,
          accessibilityLabel: `RetryBottom_AccessibilityLabel__TouchableCrossClose`,
          children: (0, _jsxRuntime.jsx)(_icons.CrossBlue, {})
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.textContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.titleStyle,
            tx: "popupError.somethingWrongOneline"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.messageStyle,
            tx: "popupError.networkErrorMessage"
          })]
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: styles.buttonStyle,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "secondary",
            text: retryButtonLabel || "Retry",
            statePreset: "default",
            backgroundPreset: "light",
            onPress: onRetry,
            testID: `RetryBottom__ButtonExecuteAndClose`,
            accessibilityLabel: `RetryBottom__ButtonExecuteAndClose`
          })
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    containerStyle: {
      paddingHorizontal: 24,
      marginHorizontal: 8,
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopEndRadius: 16,
      paddingBottom: 48
    },
    titleStyle: Object.assign({}, _text.presets.h2, {
      lineHeight: 28,
      textAlign: "center",
      marginBottom: 16
    }),
    messageStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      textAlign: "center",
      color: _theme.color.palette.darkestGrey
    }),
    textContainerStyle: {
      marginVertical: 30
    },
    iconStyle: {
      position: "absolute",
      top: -35,
      alignItems: "center",
      justifyContent: "center",
      width: 70,
      height: 70,
      borderRadius: 50
    },
    buttonStyle: {
      width: "100%",
      borderRadius: 60
    },
    dismissIconContainer: {
      alignSelf: "flex-end",
      marginTop: 15
    }
  });
  var _default = exports.default = (0, _react.forwardRef)(GlobalRetryBottom);
