  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ForcedUpdateDialogCpay = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[1]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeVersionInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _utils = _$$_REQUIRE(_dependencyMap[5]);
  var _systemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _eventHelper = _$$_REQUIRE(_dependencyMap[7]);
  var _changipay = _$$_REQUIRE(_dependencyMap[8]);
  var _panResponder = _$$_REQUIRE(_dependencyMap[9]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _loginHelper = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ForcedUpdateDialogCpay = exports.ForcedUpdateDialogCpay = function ForcedUpdateDialogCpay() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var forcedUpdateFetching = (0, _reactRedux.useSelector)(_systemRedux.SystemSelectors.forcedUpdateCpayFetching);
    var forcedUpdatePayload = (0, _reactRedux.useSelector)(_systemRedux.SystemSelectors.forcedUpdateCpayPayload);
    var _useCPay = (0, _changipay.useCPay)(),
      openNativeCpay = _useCPay.openNativeCpay;
    var _useContext = (0, _react.useContext)(_panResponder.PanResponderContext),
      forceUpdateHandling = _useContext.forceUpdateHandling,
      currentForceUpdateFlow = _useContext.currentForceUpdateFlow;
    var popupRef = (0, _react.useRef)(null);
    var isOpen = (0, _react.useRef)(false);
    var isNPSOpen = (0, _react.useRef)(false);
    (0, _react.useEffect)(function () {
      _globalLoadingController.default.setCpayForceUpdateRef(popupRef);
    }, []);
    (0, _react.useImperativeHandle)(popupRef, function () {
      return {
        show: function show() {},
        hide: function hide() {}
      };
    }, []);
    var checkOpen = function checkOpen() {
      isNPSOpen.current = true;
    };
    (0, _react.useEffect)(function () {
      _loginHelper.biometricEventEmitter.on("nps_displayed", checkOpen);
      return function () {
        _loginHelper.biometricEventEmitter.off("nps_displayed", checkOpen);
      };
    }, []);
    var closeForcedUpdatePopup = function closeForcedUpdatePopup() {
      if (currentForceUpdateFlow.current === "CPAY") {
        forceUpdateHandling(null);
      }
      isOpen.current = false;
      dispatch(_systemRedux.default.forcedUpdateShow(false));
    };
    var showForcedUpdatePopup = function showForcedUpdatePopup(data) {
      forceUpdateHandling("CPAY");
      if (isOpen.current) {
        if (!isNPSOpen) return;
        isNPSOpen.current = false;
      }
      isOpen.current = true;
      _reactNative.Alert.alert(data == null ? undefined : data.title, data == null ? undefined : data.description, data == null ? undefined : data.buttons);
      dispatch(_systemRedux.default.forcedUpdateShow(true));
    };
    var updateNow = function updateNow() {
      isOpen.current = false;
      (0, _eventHelper.navigateToStore)();
    };
    var reminderAfterForce = function reminderAfterForce() {
      closeForcedUpdatePopup();
    };
    var reminderAfterHours = function reminderAfterHours() {
      if (_reactNative.Platform.OS === 'android') {
        openNativeCpay();
        closeForcedUpdatePopup();
      } else {
        closeForcedUpdatePopup();
        setTimeout(function () {
          openNativeCpay();
        }, 500);
      }
    };
    (0, _react.useEffect)(function () {
      if ((0, _utils.ifAllTrue)([!forcedUpdateFetching, forcedUpdatePayload])) {
        var _forcedUpdatePayload$, _forcedUpdatePayload$2;
        var forcedUpdateData = forcedUpdatePayload == null || (_forcedUpdatePayload$ = forcedUpdatePayload["forced-update"]) == null ? undefined : _forcedUpdatePayload$.find(function (item) {
          return item.platform === _reactNative.Platform.OS && item.fragmentName === `cpay_forced_${_reactNative.Platform.OS}`;
        });
        var recommendedUpdateData = forcedUpdatePayload == null || (_forcedUpdatePayload$2 = forcedUpdatePayload["recommended"]) == null ? undefined : _forcedUpdatePayload$2.find(function (item) {
          return item.platform === _reactNative.Platform.OS && item.fragmentName === `cpay_recommended_${_reactNative.Platform.OS}`;
        });
        if (forcedUpdateData) {
          var shouldDisplayForced = checkForcedCPay(forcedUpdateData);
          if (shouldDisplayForced) {
            return;
          }
        }
        if (recommendedUpdateData) {
          checkRecommenedCPay(recommendedUpdateData);
        } else {
          openNativeCpay();
        }
      }
    }, [forcedUpdatePayload, forcedUpdateFetching]);
    var checkForcedCPay = function checkForcedCPay(forcedUpdateData) {
      var shouldDisplay = false;
      var compareVer = (0, _utils.compareVersion)(_reactNativeVersionInfo.default.appVersion, forcedUpdateData == null ? undefined : forcedUpdateData.targetVersion);
      if (compareVer < 0) {
        var information = {
          title: forcedUpdateData == null ? undefined : forcedUpdateData.title,
          description: forcedUpdateData == null ? undefined : forcedUpdateData.description,
          buttons: [{
            bold: true,
            text: forcedUpdateData == null ? undefined : forcedUpdateData.button1Label,
            onPress: function onPress() {
              return updateNow();
            }
          }, {
            text: forcedUpdateData == null ? undefined : forcedUpdateData.button2Label,
            onPress: function onPress() {
              return reminderAfterForce();
            }
          }]
        };
        showForcedUpdatePopup(information);
        shouldDisplay = true;
      }
      return shouldDisplay;
    };
    var checkRecommenedCPay = function checkRecommenedCPay(recommendedUpdateData) {
      var compareVer = (0, _utils.compareVersion)(_reactNativeVersionInfo.default.appVersion, recommendedUpdateData == null ? undefined : recommendedUpdateData.targetVersion);
      if (compareVer < 0) {
        var information = {
          title: recommendedUpdateData == null ? undefined : recommendedUpdateData.title,
          description: recommendedUpdateData == null ? undefined : recommendedUpdateData.description,
          buttons: [{
            text: recommendedUpdateData == null ? undefined : recommendedUpdateData.button1Label,
            style: 'cancel',
            onPress: function onPress() {
              return updateNow();
            }
          }, {
            text: recommendedUpdateData == null ? undefined : recommendedUpdateData.button2Label,
            onPress: function onPress() {
              return reminderAfterHours();
            }
          }]
        };
        showForcedUpdatePopup(information);
      } else {
        openNativeCpay();
      }
    };
    return null;
  };
