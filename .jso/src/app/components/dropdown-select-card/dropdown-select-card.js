  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _dropdownSelectCard = _$$_REQUIRE(_dependencyMap[9]);
  var _downArrow = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var parentContainer = {
    width: width - (0, _reactNativeSizeMatters.scale)(73)
  };
  var cardContainer = {
    backgroundColor: _theme.color.palette.whiteGrey,
    elevation: 5,
    borderRadius: 16,
    shadowColor: _theme.color.palette.almostBlackGrey,
    shadowOpacity: 0.2,
    shadowRadius: 20,
    shadowOffset: {
      height: 6,
      width: 0
    }
  };
  var container = {
    paddingStart: 16,
    paddingVertical: 16,
    overflow: "hidden"
  };
  var titleStyle = {
    color: _theme.color.palette.almostBlackGrey,
    flex: 2
  };
  var imageStyle = {
    height: 24,
    width: 24
  };
  var imageContainerStyle = {
    flex: 0.2,
    marginEnd: 16
  };
  var defaultViewStyle = {
    flexDirection: "row",
    alignItems: "center"
  };
  var silverLoadingColors = [_theme.color.palette.silver, _theme.color.background, _theme.color.palette.silver];
  var loadingContainer = Object.assign({}, defaultViewStyle);
  var loadingStyle = {
    height: 18,
    borderRadius: 4
  };
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: loadingContainer,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: titleStyle,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: silverLoadingColors,
          shimmerStyle: loadingStyle
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: imageContainerStyle,
        children: (0, _jsxRuntime.jsx)(_downArrow.default, {
          style: imageStyle
        })
      })]
    });
  };
  var defaultView = function defaultView(props) {
    var title = props.title;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: defaultViewStyle,
      children: [title && title.length > 0 ? (0, _jsxRuntime.jsx)(_text.Text, {
        numberOfLines: 1,
        tx: title,
        preset: "bodyTextBold",
        style: titleStyle
      }) : (0, _jsxRuntime.jsx)(_text.Text, {
        numberOfLines: 1,
        text: (0, _i18n.translate)("dropdownSelectCard.imTravellingOnThisFlight"),
        preset: "bodyTextBold",
        style: titleStyle
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: imageContainerStyle,
        children: (0, _jsxRuntime.jsx)(_downArrow.default, {
          style: imageStyle
        })
      })]
    });
  };
  var DropdownSelectCard = function DropdownSelectCard(props) {
    var isLoading = props.type === _dropdownSelectCard.DropdownSelectCardType.loading;
    var style = props.style;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: [Object.assign({}, parentContainer), style],
      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: cardContainer,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: props.onPressed,
          disabled: isLoading,
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: container,
            children: isLoading ? loadingView() : defaultView(props)
          })
        })
      })
    });
  };
  var _default = exports.default = DropdownSelectCard;
