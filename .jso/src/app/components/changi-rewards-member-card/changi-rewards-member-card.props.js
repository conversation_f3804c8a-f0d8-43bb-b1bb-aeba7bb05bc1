  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Tier = exports.ChangiRewardsMemberCardType = undefined;
  var ChangiRewardsMemberCardType = exports.ChangiRewardsMemberCardType = /*#__PURE__*/function (ChangiRewardsMemberCardType) {
    ChangiRewardsMemberCardType["default"] = "default";
    ChangiRewardsMemberCardType["loading"] = "loading";
    ChangiRewardsMemberCardType["upgrade"] = "upgrade";
    return ChangiRewardsMemberCardType;
  }({}); // export enum Tier {
  //   Member,
  //   StaffMember,
  //   Gold,
  //   StaffGold,
  //   Platinum,
  //   StaffPlatinum,
  //   Classic,
  //   Premium,
  //   Elite,
  // }
  var Tier = exports.Tier = /*#__PURE__*/function (Tier) {
    Tier["Member"] = "Member";
    Tier["StaffMember"] = "StaffMember";
    Tier["Gold"] = "Gold";
    Tier["StaffGold"] = "StaffGold";
    Tier["Platinum"] = "Platinum";
    Tier["StaffPlatinum"] = "StaffPlatinum";
    Tier["Classic"] = "Classic";
    Tier["Premium"] = "Premium";
    Tier["Elite"] = "Elite";
    Tier["Monarch"] = "Monarch";
    Tier["StaffMonarch"] = "StaffMonarch";
    return Tier;
  }({});
