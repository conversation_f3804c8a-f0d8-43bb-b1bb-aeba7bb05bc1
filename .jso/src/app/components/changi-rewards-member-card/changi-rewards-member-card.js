  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ChangiRewardsMemberCard = ChangiRewardsMemberCard;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var React = _react;
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _button = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _slider = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[11]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[13]);
  var _utils = _$$_REQUIRE(_dependencyMap[14]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[15]);
  var _getConfigurationPermission = _$$_REQUIRE(_dependencyMap[16]);
  var _htmlRichtext = _$$_REQUIRE(_dependencyMap[17]);
  var _text2 = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var memberGradientColor = ["rgba(105, 80, 161, 1)", "rgba(178, 141, 193, 1)"];
  var goldGradientColor = ["rgba(148, 124, 78, 1)", "rgba(178, 158, 120, 1)"];
  var platinumGradientColor = ["rgba(41, 52, 60, 1)", "rgba(69, 99, 116, 1)"];
  var classicGradientColor = ["rgba(0, 106, 133, 1)", "rgba(42, 168, 200, 1)"];
  var premiumGradientColor = ["rgba(1, 85, 152, 1)", "rgba(41, 133, 206, 1)"];
  var eliteGradientColor = ["rgba(69, 69, 69, 1)", "rgba(155, 155, 155, 1)"];
  var staffMemberGradientColor = ["rgba(105, 80, 161, 1)", "rgba(178, 141, 193, 1)"];
  var staffGoldGradientColor = ["rgba(148, 124, 78, 1)", "rgba(178, 158, 120, 1)"];
  var staffPlatinumGradientColor = ["rgba(41, 52, 60, 1)", "rgba(69, 99, 116, 1)"];
  var gradientDisabledTheme = ["#E5E5E5", "#E5E5E5"];
  var memberTextColor = "#7A35B0";
  var goldTextColor = "#947C4E";
  var platinumTextColor = "#29343C";
  var staffMemberTextColor = "#7A35B0";
  var staffGoldTextColor = "#947C4E";
  var staffPlatinumTextColor = "#29343C";
  var classicTextColor = "#0483A4";
  var premiumTextColor = "#015598";
  var eliteTextColor = "#454545";
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var containerStyle = Object.assign({}, _reactNative2.Platform.select({
    ios: {
      shadowRadius: 20,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 6
      }
    },
    android: {
      elevation: 3
    }
  }), {
    borderRadius: 17,
    width: "100%",
    backgroundColor: _theme.color.palette.whiteGrey,
    alignSelf: "center",
    flex: 1
  });
  var containerDisabledStyle = Object.assign({}, _reactNative2.Platform.select({
    ios: {
      shadowRadius: 20,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 6
      }
    },
    android: {
      elevation: 3
    }
  }), {
    borderRadius: 17,
    width: "100%",
    backgroundColor: _theme.color.palette.whiteGrey,
    alignSelf: "center",
    flex: 1,
    height: 160
  });
  var gradientContainerStyle = {
    width: "100%",
    height: 40,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    justifyContent: "center"
  };
  var titleStyle = {
    color: _theme.color.palette.whiteGrey,
    marginLeft: 16,
    letterSpacing: 2.4,
    textTransform: "uppercase"
  };
  var pointsTokenContainerView = {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    paddingHorizontal: 16,
    marginTop: 7
  };
  var pointsContainerStyle = {
    flexDirection: "row",
    alignItems: "flex-end",
    width: "50%"
  };
  var pointTextStyle = {
    marginBottom: -2
  };
  var pointsLabelTextMarginStyle = {
    marginLeft: 3
  };
  var qualificationTextStyle = Object.assign({}, _text2.presets.caption2Regular, {
    color: _theme.color.palette.almostBlackGrey,
    marginLeft: 16,
    marginRight: 24,
    marginTop: 16,
    marginBottom: 24
  });
  var upgradeButtonStyle = {
    alignSelf: "flex-start",
    paddingHorizontal: 12,
    marginHorizontal: 16,
    marginTop: 11,
    marginBottom: 17
  };
  var skeletonContainerStyle = Object.assign({
    width: "100%",
    borderRadius: 8
  }, _theme.shadow.secondaryShadow);
  var skeletonHeaderStyle = {
    width: "100%",
    height: 40,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    justifyContent: "center",
    backgroundColor: _theme.color.palette.lighterGrey
  };
  var skeletonImageStyle = {
    backgroundColor: _theme.color.background,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8
  };
  var midGreyLoadingColors = [_theme.color.palette.midGrey, _theme.color.background, _theme.color.palette.midGrey];
  var lightGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayoutImage = [{
    height: 18,
    width: 126,
    borderRadius: 4,
    marginLeft: 16
  }, {
    height: 18,
    width: 82,
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 18
  }, {
    height: 5,
    borderRadius: 12,
    marginLeft: 16,
    marginTop: 18,
    width: "90%"
  }, {
    height: 10,
    width: 161,
    borderRadius: 4,
    marginLeft: 16,
    marginTop: 20,
    marginBottom: 31
  }];
  var wrapDisabledInfor = {
    paddingHorizontal: 16,
    marginTop: 8
  };
  var captionDisabled = {
    color: _theme.color.palette.almostBlackGrey,
    textAlign: "left",
    marginBottom: 4
  };
  var descriptionDisabled = {
    color: _theme.color.palette.almostBlackGrey
  };
  var imageBackgroundJewelCard = {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    flex: 1
  };
  var titleDisabledStyle = {
    color: "#999999",
    marginLeft: 16,
    letterSpacing: 2.4,
    textTransform: "uppercase"
  };
  var overLayLoading = {
    width: width,
    height: height,
    position: "absolute"
  };
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: skeletonContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: skeletonHeaderStyle,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: midGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[0]
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: skeletonImageStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[2]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lightGreyLoadingColors,
          shimmerStyle: skeletonLayoutImage[3]
        })]
      })]
    });
  };
  var handleGradientTheme = function handleGradientTheme(tier) {
    var theme;
    var textColor;
    switch (tier) {
      case _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Gold]:
        theme = goldGradientColor;
        textColor = goldTextColor;
        break;
      case _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.StaffGold]:
        theme = staffGoldGradientColor;
        textColor = staffGoldTextColor;
        break;
      case _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Platinum]:
        theme = platinumGradientColor;
        textColor = platinumTextColor;
        break;
      case _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.StaffPlatinum]:
        theme = staffPlatinumGradientColor;
        textColor = staffPlatinumTextColor;
        break;
      case _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Classic]:
        theme = classicGradientColor;
        textColor = classicTextColor;
        break;
      case _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Premium]:
        theme = premiumGradientColor;
        textColor = premiumTextColor;
        break;
      case _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Elite]:
        theme = eliteGradientColor;
        textColor = eliteTextColor;
        break;
      case _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.StaffMember]:
        theme = staffMemberGradientColor;
        textColor = staffMemberTextColor;
        break;
      default:
        theme = memberGradientColor;
        textColor = memberTextColor;
        break;
    }
    return {
      theme: theme,
      textColor: textColor
    };
  };
  var defaultView = function defaultView(props) {
    var points = props.points,
      membershipText = props.membershipText,
      currentTierInfo = props.currentTierInfo,
      icon = props.icon,
      type = props.type,
      onUpgradePressed = props.onUpgradePressed,
      upgradeButtonText = props.upgradeButtonText,
      onPressed = props.onPressed,
      disabled = props.disabled,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ChangiRewardsMemberCard" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ChangiRewardsMemberCard" : _props$accessibilityL,
      disableEntryCR = props.disableEntryCR,
      onCheckEntryPoint = props.onCheckEntryPoint,
      currentTierNettPercent = props.currentTierNettPercent;
    var rewardPoints = (0, _react.useMemo)(function () {
      if (points) {
        var pointSubstr = points.substring(0, 8);
        if (points.length > 10) {
          return `${pointSubstr}...`;
        }
        return points;
      }
      return "";
    }, [points]);
    var currentTierInfoCorrect = currentTierInfo == null ? undefined : currentTierInfo.replace(" ", "");
    var isMember = currentTierInfoCorrect === _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Member];
    var isGold = currentTierInfoCorrect === _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Gold];
    var isPlatinum = currentTierInfoCorrect === _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Platinum];
    var isStaffMember = currentTierInfoCorrect === _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.StaffMember];
    var isStaffGold = currentTierInfoCorrect === _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.StaffGold];
    var isStaffPlatinum = currentTierInfoCorrect === _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.StaffPlatinum];
    var isElite = currentTierInfoCorrect === _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Elite];
    var isClassic = currentTierInfoCorrect === _changiRewardsMemberCard.Tier[_changiRewardsMemberCard.Tier.Classic];
    var isChangiRewards = isMember || isGold || isPlatinum || isStaffMember || isStaffPlatinum || isStaffGold;
    var _React$useState = React.useState(false),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      loadingGetConfig = _React$useState2[0],
      setLoadingGetConfig = _React$useState2[1];
    var _useGetConfigurationP = (0, _getConfigurationPermission.useGetConfigurationPermissionHelper)(),
      getConfigApp = _useGetConfigurationP.getConfigApp,
      notifyDisableChangiRewards = _useGetConfigurationP.notifyDisableChangiRewards;
    var tierTextMiddle = (0, _utils.simpleCondition)({
      condition: isChangiRewards,
      ifValue: (0, _i18n.translate)("changiRewardsMemberCard.gold"),
      elseValue: (0, _i18n.translate)("changiRewardsMemberCard.premium")
    });
    var tierTextRight = (0, _utils.simpleCondition)({
      condition: isChangiRewards,
      ifValue: (0, _i18n.translate)("changiRewardsMemberCard.platinum"),
      elseValue: (0, _i18n.translate)("changiRewardsMemberCard.elite")
    });
    var colorTheme = handleGradientTheme(currentTierInfoCorrect);
    var gradientTheme = colorTheme == null ? undefined : colorTheme.theme;
    var pointsLabelTextStyle = {
      color: colorTheme == null ? undefined : colorTheme.textColor
    };
    var pointsNumberTextStyle = {
      color: colorTheme == null ? undefined : colorTheme.textColor
    };
    var upgradeButtonTheme = {
      borderColor: gradientTheme == null ? undefined : gradientTheme[0],
      color: gradientTheme == null ? undefined : gradientTheme[0]
    };
    var isUpgrade = type === _changiRewardsMemberCard.ChangiRewardsMemberCardType.upgrade;
    var pointsText = (0, _utils.simpleCondition)({
      condition: isUpgrade,
      ifValue: {
        condition: isElite && isUpgrade,
        ifValue: (0, _i18n.translate)("changiRewardsMemberCard.elite"),
        elseValue: (0, _i18n.translate)("changiRewardsMemberCard.premium")
      },
      elseValue: rewardPoints
    });
    var qualificationStyle = (0, _utils.simpleCondition)({
      condition: isUpgrade,
      ifValue: Object.assign({}, qualificationTextStyle, {
        marginTop: 7,
        marginBottom: 0
      }),
      elseValue: Object.assign({}, qualificationTextStyle)
    });
    var titleText = (0, _utils.simpleCondition)({
      condition: isChangiRewards,
      ifValue: (0, _i18n.translate)("changiRewardsMemberCard.changiRewards"),
      elseValue: (0, _i18n.translate)("changiRewardsMemberCard.changiRewardsTravel")
    });
    var getProgressValue = function getProgressValue() {
      return (currentTierNettPercent || 0) / 100;
    };
    var handlePointText = function handlePointText(point) {
      try {
        return parseFloat(point);
      } catch (e) {
        return 0;
      }
    };
    var handlePress = function handlePress() {
      getConfigApp({
        configKey: _constants.AppConfigPermissionTypes.changiappCREnabled,
        onTap: function onTap() {
          return setLoadingGetConfig(true);
        },
        callbackSuccess: function callbackSuccess() {
          onCheckEntryPoint(false);
          setLoadingGetConfig(false);
          onPressed == null || onPressed();
        },
        callbackFailure: function callbackFailure() {
          onCheckEntryPoint(true);
          setLoadingGetConfig(false);
          notifyDisableChangiRewards();
        }
      });
    };
    if (disableEntryCR) {
      return (0, _jsxRuntime.jsxs)(React.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: containerDisabledStyle,
          disabled: false,
          onPress: handlePress,
          testID: `${testID}__TouchableRewardMemberCard`,
          accessibilityLabel: `${testID}__TouchableRewardMemberCard`,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: imageBackgroundJewelCard,
            children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              style: gradientContainerStyle,
              start: {
                x: 0.3558,
                y: 0
              },
              end: {
                x: 0.9436,
                y: 0
              },
              colors: gradientDisabledTheme,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                preset: "caption2Bold",
                style: titleDisabledStyle,
                children: "CHANGI REWARDS"
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: wrapDisabledInfor,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                text: "We\u2019re currently unable to retrieve your membership details",
                preset: "caption1Bold",
                style: captionDisabled
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                text: "Please try again later.",
                preset: "caption2Regular",
                style: descriptionDisabled
              })]
            })]
          })
        }), loadingGetConfig && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: overLayLoading,
          children: (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
            visible: loadingGetConfig
          })
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(React.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: containerStyle,
        disabled: isUpgrade || disabled,
        onPress: handlePress,
        testID: `${testID}__TouchableRewardMemberCard`,
        accessibilityLabel: `${accessibilityLabel}__TouchableRewardMemberCard`,
        children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: gradientContainerStyle,
          start: {
            x: 0.3558,
            y: 0
          },
          end: {
            x: 0.9436,
            y: 0
          },
          colors: gradientTheme,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption2Bold",
            style: titleStyle,
            children: titleText
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: pointsTokenContainerView,
          children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: pointsContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              preset: "h3",
              style: [pointTextStyle, pointsNumberTextStyle],
              numberOfLines: 1,
              children: pointsText || "0"
            }), !isUpgrade && (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "caption1Regular",
              style: [pointsLabelTextStyle, pointsLabelTextMarginStyle],
              numberOfLines: 1,
              tx: handlePointText(pointsText) > 0 ? "changiRewardsMemberCard.points" : "changiRewardsMemberCard.point"
            })]
          })
        }), !isUpgrade && (0, _jsxRuntime.jsx)(_slider.default, {
          value: getProgressValue(),
          thumbImage: icon,
          minimumTrackTintColor: gradientTheme,
          maximumTrackTintColor: _theme.color.palette.lightGrey,
          tierTextMiddle: !isClassic && tierTextMiddle,
          tierTextRight: isClassic ? tierTextMiddle : tierTextRight,
          testID: `${testID}__Slider`,
          accessibilityLabel: `${accessibilityLabel}__Slider`
        }), (0, _jsxRuntime.jsx)(_htmlRichtext.HtmlRichtext, {
          style: qualificationStyle,
          value: `<p>${membershipText}</p>`,
          numberOfLines: 1
        }), isUpgrade && (0, _jsxRuntime.jsx)(_button.Button, {
          text: upgradeButtonText.toUpperCase(),
          typePreset: "secondary",
          onPress: onUpgradePressed,
          sizePreset: "medium",
          statePreset: "default",
          backgroundPreset: "light",
          textPreset: "buttonExtraSmall",
          textStyle: upgradeButtonTheme,
          style: [upgradeButtonStyle, upgradeButtonTheme],
          testID: `${testID}__ButtonRewardMemberCardUpdrade`,
          accessibilityLabel: `${accessibilityLabel}__ButtonRewardMemberCardUpdrade`
        })]
      }), loadingGetConfig && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: overLayLoading,
        children: (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
          visible: loadingGetConfig
        })
      })]
    });
  };
  function ChangiRewardsMemberCard(props) {
    var isLoading = _changiRewardsMemberCard.ChangiRewardsMemberCardType.loading === props.type;
    return isLoading ? loadingView() : defaultView(props);
  }
