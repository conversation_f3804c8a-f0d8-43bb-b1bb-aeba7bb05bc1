  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_changiRewardsMemberCard).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _changiRewardsMemberCard[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _changiRewardsMemberCard[key];
      }
    });
  });
  var _changiRewardsMemberCard2 = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_changiRewardsMemberCard2).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _changiRewardsMemberCard2[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _changiRewardsMemberCard2[key];
      }
    });
  });
