  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _button = _$$_REQUIRE(_dependencyMap[5]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: 24
    },
    description: {
      marginTop: 16
    },
    first_action: {
      marginTop: 24
    },
    first_action_button: {
      paddingHorizontal: 40
    },
    icon: {},
    iconImage: {
      height: 190,
      overlayColor: _theme.color.palette.whiteGrey,
      width: 190
    },
    second_action: {
      marginTop: 24
    },
    textDescription: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    },
    textFirstActionButton: {
      color: _theme.color.palette.lightPurple
    },
    title: {
      marginTop: 45
    },
    titleEmpty: {
      textAlign: "center"
    },
    wrapIconImage: {
      borderRadius: 95,
      height: 190,
      overflow: "hidden",
      width: 190
    }
  });
  var EmptyScreen = function EmptyScreen(_ref) {
    var icon = _ref.icon,
      iconUrl = _ref.iconUrl,
      title = _ref.title,
      description = _ref.description,
      firstAction = _ref.firstAction,
      titleComponent = _ref.titleComponent,
      firstActionPress = _ref.firstActionPress,
      secondAction = _ref.secondAction,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? "EmptyScreen" : _ref$testID,
      _ref$accessibilityLab = _ref.accessibilityLabel,
      accessibilityLabel = _ref$accessibilityLab === undefined ? "EmptyScreen" : _ref$accessibilityLab,
      _ref$firstActionStyle = _ref.firstActionStyles,
      firstActionStyles = _ref$firstActionStyle === undefined ? {} : _ref$firstActionStyle,
      _ref$titleStyles = _ref.titleStyles,
      titleStyles = _ref$titleStyles === undefined ? {} : _ref$titleStyles,
      _ref$descriptionStyle = _ref.descriptionStyles,
      descriptionStyles = _ref$descriptionStyle === undefined ? {} : _ref$descriptionStyle;
    var renderTitle = (0, _react.useMemo)(function () {
      if (titleComponent) {
        return titleComponent;
      }
      if (!(0, _isEmpty.default)(description)) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [styles.title, titleStyles],
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: title,
            preset: "h2",
            style: styles.titleEmpty
          })
        });
      }
    }, [titleComponent, description, titleStyles]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [!icon && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.icon,
        children: icon
      }), (0, _isEmpty.default)(icon) && iconUrl && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.wrapIconImage,
        children: (0, _jsxRuntime.jsx)(_baseImage.default, {
          style: styles.iconImage,
          source: iconUrl,
          resizeMode: "cover"
        })
      }), renderTitle, !(0, _isEmpty.default)(description) && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [styles.description, descriptionStyles],
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: description,
          preset: "caption1Regular",
          style: styles.textDescription
        })
      }), !(0, _isEmpty.default)(firstAction) && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.first_action,
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          style: [styles.first_action_button, firstActionStyles],
          typePreset: "secondary",
          textPreset: "buttonLarge",
          textStyle: styles.textFirstActionButton,
          text: firstAction,
          onPress: function onPress() {
            return firstActionPress();
          },
          testID: `${testID}__ButtonFirstAction`,
          accessibilityLabel: `${accessibilityLabel}__ButtonFirstAction`
        })
      }), !(0, _isEmpty.default)(secondAction) && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.second_action,
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          text: secondAction,
          onPress: function onPress() {
            return null;
          },
          testID: `${testID}__ButtonSecondAction`,
          accessibilityLabel: `${accessibilityLabel}__ButtonSecondAction`
        })
      })]
    });
  };
  var _default = exports.default = EmptyScreen;
