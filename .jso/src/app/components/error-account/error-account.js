  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorAccountComponent = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  var _errorAccount = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "ErrorAccountComponent";
  var ErrorAccountComponent = exports.ErrorAccountComponent = function ErrorAccountComponent(props) {
    var _props$title = props.title,
      title = _props$title === undefined ? (0, _i18n.translate)("forYouScreen.errorAccount.title") : _props$title,
      _props$content = props.content,
      content = _props$content === undefined ? (0, _i18n.translate)("forYouScreen.errorAccount.content") : _props$content,
      _props$subContent = props.subContent,
      subContent = _props$subContent === undefined ? (0, _i18n.translate)("forYouScreen.errorAccount.subContent") : _props$subContent,
      style = props.style;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, Object.assign({
      style: Object.assign({}, _errorAccount.default.standardContainer, style)
    }, (0, _utils.accessibility)({
      testID: `${COMPONENT_NAME}__CONTAINER`,
      accessibilityLabel: `${COMPONENT_NAME}__CONTAINER`,
      OS: _reactNative.Platform.OS
    }), {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _errorAccount.default.headerContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _errorAccount.default.headerTextContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, Object.assign({
            text: title,
            style: _errorAccount.default.titleStyle
          }, (0, _utils.accessibility)({
            testID: `${COMPONENT_NAME}__TITLE`,
            accessibilityLabel: `${COMPONENT_NAME}__TITLE`,
            OS: _reactNative.Platform.OS
          }))), (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
            text: content,
            style: _errorAccount.default.contentStyle
          }, (0, _utils.accessibility)({
            testID: `${COMPONENT_NAME}__CONTENT`,
            accessibilityLabel: `${COMPONENT_NAME}__CONTENT`,
            OS: _reactNative.Platform.OS
          })))]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _errorAccount.default.iconContainer,
          children: (0, _jsxRuntime.jsx)(_icons.IconOops, {})
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _errorAccount.default.bottomContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, Object.assign({
          text: subContent,
          style: _errorAccount.default.subContent
        }, (0, _utils.accessibility)({
          testID: `${COMPONENT_NAME}__SUB_CONTENT`,
          accessibilityLabel: `${COMPONENT_NAME}__SUB_CONTENT`,
          OS: _reactNative.Platform.OS
        })))
      })]
    }));
  };
