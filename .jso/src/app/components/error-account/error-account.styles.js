  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = _reactNative.StyleSheet.create({
    standardContainer: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      justifyContent: "center",
      paddingVertical: 25,
      margin: 24,
      padding: 24
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 16,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    headerContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 24
    },
    headerTextContainer: {
      flex: 1
    },
    iconContainer: {},
    bottomContainer: {},
    titleStyle: Object.assign({}, _text.presets.h3, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 8
    }),
    contentStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey
    }),
    subContent: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 8
    })
  });
  var _default = exports.default = styles;
