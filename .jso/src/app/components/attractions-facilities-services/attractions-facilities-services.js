  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.AttractionsFacilitiesServices = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[9]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[11]);
  var _fly = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var parentContainerWidth = width / 2 - 32;
  var imageBackgroundWidth = width / 2 - 58;
  var parentContainer = {
    width: parentContainerWidth,
    marginTop: 52,
    marginBottom: 4,
    marginHorizontal: 8
  };
  var container = Object.assign({
    backgroundColor: _theme.color.palette.whiteGrey,
    elevation: 5,
    borderRadius: 16
  }, _reactNative2.Platform.select({
    ios: {
      shadowRadius: 16,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 3
      },
      backgroundColor: _theme.color.palette.whiteGrey
    },
    android: {
      elevation: 3,
      backgroundColor: _theme.color.palette.whiteGrey
    }
  }));
  var bottomContainer = {
    padding: 12,
    height: 86
  };
  var imageBackground = {
    width: imageBackgroundWidth,
    height: 80,
    alignSelf: "center",
    marginTop: -40,
    borderRadius: 12
  };
  var skeletonImageBackground = Object.assign({}, imageBackground);
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayout = [{
    marginTop: -40,
    paddingBottom: 12
  }, {
    width: imageBackgroundWidth,
    height: 80,
    borderRadius: 12,
    alignSelf: "center"
  }, {
    width: 115,
    height: 13,
    borderRadius: 4,
    marginTop: 17,
    marginLeft: 12
  }, {
    width: 115,
    height: 13,
    borderRadius: 4,
    marginTop: 9,
    marginLeft: 12
  }, {
    width: 56,
    height: 13,
    borderRadius: 4,
    marginTop: 9,
    marginLeft: 12
  }];
  var titleStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var locationTextTwoRow = Object.assign({}, titleStyle);
  var locationTextThreeRow = Object.assign({}, titleStyle);
  var locationTextStyle = {
    marginTop: 4
  };
  var locationViewStyle = {
    marginTop: 4
  };
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: skeletonLayout[0],
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayout[1]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: skeletonLayout[2]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: skeletonLayout[3]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: skeletonLayout[4]
      })]
    });
  };
  var defaultView = function defaultView(props, opacity) {
    var imageUrl = props.imageUrl,
      title = props.title,
      type = props.type,
      locationDisplayText = props.locationDisplayText;
    var isLoading = type === _exploreItemType.ExploreItemTypeEnum.loading;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: {
        opacity: opacity
      },
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        resizeMode: "cover",
        source: {
          uri: imageUrl
        },
        style: isLoading ? skeletonImageBackground : imageBackground
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: bottomContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: locationDisplayText ? 2 : 3,
          text: title,
          preset: "bodyTextBold",
          style: locationDisplayText ? locationTextTwoRow : locationTextThreeRow
        }), locationDisplayText ? (0, _jsxRuntime.jsx)(_text.Text, {
          text: locationDisplayText,
          numberOfLines: 1,
          style: locationTextStyle,
          preset: "caption1Regular"
        }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: locationViewStyle
        })]
      })]
    });
  };
  var AttractionsFacilitiesServices = exports.AttractionsFacilitiesServices = function AttractionsFacilitiesServices(props) {
    var _useContext;
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      flyDetailsFirstFlag = _useContext$Handlers.flyDetailsFirstFlag;
    var isFlightDetailsFirst = (0, _remoteConfig.isFlagOnCondition)(flyDetailsFirstFlag);
    var isLoading = props.type === _exploreItemType.ExploreItemTypeEnum.loading;
    var _useState = (0, _react.useState)(1),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      opacity = _useState2[0],
      setOpacity = _useState2[1];
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "AttractionsFacilitiesServices" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "AttractionsFacilitiesServices" : _props$accessibilityL,
      style = props.style,
      isFlex1Fixed = props.isFlex1Fixed;
    var containerStyle = (0, _react.useMemo)(function () {
      var styles = [parentContainer, style == null ? undefined : style.parentContainer];
      if (isFlex1Fixed && isFlightDetailsFirst) {
        styles.push({
          flex: 1
        });
      }
      return styles;
    }, [parentContainer, style == null ? undefined : style.parentContainer, isFlex1Fixed, isFlightDetailsFirst]);
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: containerStyle,
      children: (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: props.onPressed,
        disabled: isLoading,
        onPressIn: function onPressIn() {
          return setOpacity(0.2);
        },
        onPressOut: function onPressOut() {
          return setOpacity(1);
        },
        testID: `${testID}__TouchableWithoutFeedback`,
        accessibilityLabel: `${accessibilityLabel}__TouchableWithoutFeedback`,
        accessible: false,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: container,
            children: isLoading ? loadingView() : defaultView(props, opacity)
          })
        })
      })
    });
  };
