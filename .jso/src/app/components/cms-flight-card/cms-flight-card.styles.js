  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    titleContainer: {
      flex: 1
    },
    textAlmostBackColor: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left"
    },
    containerExpandCollapseIcon: {
      alignSelf: "center",
      width: 100,
      alignItems: "center",
      paddingTop: 16
    },
    transportServicesRow: {
      flexDirection: "row",
      paddingVertical: 16
    },
    containerTransportCard: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1
    },
    containerDefaultLeavingTheAirport: {
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey,
      paddingVertical: 16
    },
    containerIcon: {
      width: 40,
      height: 40,
      borderRadius: 4,
      backgroundColor: _theme.color.palette.lightestGrey,
      justifyContent: "center",
      alignItems: "center"
    },
    imageStyle: {
      height: 24,
      width: 24
    },
    containerCollapseTiles: {
      flexDirection: "row",
      paddingVertical: 16
    },
    containerCollapseTilesTitle: {
      flexDirection: "row",
      flex: 1,
      marginLeft: 8
    },
    textCollapseTiles: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "left"
    },
    placeholderIcon: {
      width: 40,
      height: 40,
      borderRadius: 4,
      backgroundColor: _theme.color.palette.whiteGrey,
      justifyContent: "center",
      alignItems: "center"
    }
  });
