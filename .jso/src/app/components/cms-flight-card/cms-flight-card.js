  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.CMSFlightCard = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _cmsFlightCard = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _flyTileCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[11]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CMSFlightCard = exports.CMSFlightCard = function CMSFlightCard(_ref) {
    var section = _ref.section,
      onSendTrackingData = _ref.onSendTrackingData;
    var title = section.title,
      enableExpandCollapse = section.enableExpandCollapse,
      expandCollapse = section.expandCollapse;
    var _React$useState = _react.default.useState(expandCollapse !== _flightDetail.ExpandCollapseEnum.COLLAPSE),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      isExpanded = _React$useState2[0],
      setIsExpanded = _React$useState2[1];
    (0, _react.useEffect)(function () {
      setIsExpanded(expandCollapse !== _flightDetail.ExpandCollapseEnum.COLLAPSE);
    }, [expandCollapse]);
    var tiles = (0, _react.useMemo)(function () {
      if (section != null && section.tiles) {
        return section.tiles.sort(function (a, b) {
          return Number(a.sequenceNumber) - Number(b.sequenceNumber);
        });
      }
      return [];
    }, [section]);
    var collapTitles = (0, _react.useMemo)(function () {
      if (!tiles.length) {
        return "";
      }
      var result = "";
      tiles.forEach(function (tile, index) {
        if (index === tiles.length - 1) {
          result += `${tile.title}`;
        } else {
          result += `${tile.title}  |  `;
        }
      });
      return result;
    }, [tiles]);
    var onPressExpandCollapse = function onPressExpandCollapse() {
      setIsExpanded(function (prev) {
        return !prev;
      });
    };
    var renderIconCard = function renderIconCard(icon) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _cmsFlightCard.styles.containerIcon,
        children: (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: (0, _screenHelper.getUriImage)(icon)
          },
          style: _cmsFlightCard.styles.imageStyle,
          resizeMode: "contain"
        })
      });
    };
    var renderExpandTiles = function renderExpandTiles() {
      if ((0, _lodash.isEmpty)(tiles)) return null;
      var renderedRows = [];
      var rowQueue = []; // Temporary array to hold tiles that are not long-design

      /**
       * Flush the rowQueue:
       * - If there is only one tile in the queue (tileTag !== "long-design"), add a dummy tile
       *   to always render 2 tiles in one row.
       */
      var flushRowQueue = function flushRowQueue(keySuffix) {
        if (rowQueue.length === 0) return;
        // If there is only one non-long-design tile, add a dummy tile.
        if (rowQueue.length === 1) {
          var dummyTile = {
            icon: "",
            title: "",
            description: "",
            navigation: [],
            redirect: undefined,
            type: {
              tagName: "dummy"
            }
          };
          rowQueue.push(dummyTile);
        }
        // Render two tiles in one row
        renderedRows.push((0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _cmsFlightCard.styles.transportServicesRow,
          children: rowQueue.map(function (tile, idx) {
            return (0, _jsxRuntime.jsx)(_flyTileCard.default, {
              icon: tile.icon ? renderIconCard(tile.icon) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _cmsFlightCard.styles.placeholderIcon
              }),
              title: tile.title,
              desc: tile.description,
              navigationTiles: tile.navigation,
              redirect: tile.redirect,
              tileTag: tile.type.tagName,
              onSendTrackingData: onSendTrackingData
            }, `tile-${keySuffix}-${idx}`);
          })
        }, `row-${keySuffix}`));
        rowQueue = [];
      };

      // Loop through each tile in the list
      tiles.forEach(function (tile, index) {
        var tileTag = tile.type.tagName;
        if (tileTag === "long-design") {
          // Flush any pending group before rendering a long-design tile
          flushRowQueue(index);
          renderedRows.push((0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _cmsFlightCard.styles.containerDefaultLeavingTheAirport,
            children: (0, _jsxRuntime.jsx)(_flyTileCard.default, {
              icon: renderIconCard(tile.icon),
              title: tile.title,
              desc: tile.description,
              navigationTiles: tile.navigation,
              redirect: tile.redirect,
              tileTag: tileTag,
              onSendTrackingData: onSendTrackingData
            })
          }, `longTile-${index}`));
        } else {
          // For any tile that is not long-design, group them into rows of 2.
          rowQueue.push(tile);
          if (rowQueue.length === 2) {
            flushRowQueue(index);
          }
        }
      });

      // Flush any remaining non-long-design tile(s) in the rowQueue
      flushRowQueue("final");

      // Optionally remove the bottom border from the last row
      if (renderedRows.length > 0) {
        var lastRow = renderedRows[renderedRows.length - 1];
        renderedRows[renderedRows.length - 1] = _react.default.cloneElement(lastRow, {
          style: [lastRow.props.style, {
            borderBottomWidth: 0,
            borderBottomColor: "transparent",
            paddingBottom: 0
          }]
        });
      }
      return renderedRows;
    };
    var renderCollapseTiles = function renderCollapseTiles() {
      if ((0, _lodash.isEmpty)(tiles)) {
        return null;
      }
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _cmsFlightCard.styles.containerCollapseTiles,
        children: [(0, _jsxRuntime.jsx)(_icons.CircleArrowUpward, {}), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _cmsFlightCard.styles.containerCollapseTilesTitle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            text: collapTitles,
            style: _cmsFlightCard.styles.textCollapseTiles
          })
        })]
      });
    };
    var renderExpandCollapse = function renderExpandCollapse() {
      if (!enableExpandCollapse) {
        return null;
      }
      if (isExpanded) {
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _cmsFlightCard.styles.containerExpandCollapseIcon,
          onPress: onPressExpandCollapse,
          children: (0, _jsxRuntime.jsx)(_icons.AccordionUp, {})
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: _cmsFlightCard.styles.containerExpandCollapseIcon,
        onPress: onPressExpandCollapse,
        children: (0, _jsxRuntime.jsx)(_icons.AccordionDown, {})
      });
    };
    var renderExpandCollapseTiles = function renderExpandCollapseTiles() {
      if (isExpanded) {
        return renderExpandTiles();
      }
      return renderCollapseTiles();
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _cmsFlightCard.styles.titleContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          preset: "caption1Bold",
          numberOfLines: 1,
          style: _cmsFlightCard.styles.textAlmostBackColor
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [renderExpandCollapseTiles(), renderExpandCollapse()]
      })]
    });
  };
