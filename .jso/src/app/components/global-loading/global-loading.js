  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _utils = _$$_REQUIRE(_dependencyMap[2]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _loadingAnimation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _panResponder = _$$_REQUIRE(_dependencyMap[8]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var colorActive = "rgba(252, 252, 252, 0.8)";
  var colorTransparent = "rgba(252, 252, 252, 1)";
  var styles = _reactNative.StyleSheet.create({
    containerStyle: Object.assign({
      alignItems: "center",
      backgroundColor: colorActive,
      bottom: 0,
      flex: 1,
      justifyContent: "center",
      left: 0,
      position: "absolute",
      right: 0,
      top: 0,
      zIndex: 10
    }, _reactNative.Platform.select({
      android: {
        elevation: 5
      }
    })),
    containerStyleNoTransparent: Object.assign({
      alignItems: "center",
      backgroundColor: colorTransparent,
      bottom: 0,
      flex: 1,
      justifyContent: "center",
      left: 0,
      position: "absolute",
      right: 0,
      top: 0,
      zIndex: 10
    }, _reactNative.Platform.select({
      android: {
        elevation: 5
      }
    })),
    lottieStyle: {
      marginLeft: 5,
      width: "70%",
      height: '100%'
    }
  });
  var GlobalLoading = function GlobalLoading() {
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      visible = _useState2[0],
      setVisible = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isTransparent = _useState4[0],
      setIsTransparent = _useState4[1];
    var loadingRef = (0, _react.useRef)(null);
    var _useContext = (0, _react.useContext)(_panResponder.PanResponderContext),
      conditionTimeRef = _useContext.conditionTimeRef,
      idleTimeRef = _useContext.idleTimeRef,
      isPageLoadingRef = _useContext.isPageLoadingRef;
    (0, _react.useEffect)(function () {
      _globalLoadingController.default.setGlobalLoadingRef(loadingRef);
    }, []);
    (0, _react.useImperativeHandle)(loadingRef, function () {
      return {
        show: function show(isTransparentLoading) {
          setVisible(true);
          setIsTransparent(isTransparentLoading);
        },
        hide: function hide() {
          return setVisible(false);
        }
      };
    }, []);

    // Handle preventing app rating when loading is visible
    (0, _react.useEffect)(function () {
      isPageLoadingRef.current = !!visible;
      if (visible) {
        (0, _screenHelper.clearAppRatingTimers)({
          conditionTimeRef: conditionTimeRef,
          idleTimeRef: idleTimeRef
        });
      } else {
        (0, _screenHelper.resetInactivityTimeout)({
          conditionTimeRef: conditionTimeRef,
          idleTimeRef: idleTimeRef,
          callback: function callback() {
            return (0, _screenHook.getCurrentScreenActive)() === _constants.TrackingScreenName.Explore ? (0, _screenHelper.trackingShowRatingPopupExploreScreen)({
              isPageLoadingRef: isPageLoadingRef
            }) : (0, _screenHelper.trackingShowRatingPopup)({
              isPageLoadingRef: isPageLoadingRef
            });
          }
        });
      }
    }, [visible]);
    (0, _react.useEffect)(function () {
      return function () {
        isPageLoadingRef.current = false;
      };
    }, []);
    if (!visible) return null;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: Object.assign({}, (0, _utils.handleCondition)(isTransparent, styles.containerStyle, styles.containerStyleNoTransparent)),
      children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
        style: styles.lottieStyle,
        source: _loadingAnimation.default,
        autoPlay: true,
        loop: true
      })
    });
  };
  var _default = exports.default = (0, _react.forwardRef)(GlobalLoading);
