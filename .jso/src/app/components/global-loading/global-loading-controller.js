  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _GlobalLoadingController;
  var GlobalLoadingController = exports.default = /*#__PURE__*/(0, _createClass2.default)(function GlobalLoadingController() {
    (0, _classCallCheck2.default)(this, GlobalLoadingController);
  });
  _GlobalLoadingController = GlobalLoadingController;
  GlobalLoadingController.currentRetry = (0, _react.createRef)();
  GlobalLoadingController.forceUpdateCpayRef = (0, _react.createRef)();
  GlobalLoadingController.forceUpdateCappRef = (0, _react.createRef)();
  GlobalLoadingController.setAppRef = function (ref) {
    _GlobalLoadingController.appRef = ref;
  };
  GlobalLoadingController.setGlobalLoadingRef = function (ref) {
    _GlobalLoadingController.loadingRef = ref;
  };
  GlobalLoadingController.setGlobalTryBottomRef = function (ref) {
    _GlobalLoadingController.retryBottomRef = ref;
  };
  GlobalLoadingController.setCurrentRetry = function (value) {
    var _GlobalLoadingControl;
    (_GlobalLoadingControl = _GlobalLoadingController.retryBottomRef) == null || (_GlobalLoadingControl = _GlobalLoadingControl.current) == null || _GlobalLoadingControl.setRetryFunction(value);
  };
  GlobalLoadingController.showLoading = function (isTransparentLoading) {
    var _GlobalLoadingControl2;
    (_GlobalLoadingControl2 = _GlobalLoadingController.loadingRef) == null || (_GlobalLoadingControl2 = _GlobalLoadingControl2.current) == null || _GlobalLoadingControl2.show(isTransparentLoading);
  };
  GlobalLoadingController.hideLoading = function () {
    var _GlobalLoadingControl3;
    (_GlobalLoadingControl3 = _GlobalLoadingController.loadingRef) == null || (_GlobalLoadingControl3 = _GlobalLoadingControl3.current) == null || _GlobalLoadingControl3.hide();
  };
  GlobalLoadingController.showRetryBottomRef = function () {
    var _GlobalLoadingControl4;
    (_GlobalLoadingControl4 = _GlobalLoadingController.retryBottomRef) == null || (_GlobalLoadingControl4 = _GlobalLoadingControl4.current) == null || _GlobalLoadingControl4.open();
  };
  GlobalLoadingController.hideRetryBottomRef = function () {
    var _GlobalLoadingControl5;
    (_GlobalLoadingControl5 = _GlobalLoadingController.retryBottomRef) == null || (_GlobalLoadingControl5 = _GlobalLoadingControl5.current) == null || _GlobalLoadingControl5.close();
  };
  GlobalLoadingController.setCurrentRetryLabel = function (label) {
    var _GlobalLoadingControl6;
    (_GlobalLoadingControl6 = _GlobalLoadingController.retryBottomRef) == null || (_GlobalLoadingControl6 = _GlobalLoadingControl6.current) == null || _GlobalLoadingControl6.setRetryLabel == null || _GlobalLoadingControl6.setRetryLabel(label);
  };
  GlobalLoadingController.setCpayForceUpdateRef = function (ref) {
    _GlobalLoadingController.forceUpdateCpayRef = ref;
  };
  GlobalLoadingController.hideCpayForceUpdatePopup = function () {
    var _GlobalLoadingControl7;
    (_GlobalLoadingControl7 = _GlobalLoadingController.forceUpdateCpayRef) == null || (_GlobalLoadingControl7 = _GlobalLoadingControl7.current) == null || _GlobalLoadingControl7.hide();
  };
  GlobalLoadingController.setCappForceUpdateRef = function (ref) {
    _GlobalLoadingController.forceUpdateCappRef = ref;
  };
  GlobalLoadingController.hideCappForceUpdatePopup = function () {
    var _GlobalLoadingControl8;
    (_GlobalLoadingControl8 = _GlobalLoadingController.forceUpdateCappRef) == null || (_GlobalLoadingControl8 = _GlobalLoadingControl8.current) == null || _GlobalLoadingControl8.hide();
  };
