  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ContentGuide = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _contentGuide = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "ContentGuide";
  var parentContainer = {
    width: 200,
    flex: 1
  };
  var container = {
    backgroundColor: _theme.color.palette.whiteGrey,
    elevation: 5,
    borderRadius: 16,
    shadowColor: _theme.color.palette.almostBlackGrey,
    shadowOpacity: 0.2,
    shadowRadius: 20,
    shadowOffset: {
      height: 6,
      width: 0
    },
    flex: 1
  };
  var outerContainer = {
    paddingTop: 29,
    flex: 1
  };
  var bottomContainer = {
    padding: 12
  };
  var imageBackground = {
    width: 176,
    height: 97,
    alignSelf: "center",
    marginTop: -30,
    borderRadius: 12
  };
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonImageBackground = Object.assign({}, imageBackground, {
    borderRadius: 17
  });
  var skeletonLayout = [{
    marginTop: -29
  }, {
    width: 176,
    height: 97,
    borderRadius: 12,
    marginLeft: 12
  }, {
    width: 151,
    height: 13,
    borderRadius: 4,
    marginTop: 16,
    marginLeft: 12
  }, {
    width: 151,
    height: 13,
    borderRadius: 4,
    marginTop: 12,
    marginLeft: 12
  }, {
    width: 57,
    height: 13,
    borderRadius: 4,
    marginTop: 12,
    marginLeft: 12
  }, {
    width: 151,
    height: 13,
    borderRadius: 4,
    marginTop: 12,
    marginLeft: 12,
    marginBottom: 12
  }];
  var titleStyle = {
    color: _theme.color.palette.almostBlackGrey,
    alignSelf: "flex-start"
  };
  var bottomTextStyle = {
    paddingTop: 8,
    alignSelf: "flex-start"
  };
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: skeletonLayout[0],
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayout[1]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: skeletonLayout[2]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: skeletonLayout[3]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: skeletonLayout[4]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: skeletonLayout[5]
      })]
    });
  };
  var defaultView = function defaultView(props, opacity) {
    var image = props.imageUrl,
      title = props.title,
      description = props.description,
      source = props.source,
      type = props.type;
    var isLoading = type === _contentGuide.ContentGuideType.loading;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: {
        opacity: opacity
      },
      accessible: false,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        resizeMode: "cover",
        source: {
          uri: image
        },
        style: isLoading ? skeletonImageBackground : imageBackground,
        testID: `${COMPONENT_NAME}__Image`,
        accessibilityLabel: image
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: bottomContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: description ? 2 : 3,
          text: title,
          preset: "bodyTextBold",
          style: titleStyle,
          testID: `${COMPONENT_NAME}__Title`,
          accessibilityLabel: title
        }), description || source ? (0, _jsxRuntime.jsx)(_text.Text, {
          text: description || source,
          numberOfLines: (0, _utils.handleCondition)(description, 3, 1),
          style: bottomTextStyle,
          preset: "caption1Regular",
          testID: `${COMPONENT_NAME}__Description`,
          accessibilityLabel: description || source
        }) : null]
      })]
    });
  };
  var ContentGuide = exports.ContentGuide = function ContentGuide(props) {
    var isLoading = props.type === _contentGuide.ContentGuideType.loading;
    var _useState = (0, _react.useState)(1),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      opacity = _useState2[0],
      setOpacity = _useState2[1];
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: parentContainer,
      accessible: false,
      children: (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: props.onPressed,
        disabled: isLoading,
        onPressIn: function onPressIn() {
          return setOpacity(0.2);
        },
        onPressOut: function onPressOut() {
          return setOpacity(1);
        },
        accessible: false,
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: outerContainer,
          accessible: false,
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: container,
            children: isLoading ? loadingView() : defaultView(props, opacity)
          })
        })
      })
    });
  };
