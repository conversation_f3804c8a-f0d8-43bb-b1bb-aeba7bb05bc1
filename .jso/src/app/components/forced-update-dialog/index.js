  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ForcedUpdateDialog = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _$$_REQUIRE(_dependencyMap[2]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeVersionInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _storage = _$$_REQUIRE(_dependencyMap[6]);
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _utils = _$$_REQUIRE(_dependencyMap[8]);
  var _systemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _eventHelper = _$$_REQUIRE(_dependencyMap[10]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[11]);
  var _panResponder = _$$_REQUIRE(_dependencyMap[12]);
  var _changipay = _$$_REQUIRE(_dependencyMap[13]);
  var _qualtrics = _$$_REQUIRE(_dependencyMap[14]);
  var _envParams = _$$_REQUIRE(_dependencyMap[15]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _useAppStateChange2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ReminderHours = 0.1; // hours
  var ALERT_CHECK_VERSION_HANDLE = /*#__PURE__*/function (ALERT_CHECK_VERSION_HANDLE) {
    ALERT_CHECK_VERSION_HANDLE["UPDATE_NOW"] = "UPDATE_NOW";
    ALERT_CHECK_VERSION_HANDLE["REMIND_ME_LATER"] = "REMIND_ME_LATER";
    ALERT_CHECK_VERSION_HANDLE["CONTINUE_WITHOUT_UPDATING"] = "CONTINUE_WITHOUT_UPDATING";
    return ALERT_CHECK_VERSION_HANDLE;
  }(ALERT_CHECK_VERSION_HANDLE || {});
  var ForcedUpdateDialog = exports.ForcedUpdateDialog = function ForcedUpdateDialog() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var reminderInterval;
    var forcedUpdateFetching = (0, _reactRedux.useSelector)(_systemRedux.SystemSelectors.forcedUpdateFetching);
    var forcedUpdatePayload = (0, _reactRedux.useSelector)(_systemRedux.SystemSelectors.forcedUpdatePayload);
    var _useContext = (0, _react.useContext)(_panResponder.PanResponderContext),
      forceUpdateHandling = _useContext.forceUpdateHandling,
      isPendingCpayUpdateCheck = _useContext.isPendingCpayUpdateCheck,
      currentForceUpdateFlow = _useContext.currentForceUpdateFlow;
    var _useCPay = (0, _changipay.useCPay)(),
      openChangiPay = _useCPay.openChangiPay;
    var popupRef = (0, _react.useRef)(null);
    var isOpen = (0, _react.useRef)(false);
    var _useAppStateChange = (0, _useAppStateChange2.default)(),
      appStateVisible = _useAppStateChange.appStateVisible;
    (0, _react.useEffect)(function () {
      _globalLoadingController.default.setCappForceUpdateRef(popupRef);
    }, []);
    (0, _react.useImperativeHandle)(popupRef, function () {
      return {
        show: function show() {},
        hide: function hide() {
          dispatch(_systemRedux.default.forcedUpdateShow(false));
          if (currentForceUpdateFlow.current === "CAPP") {
            forceUpdateHandling(null);
          }
        }
      };
    }, []);
    var checkAppSettingsVersion = function checkAppSettingsVersion(forcedUpdateData) {
      var currentAppSettingsVersion = (0, _mmkvStorage.getAppSettingVersion)();
      if (forcedUpdateData && forcedUpdateData != null && forcedUpdateData.appSettingsVersion && currentAppSettingsVersion) {
        var compareVer = (0, _utils.compareVersion)(forcedUpdateData == null ? undefined : forcedUpdateData.appSettingsVersion, currentAppSettingsVersion);
        if (compareVer === 0) {
          (0, _mmkvStorage.setAppSettingNeedToRefreshStatus)(false);
        } else {
          (0, _mmkvStorage.setAppSettingVersion)(forcedUpdateData == null ? undefined : forcedUpdateData.appSettingsVersion);
          (0, _mmkvStorage.setAppSettingNeedToRefreshStatus)(true);
        }
      } else if (forcedUpdateData && forcedUpdateData != null && forcedUpdateData.appSettingsVersion && !currentAppSettingsVersion) {
        (0, _mmkvStorage.setAppSettingVersion)(forcedUpdateData == null ? undefined : forcedUpdateData.appSettingsVersion);
        (0, _mmkvStorage.setAppSettingNeedToRefreshStatus)(false);
      } else {
        (0, _mmkvStorage.setAppSettingNeedToRefreshStatus)(true);
      }
    };
    (0, _react.useEffect)(function () {
      if ((0, _utils.ifAllTrue)([!forcedUpdateFetching, forcedUpdatePayload, appStateVisible === "active"])) {
        var _forcedUpdatePayload$, _forcedUpdatePayload$2;
        var forcedUpdateData = forcedUpdatePayload == null || (_forcedUpdatePayload$ = forcedUpdatePayload["forced-update"]) == null ? undefined : _forcedUpdatePayload$.find(function (item) {
          return item.platform === _reactNative.Platform.OS && item.fragmentName === `capp_forced_${_reactNative.Platform.OS}`;
        });
        var recommendedUpdateData = forcedUpdatePayload == null || (_forcedUpdatePayload$2 = forcedUpdatePayload["recommended"]) == null ? undefined : _forcedUpdatePayload$2.find(function (item) {
          return item.platform === _reactNative.Platform.OS && item.fragmentName === `capp_recommended_${_reactNative.Platform.OS}`;
        });
        // another flow to save and check appSettingVersion
        checkAppSettingsVersion(forcedUpdateData);
        if (forcedUpdateData) {
          var shouldDisplayForced = checkForcedCapp(forcedUpdateData);
          if (shouldDisplayForced) {
            return;
          }
        }
        if (recommendedUpdateData) {
          checkRecommenedCapp(recommendedUpdateData);
        } else {
          endCappUpdateFlow();
          (0, _storage.remove)(_storage.StorageKey.aemAppVersionUpdating);
        }
      } else if ((0, _utils.ifAllTrue)([!forcedUpdateFetching, !forcedUpdatePayload])) {
        checkAppSettingsVersion(null);
      }
    }, [forcedUpdatePayload, forcedUpdateFetching, appStateVisible]);
    var checkForcedCapp = function checkForcedCapp(forcedUpdateData) {
      var shouldDisplay = false;
      var compareVer = (0, _utils.compareVersion)(_reactNativeVersionInfo.default.appVersion, forcedUpdateData == null ? undefined : forcedUpdateData.targetVersion);
      if (compareVer < 0) {
        // VersionInfo.appVersion > forcedUpdateData?.version
        var information = {
          title: forcedUpdateData == null ? undefined : forcedUpdateData.title,
          description: forcedUpdateData == null ? undefined : forcedUpdateData.description,
          buttons: [{
            bold: true,
            text: forcedUpdateData == null ? undefined : forcedUpdateData.button1Label,
            onPress: function onPress() {
              return updateNow();
            }
          }]
        };
        showForcedUpdatePopup(information);
        shouldDisplay = true;
      }
      return shouldDisplay;
    };
    var checkRecommenedCapp = function checkRecommenedCapp(recommendedUpdateData) {
      var compareVer = (0, _utils.compareVersion)(_reactNativeVersionInfo.default.appVersion, recommendedUpdateData == null ? undefined : recommendedUpdateData.targetVersion);
      if (compareVer < 0) {
        var information = {
          title: recommendedUpdateData == null ? undefined : recommendedUpdateData.title,
          description: recommendedUpdateData == null ? undefined : recommendedUpdateData.description,
          buttons: [{
            text: recommendedUpdateData == null ? undefined : recommendedUpdateData.button1Label,
            style: 'cancel',
            onPress: function onPress() {
              return updateNow();
            }
          }, {
            text: recommendedUpdateData == null ? undefined : recommendedUpdateData.button2Label,
            onPress: function onPress() {
              return reminderAfterHours(information);
            }
          }]
        };
        showPopupWithCondition(information);
      } else {
        (0, _storage.remove)(_storage.StorageKey.aemAppVersionUpdating);
        endCappUpdateFlow();
      }
    };
    var showForcedUpdatePopup = function showForcedUpdatePopup(data) {
      if (isOpen.current) {
        return;
      }
      isOpen.current = true;
      console.log(`showForcedUpdatePopup: ${JSON.stringify(data)}`);
      _reactNative.Alert.alert(data == null ? undefined : data.title, data == null ? undefined : data.description, data == null ? undefined : data.buttons);
      dispatch(_systemRedux.default.forcedUpdateShow(true));
      forceUpdateHandling("CAPP");
    };
    var closeForcedUpdatePopup = function closeForcedUpdatePopup() {
      isOpen.current = false;
      dispatch(_systemRedux.default.forcedUpdateShow(false));
      endCappUpdateFlow();
    };
    var endCappUpdateFlow = function endCappUpdateFlow() {
      if (currentForceUpdateFlow.current === "CAPP") {
        forceUpdateHandling(null);
      }
      if (isPendingCpayUpdateCheck.current) {
        //case close CAPP popup
        openChangiPay();
      } else {
        setTimeout(function () {
          (0, _qualtrics.checkSurvey)((0, _envParams.env)(), !!currentForceUpdateFlow.current);
        }, 2000);
      }
    };
    var showPopupWithCondition = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (data) {
        var AemAppVersionUpdating = yield (0, _storage.load)(_storage.StorageKey.aemAppVersionUpdating);
        if ((AemAppVersionUpdating == null ? undefined : AemAppVersionUpdating.action) === ALERT_CHECK_VERSION_HANDLE.REMIND_ME_LATER) {
          var currentDate = (0, _momentTimezone.default)().tz("Asia/Singapore").format();
          var isAfter = (0, _momentTimezone.default)(currentDate).isAfter((0, _momentTimezone.default)(AemAppVersionUpdating == null ? undefined : AemAppVersionUpdating.expiredDate));
          var compare = (0, _utils.compareVersion)(_reactNativeVersionInfo.default.appVersion, AemAppVersionUpdating == null ? undefined : AemAppVersionUpdating.version);
          if ((0, _utils.ifAllTrue)([isAfter, compare < 0])) {
            // current date > expired date
            showForcedUpdatePopup(data);
          } else {
            closeForcedUpdatePopup();
          }
        } else {
          showForcedUpdatePopup(data);
        }
      });
      return function showPopupWithCondition(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    var updateNow = function updateNow() {
      isOpen.current = false;
      (0, _eventHelper.navigateToStore)();
    };
    var reminderAfterHours = function reminderAfterHours(data) {
      var _forcedUpdatePayload$3;
      if (reminderInterval) {
        clearTimeout(reminderInterval);
      }
      var forcedUpdateData = forcedUpdatePayload == null || (_forcedUpdatePayload$3 = forcedUpdatePayload["recommended"]) == null ? undefined : _forcedUpdatePayload$3.find(function (item) {
        return item.platform === _reactNative.Platform.OS && item.fragmentName === `capp_recommended_${_reactNative.Platform.OS}`;
      });
      var reminderH = (0, _utils.simpleCondition)({
        condition: forcedUpdateData == null ? undefined : forcedUpdateData.reminderInterval,
        ifValue: Number(forcedUpdateData == null ? undefined : forcedUpdateData.reminderInterval),
        elseValue: ReminderHours
      });
      var expiredDate = (0, _momentTimezone.default)().add(reminderH, "hours").tz("Asia/Singapore").format();
      var AemAppVersionUpdating = {
        action: ALERT_CHECK_VERSION_HANDLE.REMIND_ME_LATER,
        version: forcedUpdateData == null ? undefined : forcedUpdateData.version,
        expiredDate: expiredDate
      };
      (0, _storage.save)(_storage.StorageKey.aemAppVersionUpdating, AemAppVersionUpdating);
      reminderInterval = setTimeout(function () {
        showForcedUpdatePopup(data);
      }, reminderH * 60 * 60 * 1000);
      closeForcedUpdatePopup();
    };
    return null;
  };
