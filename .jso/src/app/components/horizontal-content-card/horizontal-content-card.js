  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.HorizontalContentCard = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _horizontalContentCard = _$$_REQUIRE(_dependencyMap[8]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var mainContainer = Object.assign({
    backgroundColor: _theme.color.palette.whiteGrey,
    borderRadius: 16
  }, _theme.shadow.secondaryShadow);
  var containerStyle = {
    flexDirection: "row"
  };
  var textContainer = {
    flex: 1,
    justifyContent: "center",
    marginRight: 16
  };
  var imageStyle = {
    marginRight: 16,
    marginTop: 8,
    marginLeft: 8,
    marginBottom: 8,
    alignSelf: "center"
  };
  var imageBackground = {
    width: 70,
    height: 70,
    borderRadius: 12
  };
  var titleStyle = {
    color: _theme.color.palette.almostBlackGrey
  };
  var textStyle = {
    color: _theme.color.palette.darkestGrey,
    marginTop: 3
  };
  var skeletonLayout = [{
    width: 165,
    height: 18,
    borderRadius: 4,
    marginTop: 23,
    marginBottom: 4
  }, {
    width: 63,
    height: 18,
    borderRadius: 4
  }];
  var loadingView = function loadingView() {
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: containerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: imageStyle,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lightGreyLoadingColors,
            shimmerStyle: imageBackground
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: skeletonLayout[0]
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: lighterGreyLoadingColors,
            shimmerStyle: skeletonLayout[1]
          })]
        })]
      })
    });
  };
  var defaultView = function defaultView(props) {
    var imageUrl = props.imageUrl,
      title = props.title,
      textVal = props.textVal,
      onPressed = props.onPressed,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "HorizontalContentCard" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "HorizontalContentCard" : _props$accessibilityL,
      _props$item = props.item,
      item = _props$item === undefined ? {} : _props$item;
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      onPress: function onPress() {
        return onPressed(item);
      },
      style: containerStyle,
      testID: `${testID}__TouchableHorizontalContentCard`,
      accessibilityLabel: `${accessibilityLabel}__TouchableHorizontalContentCard`,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: imageStyle,
        children: (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: imageUrl
          },
          style: imageBackground
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: textContainer,
        children: textVal ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 1,
            text: title,
            preset: "bodyTextBold",
            style: titleStyle
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 2,
            preset: "caption1Regular",
            text: textVal,
            style: textStyle
          })]
        }) : (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 2,
          text: title,
          preset: "bodyTextBold",
          style: titleStyle
        })
      })]
    });
  };
  var HorizontalContentCard = exports.HorizontalContentCard = function HorizontalContentCard(props) {
    var isLoading = props.type === _horizontalContentCard.HorizontalContentCardType.loading;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: mainContainer,
      children: isLoading ? loadingView() : defaultView(props)
    });
  };
