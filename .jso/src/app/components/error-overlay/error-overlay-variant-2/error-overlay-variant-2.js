  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorOverlayVariant2 = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _envParams = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _button = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _iconAlert = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ErrorOverlayVariant2 = exports.ErrorOverlayVariant2 = function ErrorOverlayVariant2(props) {
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      iconUri = _useState2[0],
      setIconUri = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isLoadingIcon = _useState4[0],
      setLoadingIcon = _useState4[1];
    var _useState5 = (0, _react.useState)({
        width: 0,
        height: 0,
        resizeMode: "contain"
      }),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      iconStyle = _useState6[0],
      setIconStyle = _useState6[1];
    var reload = props.reload,
      _props$title = props.title,
      title = _props$title === undefined ? (0, _i18n.translate)("errorOverlay.variant2.title") : _props$title,
      titleTx = props.titleTx,
      _props$message = props.message,
      message = _props$message === undefined ? (0, _i18n.translate)("errorOverlay.variant2.message") : _props$message,
      messageTx = props.messageTx,
      _props$reloadText = props.reloadText,
      reloadText = _props$reloadText === undefined ? (0, _i18n.translate)("errorOverlay.variant2.retry") : _props$reloadText,
      reloadTx = props.reloadTx,
      errorCode = props.errorCode,
      icon = props.icon,
      onReload = props.onReload,
      contentStyle = props.contentStyle,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ErrorOverlayVariant2" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ErrorOverlayVariant2" : _props$accessibilityL;
    (0, _react.useEffect)(function () {
      var isMounted = true;
      if (icon === "") {
        if (isMounted) {
          setLoadingIcon(true);
        }
      } else if (icon) {
        var _env, _env2;
        setIconUri(`${(_env = (0, _envParams.env)()) == null ? undefined : _env.AEM_URL}${icon}`);
        _reactNative.Image.getSize(`${(_env2 = (0, _envParams.env)()) == null ? undefined : _env2.AEM_URL}${icon}`, function (width, height) {
          var maxWidth = 191;
          var newWidth = width;
          var newHeight = height;
          var rate = width / height;
          if (width > maxWidth) {
            newWidth = maxWidth;
            newHeight = maxWidth / rate;
          }
          if (isMounted) {
            setIconStyle({
              width: newWidth,
              height: newHeight,
              resizeMode: "contain"
            });
          }
        });
        if (isMounted) {
          setLoadingIcon(false);
        }
      } else {
        if (isMounted) {
          setLoadingIcon(false);
        }
      }
      return function () {
        isMounted = false;
      };
    }, [icon]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [!isLoadingIcon && (icon && iconUri ? (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: iconUri
        },
        style: iconStyle
      }) : (0, _jsxRuntime.jsx)(_iconAlert.default, {})), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: [styles.textContainerStyle, contentStyle],
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          text: title,
          tx: titleTx
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.messageTextStyle,
          text: message,
          tx: messageTx
        }), errorCode && (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.errorCodeTextStyle,
          children: (0, _i18n.translate)("errorOverlay.variant2.errorCode", {
            errorCode: errorCode
          })
        })]
      }), reload && (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: styles.reloadButtonStyle,
        start: {
          x: 0,
          y: 1
        },
        end: {
          x: 1,
          y: 0
        },
        colors: styles.reloadButtonColors,
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          onPress: onReload,
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "primary",
          text: reloadText,
          tx: reloadTx,
          backgroundPreset: "light",
          statePreset: "default",
          testID: `${testID}__ButtonReloadOverlay2`,
          accessibilityLabel: `${accessibilityLabel}__ButtonReloadOverlay2`
        })
      })]
    });
  };
