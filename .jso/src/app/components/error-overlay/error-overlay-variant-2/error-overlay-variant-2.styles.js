  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.titleTextStyle = exports.textContainerStyle = exports.reloadButtonStyle = exports.reloadButtonColors = exports.messageTextStyle = exports.errorCodeTextStyle = exports.containerStyle = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var containerStyle = exports.containerStyle = {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 24
  };
  var textContainerStyle = exports.textContainerStyle = {
    marginBottom: 40,
    marginTop: 24
  };
  var titleTextStyle = exports.titleTextStyle = Object.assign({}, _text.presets.h2, {
    marginBottom: 16,
    textAlign: "center"
  });
  var messageTextStyle = exports.messageTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.darkestGrey,
    textAlign: "center",
    marginBottom: 16
  });
  var errorCodeTextStyle = exports.errorCodeTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.darkestGrey,
    textAlign: "center"
  });
  var reloadButtonStyle = exports.reloadButtonStyle = {
    borderRadius: 60,
    width: "100%"
  };
  var reloadButtonColors = exports.reloadButtonColors = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
