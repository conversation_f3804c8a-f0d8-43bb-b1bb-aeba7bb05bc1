  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorOverlayHeader = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var headerContainerStyle = {
    justifyContent: "center",
    position: "absolute",
    top: 0,
    left: 0,
    flex: 1,
    zIndex: 1000,
    width: "100%"
  };
  var headerContentStyle = {
    paddingHorizontal: 24,
    height: "100%",
    justifyContent: "center"
  };
  var headerContentShadowStyle = Object.assign({}, _reactNative2.Platform.select({
    android: {
      elevation: 5
    },
    ios: {
      shadowOpacity: 0.16,
      shadowRadius: 6,
      shadowOffset: {
        width: 0,
        height: 2
      }
    },
    default: {
      borderBottomWidth: _reactNative2.StyleSheet.hairlineWidth
    }
  }), {
    shadowColor: _theme.color.palette.almostBlackGrey
  });
  var headerStyle = {
    alignItems: "center",
    flexDirection: "row"
  };
  var headerLeftStyle = {
    width: 24
  };
  var headerRightStyle = {
    width: 24
  };
  var headerTextStyle = Object.assign({}, _text.presets.subTitleBold, {
    color: _theme.color.palette.almostBlackGrey,
    paddingHorizontal: 10,
    textAlign: "center",
    flex: 1
  });
  var getDefaultHeaderHeight = function getDefaultHeaderHeight(layout, statusBarHeight) {
    var headerHeight;
    var isLandscape = layout.width > layout.height;
    if (_reactNative2.Platform.OS === "ios") {
      if (_reactNative2.Platform.isPad) {
        headerHeight = 50;
      } else {
        if (isLandscape) {
          headerHeight = 32;
        } else {
          headerHeight = 44;
        }
      }
    } else if (_reactNative2.Platform.OS === "android") {
      headerHeight = 56;
    } else {
      headerHeight = 64;
    }
    return headerHeight + statusBarHeight;
  };
  var ErrorOverlayHeader = exports.ErrorOverlayHeader = function ErrorOverlayHeader(props) {
    var title = props.title,
      titleTx = props.titleTx,
      _props$headerBackgrou = props.headerBackgroundColor,
      headerBackgroundColor = _props$headerBackgrou === undefined ? "rgb(255, 255, 255)" : _props$headerBackgrou,
      onBack = props.onBack,
      containerStyle = props.containerStyle,
      customBackButton = props.customBackButton,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ErrorOverlayHeader" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ErrorOverlayHeader" : _props$accessibilityL,
      customHeaderContentStyle = props.headerContentStyle;
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var frame = (0, _reactNativeSafeAreaContext.useSafeAreaFrame)();
    var headerHeight = getDefaultHeaderHeight(frame, insets.top);
    var isTransparent = headerBackgroundColor === "transparent";
    var renderBackButton = customBackButton ? customBackButton : isTransparent ? (0, _jsxRuntime.jsx)(_icons.BackButton, {}) : (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {
      width: "24",
      height: "24"
    });
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: [headerContainerStyle, {
        height: headerHeight
      }, containerStyle],
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [headerContentStyle, {
          backgroundColor: headerBackgroundColor
        }, isTransparent ? null : headerContentShadowStyle, customHeaderContentStyle],
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: {
            height: insets.top
          }
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: headerStyle,
          children: [!isTransparent && (0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
            backgroundColor: headerBackgroundColor
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: headerLeftStyle,
            children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onBack,
              testID: `${testID}__Header`,
              accessibilityLabel: `${accessibilityLabel}__TouchableBack`,
              children: renderBackButton
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: title,
            tx: titleTx,
            style: headerTextStyle,
            numberOfLines: 1
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: headerRightStyle
          })]
        })]
      })
    });
  };
