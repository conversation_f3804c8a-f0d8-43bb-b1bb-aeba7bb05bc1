  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.storyModeStyle = exports.overlayStyle = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    height = _Dimensions$get.height;
  var overlayStyle = exports.overlayStyle = {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flex: 1,
    width: "100%",
    height: _reactNative.Platform.OS === "android" ? height + _reactNative.StatusBar.currentHeight : height,
    zIndex: 999,
    elevation: _reactNative.Platform.OS === "android" ? 50 : 0,
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var storyModeStyle = exports.storyModeStyle = {
    height: height * 0.8,
    backgroundColor: _theme.color.palette.lightestGrey
  };
