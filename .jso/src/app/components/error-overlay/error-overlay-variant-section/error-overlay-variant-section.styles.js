  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.titleTextStyle = exports.textContainerStyle = exports.subText = exports.subContainer = exports.reloadButtonStyle = exports.reloadButtonColors = exports.messageTextStyle = exports.containerStyle = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var containerStyle = exports.containerStyle = {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24
  };
  var textContainerStyle = exports.textContainerStyle = {
    maxWidth: 300,
    marginBottom: 24
  };
  var titleTextStyle = exports.titleTextStyle = Object.assign({}, _text.presets.h2, {
    marginBottom: 16,
    marginTop: 40,
    textAlign: "center"
  });
  var messageTextStyle = exports.messageTextStyle = Object.assign({}, _text.presets.caption1Regular, {
    color: _theme.color.palette.darkestGrey,
    textAlign: "center"
  });
  var reloadButtonStyle = exports.reloadButtonStyle = {
    borderRadius: 60,
    paddingHorizontal: 24
  };
  var reloadButtonColors = exports.reloadButtonColors = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var subContainer = exports.subContainer = {
    marginTop: 20
  };
  var subText = exports.subText = Object.assign({}, _text.presets.textLink, {
    textAlign: "center"
  });
