  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _errorOverlayVariantSection = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_errorOverlayVariantSection).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _errorOverlayVariantSection[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _errorOverlayVariantSection[key];
      }
    });
  });
  var _errorOverlayVariantSection2 = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_errorOverlayVariantSection2).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _errorOverlayVariantSection2[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _errorOverlayVariantSection2[key];
      }
    });
  });
