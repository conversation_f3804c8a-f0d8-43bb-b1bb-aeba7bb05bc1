  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorOverlayVariantSection = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _button = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _iconCloud = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ErrorOverlayVariantSection = exports.ErrorOverlayVariantSection = function ErrorOverlayVariantSection(props) {
    var _props$reload = props.reload,
      reload = _props$reload === undefined ? true : _props$reload,
      _props$title = props.title,
      title = _props$title === undefined ? (0, _i18n.translate)("errorOverlay.variantSection.title") : _props$title,
      titleTx = props.titleTx,
      _props$message = props.message,
      message = _props$message === undefined ? (0, _i18n.translate)("errorOverlay.variantSection.message") : _props$message,
      messageTx = props.messageTx,
      _props$reloadText = props.reloadText,
      reloadText = _props$reloadText === undefined ? (0, _i18n.translate)("errorOverlay.variantSection.reload") : _props$reloadText,
      reloadTx = props.reloadTx,
      onReload = props.onReload,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ErrorOverlayVariantSection" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ErrorOverlayVariantSection" : _props$accessibilityL,
      reloadButtonWidth = props.reloadButtonWidth;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_iconCloud.default, {}), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.textContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          text: title,
          tx: titleTx
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.messageTextStyle,
          text: message,
          tx: messageTx
        })]
      }), reload && (0, _jsxRuntime.jsx)(_reactNative.View, {
        children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: Object.assign({}, styles.reloadButtonStyle, {
            width: reloadButtonWidth ? reloadButtonWidth : 'auto'
          }),
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: styles.reloadButtonColors,
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            onPress: onReload,
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "primary",
            text: reloadText,
            tx: reloadTx,
            backgroundPreset: "light",
            statePreset: "default",
            testID: `${testID}__ButtonReloadOverlayVariantSection`,
            accessibilityLabel: `${accessibilityLabel}__ButtonReloadOverlayVariantSection`
          })
        })
      })]
    });
  };
