  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorOverlayNoConnection = undefined;
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _native = _$$_REQUIRE(_dependencyMap[4]);
  var _errorOverlayHeader = _$$_REQUIRE(_dependencyMap[5]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _errorOverlayVariant = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var _excluded = ["reload", "visible", "hideScreenHeader", "header", "headerBackgroundColor", "headerText", "headerTx", "storyMode", "onBack", "onReload", "customBackButton", "testID", "accessibilityLabel", "noInternetOverlayStyle", "headerContentStyle"];
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ErrorOverlayNoConnection = exports.ErrorOverlayNoConnection = function ErrorOverlayNoConnection(props) {
    var reload = props.reload,
      visible = props.visible,
      _props$hideScreenHead = props.hideScreenHeader,
      hideScreenHeader = _props$hideScreenHead === undefined ? true : _props$hideScreenHead,
      header = props.header,
      headerBackgroundColor = props.headerBackgroundColor,
      headerText = props.headerText,
      headerTx = props.headerTx,
      storyMode = props.storyMode,
      onBack = props.onBack,
      onReload = props.onReload,
      customBackButton = props.customBackButton,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ErrorOverlayNoConnection" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ErrorOverlayNoConnection" : _props$accessibilityL,
      noInternetOverlayStyle = props.noInternetOverlayStyle,
      headerContentStyle = props.headerContentStyle,
      rest = (0, _objectWithoutProperties2.default)(props, _excluded);
    var navigation = storyMode ? null : (0, _native.useNavigation)();
    var containerNoInternetOverlayStyle = storyMode ? styles.storyModeStyle : styles.overlayStyle;
    (0, _react.useLayoutEffect)(function () {
      if (hideScreenHeader) {
        navigation == null || navigation.setOptions({
          headerShown: !visible
        });
      }
    }, [visible, hideScreenHeader]);
    return visible ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: noInternetOverlayStyle || containerNoInternetOverlayStyle,
      children: [header && (0, _jsxRuntime.jsx)(_errorOverlayHeader.ErrorOverlayHeader, {
        title: headerText,
        titleTx: headerTx,
        headerBackgroundColor: headerBackgroundColor,
        onBack: onBack,
        customBackButton: customBackButton,
        headerContentStyle: headerContentStyle,
        testID: `${testID}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlayNoConnection`
      }), (0, _jsxRuntime.jsx)(_errorOverlayVariant.ErrorOverlayVariant3, Object.assign({}, rest, {
        reload: reload,
        onReload: onReload,
        testID: `${testID}__ErrorOverlayVariant3`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlayVariant3`
      }))]
    }) : null;
  };
