  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorUnplannedMaintenance = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _iconCloud = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _button = _$$_REQUIRE(_dependencyMap[10]);
  var _lodash = _$$_REQUIRE(_dependencyMap[11]);
  var _htmlRichtext = _$$_REQUIRE(_dependencyMap[12]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var reloadButtonColors = [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start];
  var ErrorUnplannedMaintenance = exports.ErrorUnplannedMaintenance = function ErrorUnplannedMaintenance(props) {
    var _props$header = props.header,
      header = _props$header === undefined ? "" : _props$header,
      _props$subHeader = props.subHeader,
      subHeader = _props$subHeader === undefined ? "" : _props$subHeader,
      icon = props.icon,
      _props$buttonLabel = props.buttonLabel,
      buttonLabel = _props$buttonLabel === undefined ? "" : _props$buttonLabel,
      _props$buttonLabel2 = props.buttonLabel2,
      buttonLabel2 = _props$buttonLabel2 === undefined ? "" : _props$buttonLabel2,
      onFirstButtonPress = props.onFirstButtonPress,
      onSecondButtonPress = props.onSecondButtonPress,
      testID = props.testID,
      accessibilityLabel = props.accessibilityLabel,
      errorContainerStyle = props.errorContainerStyle;
    var iconUri = (0, _utils.mappingUrlAem)(icon);
    var _useState = (0, _react.useState)({
        width: 0,
        height: 0
      }),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      iconStyle = _useState2[0],
      setIconStyle = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      getSizeStatus = _useState4[0],
      setGetSizeStatus = _useState4[1];
    (0, _react.useEffect)(function () {
      if (icon) {
        _reactNative2.Image.getSize(iconUri, function (width, height) {
          var maxWidth = 191;
          var newWidth = width;
          var newHeight = height;
          var rate = width / height;
          if (width > maxWidth) {
            newWidth = maxWidth;
            newHeight = maxWidth / rate;
          }
          setIconStyle({
            width: newWidth,
            height: newHeight
          });
          setGetSizeStatus(true);
        }, function () {
          return setGetSizeStatus(false);
        });
      }
    }, [iconUri]);
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: [styles.container, errorContainerStyle],
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.containerStyle,
        children: [icon && getSizeStatus ? (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: iconUri
          },
          style: iconStyle,
          resizeMode: "contain"
        }) : (0, _jsxRuntime.jsx)(_iconCloud.default, {}), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.textContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.titleTextStyle,
            text: header
          }), (0, _jsxRuntime.jsx)(_htmlRichtext.HtmlRichtext, {
            style: styles.messageTextStyle,
            value: `<p>${subHeader}</p>`
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.errorFooter,
          children: [!(0, _lodash.isEmpty)(buttonLabel) && (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: styles.reloadButtonStyle,
            start: {
              x: 1,
              y: 0
            },
            end: {
              x: 0,
              y: 1
            },
            colors: reloadButtonColors,
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              onPress: onFirstButtonPress,
              sizePreset: "large",
              textPreset: "buttonLarge",
              typePreset: "primary",
              text: buttonLabel,
              backgroundPreset: "light",
              statePreset: "default",
              testID: `${testID}__ButtonFirst`,
              accessibilityLabel: `${accessibilityLabel}__ButtonFirst`
            })
          }), !(0, _lodash.isEmpty)(buttonLabel2) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.button2Styles,
            onPress: onSecondButtonPress,
            testID: `${testID}__ButtonSecond`,
            accessibilityLabel: `${accessibilityLabel}__ButtonSecond`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.button2Text,
              text: buttonLabel2
            })
          })]
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    button2Styles: {
      marginTop: 25
    },
    button2Text: Object.assign({}, _text.presets.textLink, {
      textAlign: "center"
    }),
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    containerStyle: {
      alignItems: "center",
      flex: 1,
      justifyContent: "center",
      paddingHorizontal: 24
    },
    errorFooter: {
      width: "100%"
    },
    messageTextStyle: Object.assign({}, _text.presets.caption1Regular, {
      alignSelf: "center",
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center",
      width: "100%"
    }),
    reloadButtonStyle: {
      borderRadius: 60,
      paddingHorizontal: 24
    },
    textContainerStyle: {
      marginBottom: 24,
      width: "100%"
    },
    titleTextStyle: Object.assign({}, _text.presets.h2, {
      marginBottom: 16,
      marginTop: 40,
      textAlign: "center"
    })
  });
