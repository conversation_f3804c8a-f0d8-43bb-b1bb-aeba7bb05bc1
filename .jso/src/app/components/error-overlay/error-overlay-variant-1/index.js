  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _errorOverlayVariant = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_errorOverlayVariant).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _errorOverlayVariant[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _errorOverlayVariant[key];
      }
    });
  });
  var _errorOverlayVariant2 = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_errorOverlayVariant2).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _errorOverlayVariant2[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _errorOverlayVariant2[key];
      }
    });
  });
