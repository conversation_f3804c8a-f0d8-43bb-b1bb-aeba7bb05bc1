  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _backgrounds = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[8]);
  var _monarchOverlayControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[13]);
  var _styles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _maskedView = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _Monarch_BgSwirls = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _storage = _$$_REQUIRE(_dependencyMap[20]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[21]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[22]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[23]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("screen"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var IMAGE_HEIGHT = height * 0.783;
  var handleSubcopy = function handleSubcopy(subcopy, name) {
    if (!subcopy) return null;
    return subcopy == null ? undefined : subcopy.replace("@name", name);
  };
  var MonarchOnboardingOverlay = function MonarchOnboardingOverlay() {
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("MONARCH_OVERLAY"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      visible = _useState2[0],
      setVisible = _useState2[1];
    var monarchOverlayRef = (0, _react.useRef)(null);
    var monarchImageAnimated = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var viewMonarchButtonAnimated = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var opacityAnimated = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var contentMonarchOnboardingOverlay = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.monarchOnboardingOverlayPayload);
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      visibleLottie = _useState4[0],
      setVisibleLottie = _useState4[1];
    var arrAnimatedCard = [(0, _react.useRef)(new _reactNative2.Animated.Value(0)).current, (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current, (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current];
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var animationLottieRef = (0, _react.useRef)(null);
    var _ref = contentMonarchOnboardingOverlay || {},
      logo = _ref.logo,
      welcomeTitle = _ref.welcomeTitle,
      subcopy = _ref.subcopy,
      cardHeader = _ref.cardHeader,
      cards = _ref.cards,
      buttonLabel = _ref.buttonLabel,
      navigation = _ref.navigation,
      redirect = _ref.redirect;
    (0, _react.useEffect)(function () {
      _monarchOverlayControler.default.setModalRef(monarchOverlayRef);
    }, []);
    var show = function show() {
      return setVisible(true);
    };
    var hide = function hide() {
      return setVisible(false);
    };
    (0, _react.useImperativeHandle)(monarchOverlayRef, function () {
      return {
        showOverlay: show,
        hideOverlay: hide
      };
    });
    var storeShowMonarchOverlay = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var store = yield (0, _storage.load)(_storage.StorageKey.monarchOveylayUserID);
        if ((0, _lodash.isEmpty)(store)) {
          (0, _storage.save)(_storage.StorageKey.monarchOveylayUserID, [profilePayload.id]);
        } else {
          (0, _storage.save)(_storage.StorageKey.monarchOveylayUserID, [].concat((0, _toConsumableArray2.default)(store), [profilePayload.id]));
        }
      });
      return function storeShowMonarchOverlay() {
        return _ref2.apply(this, arguments);
      };
    }();
    var runLottieAnimation = function runLottieAnimation() {
      return setVisibleLottie(true);
    };
    var startAnimationProcess = function startAnimationProcess() {
      storeShowMonarchOverlay();
      _reactNative2.Animated.parallel([_reactNative2.Animated.timing(monarchImageAnimated, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true
      }), _reactNative2.Animated.timing(viewMonarchButtonAnimated, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true
      }), _reactNative2.Animated.timing(opacityAnimated, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true
      }), _reactNative2.Animated.sequence([_reactNative2.Animated.delay(1000), _reactNative2.Animated.timing(arrAnimatedCard[0], {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }), _reactNative2.Animated.timing(arrAnimatedCard[1], {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }), _reactNative2.Animated.timing(arrAnimatedCard[2], {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      })])]).start(function () {
        return runLottieAnimation();
      });
    };
    var resetAnimation = function resetAnimation() {
      setVisibleLottie(false);
      opacityAnimated.setValue(0);
      monarchImageAnimated.setValue(0);
      viewMonarchButtonAnimated.setValue(0);
      arrAnimatedCard.forEach(function (e) {
        return e.setValue(0);
      });
    };
    var interpolatedMonarchValue = monarchImageAnimated.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 20],
      extrapolate: "clamp"
    });
    var interpolatedViewMonarchButtonValue = viewMonarchButtonAnimated.interpolate({
      inputRange: [0, 1],
      outputRange: [0, -10],
      extrapolate: "clamp"
    });
    var handlePress = function handlePress() {
      var _ref3 = navigation || "",
        type = _ref3.type,
        value = _ref3.value;
      if (!type || !value) return null;
      hide();
      handleNavigation(type, value, redirect);
    };
    return (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
      isVisible: visible,
      swipeDirection: "down",
      animationIn: "fadeIn",
      animationOut: "fadeOut",
      animationInTiming: 1000,
      animationOutTiming: 200,
      style: {
        margin: 0,
        justifyContent: "flex-end"
      },
      onSwipeComplete: hide,
      onBackdropPress: hide,
      backdropTransitionOutTiming: 0,
      onModalShow: startAnimationProcess,
      onModalHide: resetAnimation,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.ImageBackground, {
        source: _backgrounds.MonarchOverlay,
        style: [{
          width: width,
          height: IMAGE_HEIGHT
        }],
        children: [visibleLottie && (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          style: _styles.default.lottieStyle,
          source: _Monarch_BgSwirls.default,
          ref: animationLottieRef,
          loop: false,
          autoPlay: true
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: {
            position: "absolute",
            top: 65,
            right: 16
          },
          onPress: hide,
          children: (0, _jsxRuntime.jsx)(_icons.CrossWhite, {})
        }), !(0, _lodash.isEmpty)(logo) && (0, _jsxRuntime.jsx)(_reactNative2.Animated.Image, {
          source: {
            uri: (0, _mediaHelper.handleImageUrl)(logo)
          },
          style: [_styles.default.logo, {
            opacity: opacityAnimated,
            transform: [{
              translateY: interpolatedMonarchValue
            }]
          }]
        }), !(0, _lodash.isEmpty)(welcomeTitle) && (0, _jsxRuntime.jsx)(_reactNative2.Animated.Text, {
          style: [_styles.default.welcomeTitle, {
            opacity: opacityAnimated
          }],
          children: welcomeTitle
        }), !(0, _lodash.isEmpty)(subcopy) && (0, _jsxRuntime.jsx)(_reactNative2.Animated.Text, {
          style: [_styles.default.subcopy, {
            opacity: opacityAnimated
          }],
          children: handleSubcopy(subcopy, profilePayload == null ? undefined : profilePayload.firstName)
        }), (0, _jsxRuntime.jsxs)(_reactNative2.Animated.View, {
          style: [_styles.default.wrapPrivilegeSections, {
            opacity: opacityAnimated
          }],
          children: [!(0, _lodash.isEmpty)(cardHeader) && (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: _styles.default.cardHeader,
            children: cardHeader
          }), !(0, _lodash.isEmpty)(cards) && (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            data: cards,
            horizontal: true,
            style: _styles.default.wrapCardComponentStyle,
            renderItem: function renderItem(_ref4) {
              var item = _ref4.item,
                index = _ref4.index;
              return (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
                style: [_styles.default.wrapCardViewComponentStyle, {
                  opacity: arrAnimatedCard[index]
                }],
                children: (0, _jsxRuntime.jsxs)(_reactNative2.ImageBackground, {
                  style: _styles.default.cardComponentStyle,
                  source: _$$_REQUIRE(_dependencyMap[24]),
                  children: [(0, _jsxRuntime.jsx)(_reactNative2.Image, {
                    source: {
                      uri: (0, _mediaHelper.handleImageUrl)(item == null ? undefined : item.cardIcon)
                    },
                    style: _styles.default.imageCardStyle,
                    resizeMode: "contain"
                  }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
                    style: _styles.default.cardTitle,
                    children: item == null ? undefined : item.cardTitle
                  })]
                })
              });
            },
            keyExtractor: function keyExtractor(_, index) {
              return index.toString();
            },
            ItemSeparatorComponent: function ItemSeparatorComponent() {
              return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _styles.default.separatorItem
              });
            }
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          activeOpacity: 0.5,
          onPress: handlePress,
          style: {
            marginTop: 32
          },
          children: (0, _jsxRuntime.jsx)(_reactNative2.Animated.View, {
            style: {
              opacity: opacityAnimated,
              transform: [{
                translateY: interpolatedViewMonarchButtonValue
              }]
            },
            children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              colors: ["rgba(255, 225, 181, 0.32)", "#FFE280", "rgba(255, 225, 181, 0.32)"],
              start: {
                x: 0,
                y: 0
              },
              end: {
                x: 1,
                y: 1
              },
              locations: [0.4, 0.45, 0.8, 1],
              accessible: false,
              style: _styles.default.buttonViewAll,
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _styles.default.viewContentButtonLabel,
                children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
                  style: _styles.default.buttonLabel,
                  children: buttonLabel
                })
              })
            })
          })
        })]
      })
    });
  };
  var _default = exports.default = (0, _react.forwardRef)(MonarchOnboardingOverlay);
