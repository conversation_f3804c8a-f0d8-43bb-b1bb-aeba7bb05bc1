  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[2]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var _htmlContent = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNativeRenderHtml = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _l1Advisory = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeReanimated = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var L1AdvisoryCard = function L1AdvisoryCard(props) {
    var _data$extraJsonData;
    var data = props.data,
      onClose = props.onClose,
      onLayout = props.onLayout,
      onPressFirstButton = props.onPressFirstButton,
      onReadMore = props.onReadMore,
      overrideStyle = props.style;
    var _ref = (_data$extraJsonData = data == null ? undefined : data.extraJsonData) != null ? _data$extraJsonData : {},
      enableReadMore = _ref.enableReadMore,
      icon = _ref.icon,
      labelFirst = _ref.labelFirst,
      navigationFirst = _ref.navigationFirst,
      shortDescription = _ref.shortDescription;
    return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
      onLayout: onLayout,
      style: [styles.containerStyle, overrideStyle],
      children: [(0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        accessibilityLabel: `${_l1Advisory.ComponentName.L1AdvisoryCard}_CloseBtn`,
        onPress: onClose,
        style: styles.closeBtnStyle,
        testID: `${_l1Advisory.ComponentName.L1AdvisoryCard}_CloseBtn`,
        children: (0, _jsxRuntime.jsx)(_icons.Cross, {
          color: _theme.color.palette.darkestGrey,
          height: 24,
          width: 24
        })
      }), icon && (0, _jsxRuntime.jsx)(_baseImage.default, {
        accessibilityLabel: `${_l1Advisory.ComponentName.L1AdvisoryCard}_Icon`,
        source: {
          uri: (0, _utils.mappingUrlAem)(icon)
        },
        style: styles.iconContainerStyle,
        testID: `${_l1Advisory.ComponentName.L1AdvisoryCard}_Icon`
      }), (data == null ? undefined : data.title) && (0, _jsxRuntime.jsx)(_text.Text, {
        accessibilityLabel: `${_l1Advisory.ComponentName.L1AdvisoryCard}_Title`,
        style: styles.titleTextStyle,
        testID: `${_l1Advisory.ComponentName.L1AdvisoryCard}_Title`,
        text: data == null ? undefined : data.title
      }), shortDescription && (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
        source: {
          html: (0, _htmlContent.formatHtmlContent)(shortDescription)
        },
        contentWidth: width - 88,
        tagsStyles: tagStyle,
        systemFonts: systemFonts
      }), enableReadMore && (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        onPress: onReadMore,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: Object.assign({}, _text.newPresets.bodyTextBold, {
            color: _theme.color.palette.lightPurple
          }),
          tx: "common.readMore"
        })
      }), (navigationFirst == null ? undefined : navigationFirst.value) && (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        end: {
          x: 1,
          y: 0
        },
        start: {
          x: 0,
          y: 1
        },
        style: styles.firstBtnContainerStyle,
        children: (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          androidRippleColor: "transparent",
          accessibilityLabel: `${_l1Advisory.ComponentName.L1AdvisoryCard}_FirstBtn`,
          onPress: onPressFirstButton,
          testID: `${_l1Advisory.ComponentName.L1AdvisoryCard}_FirstBtn`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.firstBtnLabelTextStyle,
            text: labelFirst
          })
        })
      })]
    });
  };
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var styles = _reactNative.StyleSheet.create({
    containerStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      gap: 12,
      paddingHorizontal: 20,
      paddingVertical: 40
    }, _theme.shadow.primaryShadow),
    closeBtnStyle: {
      position: "absolute",
      right: 12,
      top: 12
    },
    iconContainerStyle: {
      height: 48,
      width: 48
    },
    titleTextStyle: Object.assign({}, _text.newPresets.bold, {
      fontSize: 20,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 28
    }),
    firstBtnContainerStyle: {
      borderRadius: 60,
      height: 44,
      marginTop: 12,
      paddingHorizontal: 24,
      paddingVertical: 10,
      alignSelf: "flex-start"
    },
    firstBtnLabelTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      lineHeight: 24
    }),
    readMoreTextStyle: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: _theme.color.palette.lightPurple
    })
  });
  var tagStyle = {
    ul: Object.assign({}, _text.newPresets.bodyTextRegular, {
      paddingLeft: 11,
      color: _theme.color.palette.darkestGrey
    }),
    ol: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: _theme.color.palette.darkestGrey
    }),
    li: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: _theme.color.palette.darkestGrey
    }),
    p: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: _theme.color.palette.darkestGrey
    })
  };
  var systemFonts = [].concat((0, _toConsumableArray2.default)(_reactNativeRenderHtml.defaultSystemFonts), ["Lato-Regular", "Lato-Bold"]);
  var _default = exports.default = L1AdvisoryCard;
