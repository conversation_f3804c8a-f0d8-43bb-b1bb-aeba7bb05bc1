  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.AnimatedText = undefined;
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _possibleConstructorReturn2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _getPrototypeOf2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _inherits2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var Animatable = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
  var container = {
    alignItems: "center",
    justifyContent: "center"
  };
  var animatedView = {
    backfaceVisibility: "hidden",
    alignItems: "center",
    justifyContent: "center"
  };
  var animatedTextStyle = {
    fontFamily: _theme.typography.regular,
    fontSize: 14,
    lineHeight: 18,
    textAlign: "left",
    textAlignVertical: "top",
    fontStyle: "normal",
    fontWeight: "400",
    letterSpacing: 0,
    color: _theme.color.palette.darkestGrey
  };
  var AnimatedText = exports.AnimatedText = /*#__PURE__*/function (_PureComponent) {
    function AnimatedText(props) {
      var _this;
      (0, _classCallCheck2.default)(this, AnimatedText);
      _this = _callSuper(this, AnimatedText, [props]);
      _this.state = {
        index: 0,
        items: [],
        isFlipped: false
      };
      _this.flipText = function () {
        _reactNative.Animated.spring(_this.animatedValue, {
          toValue: _this.state.isFlipped ? 0 : 360,
          friction: 18,
          tension: 50,
          delay: 0,
          useNativeDriver: true
        }).start(function () {
          setTimeout(function () {
            if (_this.state.index === _this.state.items.length - 1) {
              _this.setState({
                index: 0,
                isFlipped: !_this.state.isFlipped
              });
            } else {
              _this.setState({
                index: _this.state.index + 1,
                isFlipped: !_this.state.isFlipped
              });
            }
            _this.flipText();
          }, _this.props.delay);
        });
      };
      _this.animatedValue = new _reactNative.Animated.Value(0);
      _this.value = 0;
      _this.animatedValue.addListener(function (_ref) {
        var value = _ref.value;
        _this.value = value;
      });
      _this.frontInterpolate = _this.animatedValue.interpolate({
        inputRange: [0, 180],
        outputRange: ["0deg", "180deg"]
      });
      return _this;
    }
    (0, _inherits2.default)(AnimatedText, _PureComponent);
    return (0, _createClass2.default)(AnimatedText, [{
      key: "componentDidMount",
      value: function componentDidMount() {
        if (this.props.codeShares && this.props.codeShares.length > 1) {
          this.flipText();
        }
        this.setState({
          items: this.props.codeShares
        });
      }
    }, {
      key: "componentWillUnmount",
      value: function componentWillUnmount() {
        this.setState = function (_state, _callback) {
          return null;
        };
      }
    }, {
      key: "render",
      value: function render() {
        var _this2 = this;
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: container,
          children: this.state.items[this.state.index] && (0, _jsxRuntime.jsx)(Animatable.View, {
            onAnimationBegin: function onAnimationBegin() {
              if (_this2.state.index < _this2.state.items.length - 1) {
                _this2.setState({
                  index: _this2.state.index + 1
                });
              }
            },
            style: Object.assign({}, animatedView, {
              transform: [{
                rotateX: this.frontInterpolate
              }]
            }),
            children: (0, _jsxRuntime.jsx)(_reactNative.Animated.Text, {
              style: animatedTextStyle,
              allowFontScaling: false,
              children: this.state.items[this.state.index]
            })
          })
        });
      }
    }], [{
      key: "getDerivedStateFromProps",
      value: function getDerivedStateFromProps(nextProps, prevState) {
        if (nextProps.codeShares !== prevState.items) {
          return {
            items: nextProps.codeShares,
            isFlipped: prevState.isFlipped
          };
        }
        return null;
      }
    }]);
  }(_react.PureComponent);
