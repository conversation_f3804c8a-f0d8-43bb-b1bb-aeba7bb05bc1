  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Illustration = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _animations = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var Illustration = exports.Illustration = function Illustration(props) {
    var animationRef = _react.default.useRef(null);
    var loop = props.loop,
      progress = props.progress,
      style = props.style,
      source = props.source,
      autoPlay = props.autoPlay;
    return (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
      ref: animationRef,
      loop: loop,
      progress: progress,
      style: style,
      source: _animations.animations[source],
      autoPlay: autoPlay
    });
  };
